import React from 'react';
import type { Preview } from '@storybook/react';
import '@repo/ui/globals.css';
import '@/styles/web.css';
import PopupProvider from '../components/ui/popup/popup.provider';

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
  decorators: [
    Story => (
      <PopupProvider>
        <Story />
      </PopupProvider>
    ),
  ],
};

export default preview;
