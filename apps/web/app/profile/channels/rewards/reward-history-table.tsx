'use client';

import SvgIcon from '@/components/icons/svg-icon';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/ui/base.select';
import { useChannelRewardsHistory } from '@/hooks/query/channel/use-channel-rewards-history';
import type { ChannelRewardsHistory, RewardsOrder } from '@/lib/api/channel/channel.schema.server';
import { RewardHistoryTransformed } from '@/lib/api/channel/channel.transform';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/ui/components/table';
import {
  ColumnDef,
  flexRender,
  getCoreRowModel,
  getSortedRowModel,
  SortingState,
  useReactTable,
} from '@tanstack/react-table';
import * as React from 'react';
import { useState } from 'react';

type SortOption = RewardsOrder;

const sortOptions = [
  { value: 'newest', label: 'Latest' },
  { value: 'value', label: 'Reward' },
] as const;

export function RewardHistoryTable() {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [sortBy, setSortBy] = useState<SortOption>('NEWEST');

  const {
    data: rewardsHistoryData,
    isLoading,
    error,
  } = useChannelRewardsHistory({
    order: sortBy,
  });

  const columns = React.useMemo<ColumnDef<RewardHistoryTransformed>[]>(
    () => [
      {
        accessorKey: 'timestamp',
        header: 'Time',
        cell: ({ row }) => (
          <div className="text-mid-dark text-size-xs gap-space-6 flex items-center font-semibold">
            <SvgIcon name="ClockIcon" />
            {row.original.timestamp}
          </div>
        ),
      },
      {
        accessorKey: 'amount',
        header: 'Reward',
        cell: ({ row }) => (
          <div className="text-mid-dark text-size-sm font-semibold">${row.original.amount}</div>
        ),
      },
      {
        accessorKey: 'status',
        header: 'Claim Status',
        cell: ({ row }) => (
          <div className="text-mid-dark text-size-sm font-semibold">{row.original.status}</div>
        ),
      },
      {
        accessorKey: 'claimedAt',
        header: 'Claim Date',
        cell: ({ row }) => (
          <div className="text-mid-dark text-size-sm font-semibold">
            {row.original.claimedAt || '-'}
          </div>
        ),
      },
    ],
    []
  );

  const tableData = rewardsHistoryData?.histories || [];

  const table = useReactTable({
    data: tableData,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    state: {
      sorting,
    },
  });

  if (isLoading) {
    return (
      <div className="w-full">
        <div className="mb-4 flex justify-start">
          <div className="flex items-center gap-2">
            <BaseSelect value={sortBy} onValueChange={v => setSortBy(v as SortOption)} disabled>
              <BaseSelectTrigger className="h-8 w-[150px]" size="sm">
                <BaseSelectValue />
              </BaseSelectTrigger>
              <BaseSelectContent>
                {sortOptions.map(option => (
                  <BaseSelectItem key={option.value} value={option.value}>
                    {option.label}
                  </BaseSelectItem>
                ))}
              </BaseSelectContent>
            </BaseSelect>
          </div>
        </div>
        <div className="flex h-24 items-center justify-center">
          <div className="text-gray-3">Loading...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full">
        <div className="mb-4 flex justify-start">
          <div className="flex items-center gap-2">
            <BaseSelect value={sortBy} onValueChange={v => setSortBy(v as SortOption)}>
              <BaseSelectTrigger className="h-8 w-[150px]" size="sm">
                <BaseSelectValue />
              </BaseSelectTrigger>
              <BaseSelectContent>
                {sortOptions.map(option => (
                  <BaseSelectItem key={option.value} value={option.value}>
                    {option.label}
                  </BaseSelectItem>
                ))}
              </BaseSelectContent>
            </BaseSelect>
          </div>
        </div>
        <div className="flex h-24 items-center justify-center">
          <div className="text-red-500">Error loading rewards history</div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div>
        <div className="mb-4 flex justify-start">
          <div className="flex items-center gap-2">
            <BaseSelect value={sortBy} onValueChange={v => setSortBy(v as SortOption)}>
              <BaseSelectTrigger className="h-8 w-[150px]" size="sm">
                <BaseSelectValue />
              </BaseSelectTrigger>
              <BaseSelectContent>
                {sortOptions.map(option => (
                  <BaseSelectItem key={option.value} value={option.value}>
                    {option.label}
                  </BaseSelectItem>
                ))}
              </BaseSelectContent>
            </BaseSelect>
          </div>
        </div>
        <Table>
          <TableHeader className="h-[60px]">
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id} className="border-b-line border-b-0">
                {headerGroup.headers.map(header => (
                  <TableHead className="text-size-sm text-gray-3 font-semibold" key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow key={row.id} className="border-0">
                  {row.getVisibleCells().map(cell => (
                    <TableCell className="py-space-30" key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow className="border-0">
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
