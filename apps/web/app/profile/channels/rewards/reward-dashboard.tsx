'use client';

import { DashboardCard } from '@/components/ui/dashboard-card';
import { BaseButton } from '@/components/ui/base.button';
import SvgIcon from '@/components/icons/svg-icon';
import MarketCountdown from '@/components/common/market-countdown';
import { useChannelRewards } from '@/hooks/query/channel/use-channel-rewards';
import MarketClaimButton from '@/components/actions/market-claim-button';

function getNextMondayUTC(): number {
  const now = new Date();
  const currentUTC = new Date(now.getTime() + now.getTimezoneOffset() * 60000);

  // 현재 요일 (0: 일요일, 1: 월요일, ..., 6: 토요일)
  const currentDay = currentUTC.getUTCDay();

  // 다음 월요일까지의 일수 계산
  const daysUntilMonday = currentDay === 0 ? 1 : (8 - currentDay) % 7 || 7;

  // 다음 월요일 UTC 0시 계산
  const nextMonday = new Date(currentUTC);
  nextMonday.setUTCDate(currentUTC.getUTCDate() + daysUntilMonday);
  nextMonday.setUTCHours(0, 0, 0, 0);

  return nextMonday.getTime();
}

export function RewardDashboard() {
  const { data: rewards, isLoading, error } = useChannelRewards();

  if (isLoading) {
    return (
      <section className="gap-space-30 pb-space-50 border-b-line flex flex-col border-b">
        <p className="text-size-sm text-gray-3">Rewards Earned from Predictions Made on Channel.</p>
        <div className="gap-space-60 flex">
          <DashboardCard className="flex-1" variant="info">
            <div className="text-size-sm font-semibold">Total Rewards</div>
            <div className="text-size-2xl font-bold">Loading...</div>
          </DashboardCard>
          <DashboardCard className="border-sky flex-1 border" variant="neutral">
            <div className="flex justify-between">
              <div className="text-size-sm font-semibold">Claim Rewards</div>
              <div className="gap-space-8 flex items-center">
                <span className="gap-space-8 text-size-xs text-gray-3 flex items-center">
                  <SvgIcon name="ClockIcon" />
                  Rewards update time
                </span>
                <MarketCountdown showFull endTime={getNextMondayUTC()} />
              </div>
            </div>
            <div className="pl-space-10 flex justify-between">
              <span className="text-size-xs text-mid-dark font-semibold">Claimable</span>
              <div className="gap-space-30 flex items-center">
                <div className="text-size-2xl font-bold">Loading...</div>
                <BaseButton className="w-[160px] max-w-full" variant="info" disabled>
                  Claim
                </BaseButton>
              </div>
            </div>
          </DashboardCard>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="gap-space-30 pb-space-50 border-b-line flex flex-col border-b">
        <p className="text-size-sm text-gray-3">Rewards Earned from Predictions Made on Channel.</p>
        <div className="gap-space-60 flex">
          <DashboardCard className="flex-1" variant="info">
            <div className="text-size-sm font-semibold">Total Rewards</div>
            <div className="text-size-2xl font-bold text-red-500">Error loading data</div>
          </DashboardCard>
          <DashboardCard className="border-sky flex-1 border" variant="neutral">
            <div className="flex justify-between">
              <div className="text-size-sm font-semibold">Claim Rewards</div>
              <div className="gap-space-8 flex items-center">
                <span className="gap-space-8 text-size-xs text-gray-3 flex items-center">
                  <SvgIcon name="ClockIcon" />
                  Rewards update time
                </span>
                <MarketCountdown showFull endTime={getNextMondayUTC()} />
              </div>
            </div>
            <div className="pl-space-10 flex justify-between">
              <span className="text-size-xs text-mid-dark font-semibold">Claimable</span>
              <div className="gap-space-30 flex items-center">
                <div className="text-size-2xl font-bold text-red-500">Error</div>
                <BaseButton className="w-[160px] max-w-full" variant="info" disabled>
                  Claim
                </BaseButton>
              </div>
            </div>
          </DashboardCard>
        </div>
      </section>
    );
  }

  return (
    <section className="gap-space-30 pb-space-50 border-b-line flex flex-col border-b">
      <p className="text-size-sm text-gray-3">Rewards Earned from Predictions Made on Channel.</p>
      <div className="gap-space-60 flex">
        <DashboardCard className="flex-1" variant="info">
          <div className="text-size-sm font-semibold">Total Rewards</div>
          <div className="text-size-2xl font-bold">${rewards?.formattedTotalRewards || '0.00'}</div>
        </DashboardCard>
        <DashboardCard className="border-sky flex-1 border" variant="neutral">
          <div className="flex justify-between">
            <div className="text-size-sm font-semibold">Claim Rewards</div>
            <div className="gap-space-8 flex items-center">
              <span className="gap-space-8 text-size-xs text-gray-3 flex items-center">
                <SvgIcon name="ClockIcon" />
                Rewards update time
              </span>
              <MarketCountdown showFull endTime={getNextMondayUTC()} />
            </div>
          </div>
          <div className="pl-space-10 flex justify-between">
            <span className="text-size-xs text-mid-dark font-semibold">Claimable</span>
            <div className="gap-space-30 flex items-center">
              <div className="text-size-2xl font-bold">
                ${rewards?.formattedClaimableRewards || '0.00'}
              </div>
              <MarketClaimButton className="w-[160px] max-w-full" />
            </div>
          </div>
        </DashboardCard>
      </div>
    </section>
  );
}
