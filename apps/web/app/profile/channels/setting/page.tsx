'use client';

import PageContainer from '@/components/layouts/page-container';
import ChannelSetting from '@/components/forms/ChannelSetting';
import { useMyChannel } from '@/hooks/query/channel';

export default function ChannelsSettingPage() {
  const { data: channelInfo, isLoading, error } = useMyChannel();

  if (isLoading) {
    return (
      <PageContainer title="Edit Channel">
        <div className="flex items-center justify-center p-8">
          <div className="text-gray-500">Loading channel information...</div>
        </div>
      </PageContainer>
    );
  }

  if (error) {
    console.log(error);
    return (
      <PageContainer title="Edit Channel">
        <div className="flex items-center justify-center p-8">
          <div className="text-red-500">Failed to load channel information</div>
        </div>
      </PageContainer>
    );
  }

  // Prepare initial data for the form
  const initialChannelData = {
    name: channelInfo?.name || '',
    description: channelInfo?.description || '',
    imageUrl: channelInfo?.imageUrl || '',
    bannerUrl: channelInfo?.bannerUrl || '',
    channelSns: channelInfo?.channelSns || [],
  };

  return (
    <PageContainer title="Edit Channel">
      <ChannelSetting initialData={initialChannelData} />
    </PageContainer>
  );
}
