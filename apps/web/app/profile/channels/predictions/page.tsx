'use client';

import {
  BaseTabs,
  BaseTabsList,
  BaseTabsTrigger,
  BaseTabsContent,
} from '@/components/ui/base.tabs';
import PageContainer from '@/components/layouts/page-container';
import ActivePredictionsTable from './active-table';
import PredictionHistoryTable from './history-table';

export default function ChannelPredictionsPage() {
  return (
    <PageContainer title="Predictions">
      <BaseTabs defaultValue="active">
        <div className="border-b-line border-b">
          <BaseTabsList className="border-none">
            <BaseTabsTrigger className="min-w-(--tab-trigger-width)" value="active">
              Active
            </BaseTabsTrigger>
            <BaseTabsTrigger className="min-w-(--tab-trigger-width)" value="history">
              History
            </BaseTabsTrigger>
          </BaseTabsList>
        </div>
        <BaseTabsContent value="active" className="mt-6">
          <ActivePredictionsTable />
        </BaseTabsContent>
        <BaseTabsContent value="history" className="mt-6">
          <PredictionHistoryTable />
        </BaseTabsContent>
      </BaseTabs>
    </PageContainer>
  );
}
