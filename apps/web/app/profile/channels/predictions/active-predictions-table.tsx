'use client';

import CommonAvatar from '@/components/ui/avatar-image';
import { useActivePredictions } from '@/hooks/query/channel/use-active-predictions';
import type { ActivePredictionTransformed } from '@/lib/api/channel/channel.transform';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/ui/components/table';
import { Skeleton } from '@repo/ui/components/skeleton';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import * as React from 'react';
import AddMarketDepositButton from '@/components/actions/add-market-deposit-button';
import ProposeAnswerButton from '@/components/actions/propose-answer-button';
import Link from 'next/link';
import { INNER_LINKS } from '@/lib/constants';

// 스켈레톤 테이블 로딩 컴포넌트
function ActivePredictionsTableSkeleton() {
  const skeletonRows = Array.from({ length: 5 });

  return (
    <div className="w-full">
      <div className="p-4">
        <Table>
          <TableHeader>
            <TableRow className="border-b-line border-b-0">
              <TableHead className="text-size-sm text-gray-3 font-semibold">Prediction</TableHead>
              <TableHead className="text-size-sm text-gray-3 font-semibold">Total Volume</TableHead>
              <TableHead className="text-size-sm text-gray-3 font-semibold">Deposit</TableHead>
              <TableHead className="text-size-sm text-gray-3 font-semibold">Status</TableHead>
              <TableHead className="text-size-sm text-gray-3 font-semibold"></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {skeletonRows.map((_, index) => (
              <TableRow key={index} className="border-0">
                {/* Prediction 열 */}
                <TableCell>
                  <div className="gap-space-20 flex items-center">
                    <Skeleton className="size-12 rounded-full" />
                    <div className="flex flex-col gap-1">
                      <Skeleton className="h-4 w-48" />
                      <Skeleton className="h-3 w-32" />
                    </div>
                  </div>
                </TableCell>

                {/* Total Volume 열 */}
                <TableCell>
                  <Skeleton className="h-4 w-20" />
                </TableCell>

                {/* Deposit 열 */}
                <TableCell>
                  <Skeleton className="h-4 w-16" />
                </TableCell>

                {/* Status 열 */}
                <TableCell>
                  <Skeleton className="h-4 w-24" />
                </TableCell>

                {/* Actions 열 */}
                <TableCell>
                  <Skeleton className="h-8 w-[140px] rounded" />
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}

const createColumns = (refetch: () => void): ColumnDef<ActivePredictionTransformed>[] => [
  {
    accessorKey: 'title',
    header: 'Prediction',
    cell: ({ row }) => {
      const prediction = row.original;
      return (
        <Link href={INNER_LINKS.MAIN.MARKETS.DETAIL(prediction.id)}>
          <div className="gap-space-20 flex items-center">
            <CommonAvatar size="md2" imageUrl={prediction.imageUrl} />
            <div className="flex flex-col">
              <div className="text-mid-dark text-size-sm font-semibold">{prediction.title}</div>
              <div className="gap-space-10 flex items-center">
                <div className="text-size-xs gap-space-6 flex items-center">
                  {/* TODOL add createdAt */}
                  {/* <span className="text-mid-dark font-semibold">{prediction.totalVolume}</span> */}
                  {/* <span>Vol.</span> */}
                </div>
              </div>
            </div>
          </div>
        </Link>
      );
    },
  },
  {
    accessorKey: 'totalVolume',
    header: 'Total Volume',
    cell: ({ row }) => (
      <div className="text-mid-dark text-size-sm font-semibold">
        {row.original.formattedTotalVolume}
      </div>
    ),
  },
  {
    accessorKey: 'deposit',
    header: 'Deposit',
    cell: ({ row }) => (
      <div className="text-mid-dark text-size-sm font-semibold">
        {row.original.formattedDeposit}
      </div>
    ),
  },
  {
    accessorKey: 'status',
    header: 'Status',
    cell: ({ row }) => {
      return (
        <div className="text-mid-dark text-size-sm font-medium">{row.original.statusText}</div>
      );
    },
  },
  {
    id: 'actions',
    cell: ({ row }) => {
      const statusText = row.original.statusText;
      if (statusText === 'Live') {
        return <AddMarketDepositButton marketId={row.original.id} />;
      }

      if (statusText === 'Awaiting Result') {
        return (
          <ProposeAnswerButton
            marketId={row.original.id}
            className="w-[140px]"
            onSuccess={refetch}
          />
        );
      }

      const isFinalizable =
        statusText === 'Win a dispute' || statusText === 'Resolved' || statusText === 'Void';

      // TODO: modify this when ready
      // if (isFinalizable && row.original?.finalizedAt) {
      //   return <div className="w-[140px]"></div>;
      // }

      return <div className="w-[140px]"></div>;
    },
  },
];

export default function ActivePredictionsTable() {
  const [rowSelection, setRowSelection] = React.useState({});
  const { data, isLoading, error, refetch } = useActivePredictions({ page: 0, limit: 50 });

  const columns = createColumns(refetch);

  const table = useReactTable<ActivePredictionTransformed>({
    data: data?.markets || [],
    columns: columns as ColumnDef<ActivePredictionTransformed>[],
    getCoreRowModel: getCoreRowModel(),
    onRowSelectionChange: setRowSelection,
    state: {
      rowSelection,
    },
  });

  if (isLoading) {
    return <ActivePredictionsTableSkeleton />;
  }

  if (error) {
    return (
      <div className="w-full">
        <div className="p-4">
          <div className="flex h-24 items-center justify-center text-red-500">
            Error loading predictions
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="p-4">
        <Table>
          <TableHeader className="">
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id} className="border-b-line border-b-0">
                {headerGroup.headers.map(header => (
                  <TableHead className="text-size-sm text-gray-3 font-semibold" key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow key={row.id} className="border-0">
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow className="border-0">
                <TableCell colSpan={columns.length} className="pt-space-50 h-full text-center">
                  <div className="gap-space-10 flex flex-col items-center">
                    <img src="/assets/images/no-markets-available.svg" alt="No markets available" />
                    <div className="text-size-sm text-gray-3 font-semibold">
                      No Markets Available
                    </div>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
