'use client';

import CommonAvatar from '@/components/ui/avatar-image';
import { usePredictionHistory } from '@/hooks/query/channel/use-prediction-history';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/ui/components/table';
import { Popover, PopoverContent, PopoverTrigger } from '@repo/ui/components/popover';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import * as React from 'react';

type PredictionHistory = {
  id: string;
  title: string;
  imageUrl?: string;
  rewards: {
    prediction: string;
    dispute: string;
  };
  outcomes: {
    initial: string;
    final: string;
  };
  finalizedAt: string;
};

const columns: ColumnDef<PredictionHistory>[] = [
  {
    accessorKey: 'title',
    header: 'Prediction',
    cell: ({ row }) => {
      const { finalizedAt, imageUrl, title, id } = row.original;
      return (
        <div className="gap-space-20 flex items-center">
          <CommonAvatar imageUrl={imageUrl} size="md2" />
          <div className="flex flex-col">
            <div className="text-mid-dark text-size-sm font-semibold">{title}</div>
            <div className="text-size-xs text-icon-dark flex font-semibold">{finalizedAt}</div>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'rewards',
    header: 'Rewards',
    cell: ({ row }) => {
      const { prediction, dispute } = row.original.rewards;
      const totalRewards = Number(prediction) + Number(dispute);
      const [isOpen, setIsOpen] = React.useState(false);

      return (
        <Popover open={isOpen} onOpenChange={setIsOpen}>
          <PopoverTrigger asChild>
            <button
              className="text-mid-dark text-size-sm font-semibold underline hover:no-underline"
              onMouseEnter={() => setIsOpen(true)}
              onMouseLeave={() => setIsOpen(false)}
            >
              ${totalRewards.toLocaleString()}
            </button>
          </PopoverTrigger>
          <PopoverContent
            className="w-80"
            onMouseEnter={() => setIsOpen(true)}
            onMouseLeave={() => setIsOpen(false)}
          >
            <div className="space-y-4">
              <h3 className="text-size-md font-semibold">Reward Details</h3>
              <div className="flex flex-col space-y-3">
                <div className="flex flex-col">
                  <span className="text-size-xs text-gray-3 font-medium">Prediction Reward</span>
                  <span className="text-size-sm font-semibold">
                    ${Number(prediction).toLocaleString()}
                  </span>
                </div>
                <div className="flex flex-col">
                  <span className="text-size-xs text-gray-3 font-medium">Dispute Reward</span>
                  <span className="text-size-sm font-semibold">
                    ${Number(dispute).toLocaleString()}
                  </span>
                </div>
              </div>
            </div>
          </PopoverContent>
        </Popover>
      );
    },
  },
  {
    accessorKey: 'outcomes.initial',
    header: 'Initial Outcome',
    cell: ({ row }) => (
      <div className="text-mid-dark text-size-sm font-medium">{row.original.outcomes.initial}</div>
    ),
  },
  {
    accessorKey: 'outcomes.final',
    header: 'Final Outcome',
    cell: ({ row }) => (
      <div className="text-mid-dark text-size-sm font-medium">{row.original.outcomes.final}</div>
    ),
  },
];

export default function PredictionHistoryTable() {
  const [rowSelection, setRowSelection] = React.useState({});
  const { data, isLoading, error } = usePredictionHistory({ page: 0, limit: 10 });

  const table = useReactTable<PredictionHistory>({
    data: data?.markets || [],
    columns: columns as ColumnDef<PredictionHistory>[],
    getCoreRowModel: getCoreRowModel(),
    onRowSelectionChange: setRowSelection,
    state: {
      rowSelection,
    },
  });

  if (isLoading) {
    return (
      <div className="w-full">
        <div className="p-4">
          <div className="flex h-24 items-center justify-center">Loading...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full">
        <div className="p-4">
          <div className="flex h-24 items-center justify-center text-red-500">
            Error loading prediction history
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="p-4">
        <Table>
          <TableHeader className="">
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id} className="border-b-line border-b-0">
                {headerGroup.headers.map(header => (
                  <TableHead className="text-size-sm text-gray-3 font-semibold" key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow key={row.id} className="border-0">
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow className="border-0">
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
