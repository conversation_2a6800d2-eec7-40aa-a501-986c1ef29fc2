'use client';

import DepositChannelCollateralButton from '@/components/actions/deposit-channel-collateral-button';
import WithdrawChannelCollateralButton from '@/components/actions/withdraw-channel-deposit-button';
import { Popup } from '@/components/ui/popup';
import { useChannelCollateral } from '@/hooks/query/channel';
import { formatCurrency } from '@/lib/format';
import { useState } from 'react';
import DepositHistoryPopup from './deposit-history-popup';

export default function DepositsDashboard() {
  const { data: collateralData, isLoading, error } = useChannelCollateral();
  const totalAmount = collateralData ? collateralData.totalFormatted : 0;
  const [isDepositHistoryPopupOpen, setIsDepositHistoryPopupOpen] = useState(false);

  const handleDepositHistoryClick = () => {
    setIsDepositHistoryPopupOpen(true);
  };

  if (error) {
    return (
      <section className="border-t-line pt-space-50 gap-space-30 flex flex-col border-t">
        <header>
          <h2 className="text-size-base text-mid-dark font-bold">Dashboard</h2>
        </header>
        <div className="text-no-red text-size-sm">
          Failed to load deposit information. Please try again.
        </div>
      </section>
    );
  }

  return (
    <>
      <section className="border-t-line pt-space-50 gap-space-30 flex flex-col border-t">
        <header>
          <h2 className="text-size-base text-mid-dark font-bold">Dashboard</h2>
        </header>
        <div className="gap-space-60 flex">
          <div className="gap-space-15 flex flex-1 flex-col">
            <h3 className="text-size-sm text-mid-dark font-semibold">Total Deposit</h3>
            <div className="gap-space-10 flex items-center">
              <div className="border-line bg-gray-2 px-space-15 flex h-(--input-height-md) flex-1 items-center justify-between border">
                <span className="text-size-lg text-mid-dark font-bold">
                  {isLoading ? '' : formatCurrency(totalAmount)}
                </span>
              </div>
              <div className="gap-space-15 flex items-center">
                <DepositChannelCollateralButton isDisabled={isLoading}>
                  Deposit
                </DepositChannelCollateralButton>
                <WithdrawChannelCollateralButton isDisabled={isLoading}>
                  Withdraw
                </WithdrawChannelCollateralButton>
              </div>
            </div>
            <div className="flex justify-end">
              <button
                className="text-size-xs text-sky hover:text-sky/80 underline transition-colors"
                onClick={handleDepositHistoryClick}
              >
                Details
              </button>
            </div>
          </div>
          <div className="gap-space-15 flex flex-1 flex-col">
            <h3 className="text-size-sm text-mid-dark font-semibold">Deposit Status</h3>
            <div className="gap-space-15 flex">
              <div className="border-line bg-gray-2 px-space-15 flex h-(--input-height-md) flex-1 items-center justify-between border">
                <span className="text-size-xs text-mid-dark">Available</span>
                <span className="text-size-lg text-mid-dark font-bold">
                  {isLoading ? '' : '$' + collateralData?.availableFormatted || '0'}
                </span>
              </div>
              <div className="border-line bg-gray-2 px-space-15 flex h-(--input-height-md) flex-1 items-center justify-between border">
                <span className="text-size-xs text-mid-dark">Tied up</span>
                <span className="text-size-lg text-mid-dark font-bold">
                  {isLoading ? '' : '$' + collateralData?.tiedFormatted || '0'}
                </span>
              </div>
            </div>
          </div>
        </div>
      </section>
      <Popup isOpen={isDepositHistoryPopupOpen} onClose={() => setIsDepositHistoryPopupOpen(false)}>
        <DepositHistoryPopup />
      </Popup>
    </>
  );
}
