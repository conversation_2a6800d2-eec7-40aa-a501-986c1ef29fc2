import type { Meta, StoryObj } from '@storybook/react';
import SubscribedChannel from './subscribed-channel';
import { mockSubscribedChannels } from '../mock-data';
import { Switch } from '@repo/ui/components/switch';

const meta: Meta<typeof SubscribedChannel> = {
  title: 'SubscribedChannel',
  component: SubscribedChannel,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof SubscribedChannel>;

// 기본 스토리
export const Default: Story = {
  args: {
    ...mockSubscribedChannels[0],
  },
};

// 구독 중인 채널
export const Subscribed: Story = {
  args: {
    ...mockSubscribedChannels[0],
    isSubscribed: true,
  },
};

// 구독하지 않은 채널
export const Unsubscribed: Story = {
  args: {
    ...mockSubscribedChannels[0],
    isSubscribed: false,
  },
};

// 많은 구독자를 가진 채널
export const ManySubscribers: Story = {
  args: {
    ...mockSubscribedChannels[0],
    subscribers: 1000000,
    totalVolume: '$50M',
    totalMarkets: 250,
  },
};

// 긴 채널 이름
export const LongChannelName: Story = {
  args: {
    ...mockSubscribedChannels[0],
    title:
      'This is a very long channel name that might cause layout issues if not handled properly',
  },
};

// 다양한 소셜 링크를 가진 채널
export const WithManySocialLinks: Story = {
  args: {
    ...mockSubscribedChannels[0],
    socialLinks: [
      { name: 'twitter', url: '#' },
      { name: 'telegram', url: '#' },
      { name: 'discord', url: '#' },
      { name: 'youtube', url: '#' },
      { name: 'instagram', url: '#' },
    ],
  },
};

// 모바일 뷰 시뮬레이션 (좁은 컨테이너)
export const MobileView: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
  },
  decorators: [
    Story => (
      <div style={{ width: '320px' }}>
        <Story />
      </div>
    ),
  ],
  args: {
    ...mockSubscribedChannels[0],
  },
};

// Switch 컴포넌트 단독 예제
export const SwitchExample: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <div className="flex items-center space-x-2">
        <Switch id="switch-example-1" />
        <label htmlFor="switch-example-1">기본 스위치</label>
      </div>
      <div className="flex items-center space-x-2">
        <Switch id="switch-example-2" checked={true} />
        <label htmlFor="switch-example-2">활성화된 스위치</label>
      </div>
      <div className="flex items-center space-x-2">
        <Switch id="switch-example-3" disabled />
        <label htmlFor="switch-example-3">비활성화된 스위치</label>
      </div>
      <div className="flex items-center space-x-2">
        <Switch
          id="switch-example-4"
          checked={true}
          className="data-[state=checked]:bg-sky data-[state=unchecked]:bg-gray-3"
        />
        <label htmlFor="switch-example-4">커스텀 스타일 스위치</label>
      </div>
    </div>
  ),
  parameters: {
    controls: { hideNoControlsWarning: true },
  },
};

// 여러 채널 목록 표시
export const ChannelList: Story = {
  decorators: [
    Story => (
      <div style={{ width: '800px' }}>
        <div className="gap-space-5 flex flex-col">
          {mockSubscribedChannels.slice(0, 3).map((channel, index) => (
            <SubscribedChannel key={index} {...channel} />
          ))}
        </div>
      </div>
    ),
  ],
  args: {},
  parameters: {
    controls: { hideNoControlsWarning: true },
  },
};
