'use client';

import PageContainer from '@/components/layouts/page-container';
import {
  UserSubscriptionsProps,
  useUserSubscriptions,
} from '@/hooks/query/user/use-user-subscriptions';
import SubscribedChannel from './components/subscribed-channel';

export default function SubscriptionsPage() {
  const { data } = useUserSubscriptions();

  const subscribedChannels: UserSubscriptionsProps['channels'] = data?.channels || [];

  const handleSubscriptionChange = (id: string, isSubscribed: boolean) => {
    console.log(`Channel ${id} subscription changed to ${isSubscribed}`);
  };

  const totalSubscribedChannels = subscribedChannels.filter(channel => channel.isSubscribed).length;

  return (
    <PageContainer title="Subscribed Channels">
      <div className="flex flex-col">
        <div className="mb-space-30 text-size-sm text-gray-3">
          Channels: <span className="text-mid-dark">{totalSubscribedChannels}</span>
        </div>
        <div className="gap-space-5 flex flex-col">
          {subscribedChannels.map(channel => (
            <SubscribedChannel
              key={channel.channelId}
              channel={channel}
              socialLinks={[]}
              onSubscriptionChange={handleSubscriptionChange}
            />
          ))}
        </div>
      </div>
    </PageContainer>
  );
}
