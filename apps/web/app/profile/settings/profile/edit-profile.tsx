'use client';

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { BaseInput } from '@/components/ui/base.input';
import { BaseButton } from '@/components/ui/base.button';
import { AvatarImageUploader } from '@/components/ui/avatar-image-uploader';
import { pxToRem } from '@repo/ui/lib/utils';
import { useCurrentUser } from '@/hooks/query/user';
import { useUpdateUserProfile } from '@/hooks/query/user';
import { UpdateUserProfileRequestBodySchema } from '@/lib/api/user/user.schema.server';
import { toast } from '@repo/ui/components/sonner';
import { InputWithCharacterCount } from '@/components/ui/input-with-character-count';
import { TextareaWithCharacterCount } from '@/components/ui/textarea-with-character-count';

type ProfileFormValues = z.infer<typeof UpdateUserProfileRequestBodySchema>;

export default function EditProfile() {
  const { data: userInfo, refetch } = useCurrentUser();
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');

  // userInfo가 로드되면 imagePreview 초기값 설정
  useEffect(() => {
    if (userInfo?.imageUrl) {
      setImagePreview(userInfo.imageUrl);
    }
  }, [userInfo?.imageUrl]);

  const updateUserProfileMutation = useUpdateUserProfile({
    onSuccess: () => {
      toast.success('Profile updated successfully!');
      refetch();
    },
    onError: error => {
      toast.error('Failed to update profile: ' + error.message);
    },
  });

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(UpdateUserProfileRequestBodySchema),
    defaultValues: {
      nickname: userInfo?.nickname || '',
      bio: userInfo?.bio || '',
      image: undefined as unknown as File | undefined,
      email: userInfo?.email || '',
    },
  });

  // userInfo가 로드되면 form 값들을 업데이트
  useEffect(() => {
    if (userInfo) {
      form.reset({
        nickname: userInfo.nickname || '',
        bio: userInfo.bio || '',
        image: undefined as unknown as File | undefined,
        email: userInfo.email || '',
      });
    }
  }, [userInfo, form]);

  const handleFileSelect = (file: File | null) => {
    if (!file) {
      setImageFile(null);
      setImagePreview(userInfo?.imageUrl || '');
      return;
    }

    setImageFile(file);

    // Create preview with base64
    const reader = new FileReader();
    reader.onload = e => {
      const result = e.target?.result as string;
      setImagePreview(result);
    };
    reader.readAsDataURL(file);
  };

  const onSubmit = async (values: ProfileFormValues) => {
    try {
      // Create FormData for file upload according to UpdateUserProfileReqDto
      const formData = new FormData();

      // Only include fields that have been changed
      if (values.nickname && values.nickname !== userInfo?.nickname) {
        formData.append('nickname', values.nickname);
      }

      if (values.bio !== undefined && values.bio !== userInfo?.bio) {
        formData.append('bio', values.bio);
      }

      if (values.email !== undefined && values.email !== userInfo?.email) {
        formData.append('email', values.email);
      }

      // Add image file if selected (field name is 'image' according to schema)
      if (imageFile) {
        formData.append('image', imageFile);
      }

      // Only call API if there are changes
      if (Array.from(formData.keys()).length > 0) {
        await updateUserProfileMutation.mutateAsync(formData);
      } else {
        toast.info('No changes to save');
      }
    } catch (error: any) {
      // Error handling is done in the hook's onError callback
      console.error('Profile update error:', error);
    }
  };

  console.log('Formstate error', form.formState.errors);
  return (
    <>
      {/* Avatar Section */}
      <div className="mb-space-30">
        <div className="flex items-center gap-6">
          <AvatarImageUploader
            imageUrl={imagePreview}
            onFileSelect={handleFileSelect}
            size={80}
            disabled={updateUserProfileMutation.isPending}
          />
          <div className="text-size-xs text-gray-3">
            <div>
              At least <strong>80px X 80px</strong> recommended. <br /> <strong>JPG or PNG</strong>{' '}
              is allowed.
            </div>
          </div>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="gap-space-30 pt-space-30 flex flex-col">
            <h2 className="text-mid-dark text-size-base font-bold">Personal Info</h2>
            <FormField
              control={form.control}
              name="nickname"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel
                    htmlFor="nickname"
                    className="text-size-sm text-mid-dark block font-medium"
                  >
                    Username
                  </FormLabel>
                  <FormControl>
                    <InputWithCharacterCount
                      id="nickname"
                      placeholder="Enter your username"
                      maxLength={42}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel
                    htmlFor="email"
                    className="text-size-sm text-mid-dark block font-medium"
                  >
                    Email
                  </FormLabel>
                  <FormControl>
                    <BaseInput
                      id="email"
                      placeholder="Enter your email"
                      {...field}
                      className={
                        form.formState.errors.email ? 'border-red-500 focus:border-red-500' : ''
                      }
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="bio"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel htmlFor="bio" className="text-size-sm text-mid-dark block font-medium">
                    Bio
                  </FormLabel>
                  <FormControl>
                    <TextareaWithCharacterCount
                      id="bio"
                      placeholder="Tell us about yourself"
                      className="resize-none"
                      maxLength={255}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="mt-8 flex space-x-4">
            <BaseButton
              type="submit"
              variant="dark"
              style={{
                width: pxToRem(148),
                fontSize: 'var(--text-size-sm13)',
              }}
              disabled={updateUserProfileMutation.isPending}
            >
              {updateUserProfileMutation.isPending ? 'Saving...' : 'Save Changes'}
            </BaseButton>
          </div>
        </form>
      </Form>
    </>
  );
}
