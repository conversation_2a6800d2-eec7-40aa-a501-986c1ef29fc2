'use client';

import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Avatar, AvatarImage } from '@repo/ui/components/avatar';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { BaseInput } from '@/components/ui/base.input';
import { BaseTextarea } from '@/components/ui/base.textarea';
import { BaseButton } from '@/components/ui/base.button';
import { pxToRem } from '@repo/ui/lib/utils';
import { useCurrentUser } from '@/hooks/query/user';
import { useUpdateUserProfile } from '@/hooks/query/user';
import { UpdateUserProfileRequestBodySchema } from '@/lib/api/user/user.schema.server';
import { toast } from '@repo/ui/components/sonner';

type ProfileFormValues = z.infer<typeof UpdateUserProfileRequestBodySchema>;

export default function EditProfile() {
  const { data: userInfo } = useCurrentUser();
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string>(userInfo?.imageUrl || '');

  const updateUserProfileMutation = useUpdateUserProfile({
    onSuccess: () => {
      toast.success('Profile updated successfully!');
    },
    onError: error => {
      toast.error('Failed to update profile: ' + error.message);
    },
  });

  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(UpdateUserProfileRequestBodySchema),
    defaultValues: {
      nickname: userInfo?.nickname || '',
      bio: userInfo?.bio || '',
      image: undefined as unknown as File | undefined,
    },
  });

  const handleAvatarEdit = () => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = 'image/jpeg,image/png';
    input.onchange = e => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        handleFileSelect(file);
      }
    };
    input.click();
  };

  const handleFileSelect = (file: File) => {
    // Validate file type
    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    // Validate file size (5MB limit)
    if (file.size > 5 * 1024 * 1024) {
      toast.error('File size must be less than 5MB');
      return;
    }

    setImageFile(file);

    // Create preview with base64
    const reader = new FileReader();
    reader.onload = e => {
      const result = e.target?.result as string;
      setImagePreview(result);
    };
    reader.readAsDataURL(file);
  };

  const onSubmit = async (values: ProfileFormValues) => {
    try {
      // Create FormData for file upload according to UpdateUserProfileReqDto
      const formData = new FormData();

      // Only include fields that have been changed
      if (values.nickname && values.nickname !== userInfo?.nickname) {
        formData.append('nickname', values.nickname);
      }

      if (values.bio !== undefined && values.bio !== userInfo?.bio) {
        formData.append('bio', values.bio);
      }

      // Add image file if selected (field name is 'image' according to schema)
      if (imageFile) {
        formData.append('image', imageFile);
      }

      // Only call API if there are changes
      if (Array.from(formData.keys()).length > 0) {
        await updateUserProfileMutation.mutateAsync(formData);
      } else {
        toast.info('No changes to save');
      }
    } catch (error: any) {
      // Error handling is done in the hook's onError callback
      console.error('Profile update error:', error);
    }
  };

  return (
    <>
      {/* Avatar Section */}
      <div className="mb-space-30">
        <div className="flex items-center gap-6">
          <button
            onClick={handleAvatarEdit}
            className="relative size-[80px]"
            type="button"
            aria-label="Edit profile picture"
          >
            <Avatar className="h-full w-full">
              <AvatarImage src={imagePreview} className="object-cover" />
            </Avatar>
            <div
              style={{
                right: '0px',
                bottom: '0px',
              }}
              className="bg-sky absolute flex size-[24px] items-center justify-center rounded-full"
            >
              <svg
                width="10"
                height="10"
                viewBox="0 0 10 10"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M3.86229 9.5V0.5H6.13771V9.5H3.86229ZM0.5 6.13771V3.86229H9.5V6.13771H0.5Z"
                  fill="white"
                />
              </svg>
            </div>
          </button>
          <div className="text-size-xs text-gray-3">
            <div>
              At least <strong>80px X 80px</strong> recommended. <br /> <strong>JPG or PNG</strong>{' '}
              is allowed.
            </div>
          </div>
        </div>
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
          <div className="gap-space-30 pt-space-30 flex flex-col">
            <h2 className="text-mid-dark text-size-base font-bold">Personal Info</h2>
            <FormField
              control={form.control}
              name="nickname"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel
                    htmlFor="nickname"
                    className="text-size-sm text-mid-dark block font-medium"
                  >
                    Username
                  </FormLabel>
                  <FormControl>
                    <BaseInput id="nickname" placeholder="Enter your username" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="bio"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel htmlFor="bio" className="text-size-sm text-mid-dark block font-medium">
                    Bio
                  </FormLabel>
                  <FormControl>
                    <BaseTextarea
                      id="bio"
                      placeholder="Tell us about yourself"
                      className="resize-none"
                      style={{ height: '248px' }}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <div className="mt-8 flex space-x-4">
            <BaseButton
              type="submit"
              variant="dark"
              style={{
                width: pxToRem(148),
                fontSize: 'var(--text-size-sm13)',
              }}
              disabled={updateUserProfileMutation.isPending}
            >
              {updateUserProfileMutation.isPending ? 'Saving...' : 'Save Changes'}
            </BaseButton>
          </div>
        </form>
      </Form>
    </>
  );
}
