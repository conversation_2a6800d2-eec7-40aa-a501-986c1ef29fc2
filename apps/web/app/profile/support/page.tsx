import PageContainer from '@/components/layouts/page-container';

export default function SupportPage() {
  return (
    <PageContainer title="Support">
      <div className="gap-space-40 flex flex-col">
        <section className="gap-space-15 flex flex-col">
          <header className="mb-space-10">
            <h2 className="text-size-lg text-mid-dark font-semibold">1:1 Assistance</h2>
          </header>
          <p className="text-size-sm text-gray-3 leading-relaxed">
            PredictGo users can receive 1:1 assistance by contacting us via email.
            {/* <br /> */}
            If you have any questions or experience any issues while using the service,
            {/* <br /> */}
            please reach out to us at{' '}
            <a href="mailto:<EMAIL>" className="text-sky hover:underline">
              <EMAIL>.
            </a>
          </p>
        </section>

        <section className="p-space-30 gap-space-15 border-line bg-gray-2 flex flex-col border">
          <header className="mb-space-10">
            <h2 className="text-size-lg text-mid-dark font-semibold">Important Notices</h2>
          </header>
          <ul className="gap-space-20 pl-space-20 text-gray-3 flex list-disc flex-col">
            <li className="text-size-sm leading-relaxed">
              The PredictGo team will never ask for your private key. Never share your private key
              with anyone. Doing so can give others access to your wallet.
            </li>
            <li className="text-size-sm leading-relaxed">
              To protect your privacy, please do not include sensitive information such as passwords
              when contacting us.
            </li>
            <li className="text-size-sm leading-relaxed">
              Account deletion is only available for users who signed up via email. To request
              account deletion, please contact us at{' '}
              <a href="mailto:<EMAIL>" className="text-sky hover:underline">
                <EMAIL>
              </a>
              . For detailed instructions, refer to our{' '}
              <a href="#" className="text-sky hover:underline">
                Docs
              </a>
            </li>
          </ul>
        </section>
      </div>
    </PageContainer>
  );
}
