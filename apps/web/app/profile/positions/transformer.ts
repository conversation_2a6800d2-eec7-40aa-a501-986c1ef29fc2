import { PortfolioPosition } from '@/lib/api/portfolio/portfolio.schema.server';
import { PositionItem } from './types';

export const transformPostionsData = (positions: PortfolioPosition[]): PositionItem[] => {
  return positions.map(position => ({
    market: {
      id: position.market.id,
      title: position.market.title,
      imageUrl: position.market.imageUrl || '',
    },
    value: position.value.toString(),
    outcome: {
      label: position.outcome,
      order: position.outcomeOrder,
    },

    estimatedOdds: position.estimatedOdds,
    estimatedWin: position.estimatedWin,
  }));
};
