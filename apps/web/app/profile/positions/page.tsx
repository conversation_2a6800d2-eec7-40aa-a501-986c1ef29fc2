'use client';
import PageContainer from '@/components/layouts/page-container';
import {
  BaseTabs,
  BaseTabsContent,
  BaseTabsList,
  BaseTabsTrigger,
} from '@/components/ui/base.tabs';
import { pxToRem } from '@repo/ui/lib/utils';
import UserInfoSection from '@/components/common/user-info-section';
import { useGlobalStore } from '@/store/global.store';
import UserStatsSection from '@/components/common/user-stats-section';
import UserActivitiesTab from '@/components/common/user-activities-tab';
import UserPositionsTab from '@/components/common/user-positions-tab';

export default function PositionsPage() {
  const safeSmartAccountAddress = useGlobalStore(v => v.safeSmartAccount?.address);

  if (!safeSmartAccountAddress) {
    return (
      <PageContainer title="Positions">
        <div className="flex items-center justify-center py-20">
          <div className="text-gray-600">Please connect your wallet</div>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer title="Positions">
      {/* User Info Section */}
      <UserInfoSection userAddress={safeSmartAccountAddress} />

      {/* Values Info Section */}
      <UserStatsSection userAddress={safeSmartAccountAddress} />

      {/* Tab Section */}
      <section
        style={
          {
            '--tab-trigger-width': pxToRem(200),
          } as React.CSSProperties
        }
      >
        <BaseTabs defaultValue="positions">
          <div className="border-b-line border-b">
            <BaseTabsList className="border-none">
              <BaseTabsTrigger className="min-w-(--tab-trigger-width)" value="positions">
                Positions
              </BaseTabsTrigger>
              <BaseTabsTrigger className="min-w-(--tab-trigger-width)" value="activity">
                Activity
              </BaseTabsTrigger>
            </BaseTabsList>
          </div>
          <BaseTabsContent value="positions">
            <UserPositionsTab userAddress={safeSmartAccountAddress} />
          </BaseTabsContent>
          <BaseTabsContent value="activity">
            <UserActivitiesTab userAddress={safeSmartAccountAddress} />
          </BaseTabsContent>
        </BaseTabs>
      </section>
    </PageContainer>
  );
}
