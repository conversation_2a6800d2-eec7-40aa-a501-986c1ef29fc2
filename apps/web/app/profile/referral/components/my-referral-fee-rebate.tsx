'use client';

import { useReferralBenefit } from '@/hooks/query/referral/use-referral-benefit';

export default function MyReferralFeeRebate() {
  const { data, isLoading, error } = useReferralBenefit();

  if (error) {
    return (
      <section className="pt-space-50 gap-space-30 flex flex-col">
        <header>
          <h2 className="dashboard-h2">Fee Rebate</h2>
        </header>
        <div className="text-center text-red-500">
          Failed to load referral benefit data. Please try again.
        </div>
      </section>
    );
  }

  return (
    <section className="pt-space-50 gap-space-30 flex flex-col">
      <header>
        <h2 className="dashboard-h2">Fee Rebate</h2>
      </header>
      <div className="gap-space-60 flex">
        <div className="gap-space-30 flex flex-1">
          <div className="gap-space-15 flex flex-1 flex-col">
            <div className="dashboard-h3">Bind Referral Code</div>
            <div className="text-size-sm border-line px-space-15 bg-gray-2 flex h-(--value-box-height) items-center border font-semibold">
              {isLoading ? (
                <div className="h-4 w-8 animate-pulse rounded bg-gray-300" />
              ) : (
                (data?.bindReferralCode ?? '-')
              )}
            </div>
          </div>
          <div className="gap-space-15 flex flex-1 flex-col">
            <div className="dashboard-h3">Fee Rebate</div>
            <div className="text-size-sm border-line px-space-15 bg-gray-2 flex h-(--value-box-height) items-center border font-semibold">
              {isLoading ? (
                <div className="h-4 w-12 animate-pulse rounded bg-gray-300" />
              ) : (
                (data?.feeRebateRatioPercentage ?? '-')
              )}
            </div>
          </div>
        </div>
        <div className="gap-space-15 flex flex-1 flex-col">
          <div className="dashboard-h3">Total Rebate</div>
          <div className="text-size-lg border-line px-space-15 bg-gray-2 flex h-(--value-box-height) items-center border font-bold">
            {isLoading ? (
              <div className="h-6 w-16 animate-pulse rounded bg-gray-300" />
            ) : (
              `$${data?.accumulatedProfitFormatted ?? '-'}`
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
