import PageContainer from '@/components/layouts/page-container';
import { pxToRem } from '@repo/ui/lib/utils';

import MyReferralDashboard from './components/my-referral-dashboard';
import MyReferralsStats from './components/my-referrals-stats';
import MyReferralFeeRebate from './components/my-referral-fee-rebate';

export default function ReferralPage() {
  return (
    <PageContainer
      title="Referral"
      style={
        {
          '--value-box-height': pxToRem(50),
        } as React.CSSProperties
      }
    >
      <div className="flex flex-col">
        <MyReferralDashboard />
        <MyReferralsStats />
        <MyReferralFeeRebate />
      </div>
    </PageContainer>
  );
}
