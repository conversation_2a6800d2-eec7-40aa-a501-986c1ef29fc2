'use client';

import { useState, useMemo } from 'react';
// Import the actual API service class
import { AuthApis } from '@/lib/api/auth/auth.service';
import ConnectButton from '@/components/connect-button';

// Import shadcn/ui components
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { Input } from '@repo/ui/components/input';
import { Label } from '@repo/ui/components/label';
import { Button } from '@repo/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@repo/ui/components/card';
import { Alert, AlertDescription, AlertTitle } from '@repo/ui/components/alert';
import { Terminal } from 'lucide-react'; // For Alert icon

// Define the structure for API method details
interface ApiMethodDefinition {
  serviceName: string;
  serviceInstance: AuthApis; // Store instance for direct calls
  methodName: keyof AuthApis;
  description: string;
  params?: { name: string; type: 'string' | 'number'; placeholder?: string }[];
}

// Instantiate services (only AuthApis for now)
const authApisInstance = new AuthApis();

// List of available API methods to test
const availableApiMethods: ApiMethodDefinition[] = [
  {
    serviceName: 'AuthApis',
    serviceInstance: authApisInstance,
    methodName: 'getNonce',
    description: 'Get nonce for authentication',
    params: [{ name: 'address', type: 'string', placeholder: '0x... address' }],
  },
  {
    serviceName: 'AuthApis',
    serviceInstance: authApisInstance,
    methodName: 'getSession',
    description: 'Get current session information',
  },
  {
    serviceName: 'AuthApis',
    serviceInstance: authApisInstance,
    methodName: 'verify',
    description: 'Verify authentication signature',
    params: [
      { name: 'message', type: 'string', placeholder: 'Signed message' },
      { name: 'signature', type: 'string', placeholder: '0x... signature' },
    ],
  },
  {
    serviceName: 'AuthApis',
    serviceInstance: authApisInstance,
    methodName: 'signOut',
    description: 'Sign out the current user',
  },
];

export default function ApiTestPage() {
  const [selectedApiIndex, setSelectedApiIndex] = useState<string>('-1');
  const [paramValues, setParamValues] = useState<{ [key: string]: string }>({});
  const [response, setResponse] = useState<any>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  const selectedApiDefinition = useMemo(() => {
    const index = parseInt(selectedApiIndex, 10);
    // Return undefined if index is invalid, which Select component expects for placeholder
    return index >= 0 ? availableApiMethods[index] : undefined;
  }, [selectedApiIndex]);

  // Note: shadcn/ui Select returns the `value` directly, not the event
  const handleApiSelectionChange = (value: string) => {
    setSelectedApiIndex(value); // value is the index string ("0", "1", ...)
    setParamValues({}); // Reset params when selection changes
    setError(null);
    setResponse(null);
  };

  const handleParamChange = (event: React.ChangeEvent<HTMLInputElement>, paramName: string) => {
    setParamValues(prev => ({ ...prev, [paramName]: event.target.value }));
  };

  const handleCallMethod = async () => {
    if (!selectedApiDefinition) {
      setError('테스트할 API 메소드를 선택해주세요.');
      return;
    }

    setLoading(true);
    setError(null);
    setResponse(null);

    try {
      const { serviceInstance, methodName, params } = selectedApiDefinition;

      // Prepare arguments based on defined params
      let args: any;
      if (params && params.length > 0) {
        // Simple check for single object parameter (like 'verify') vs multiple args
        if (methodName === 'verify') {
          // Assuming 'verify' takes an object
          args = params.reduce(
            (acc, param) => {
              acc[param.name] = paramValues[param.name] || '';
              return acc;
            },
            {} as { [key: string]: string }
          );
        } else {
          // Assuming others take positional arguments (like 'getNonce')
          args = params.map(param => paramValues[param.name] || '');
        }
      } else {
        args = []; // No parameters needed
      }

      // Dynamically call the method on the service instance
      // Need to handle both positional and object arguments based on method signature
      let result;
      if (methodName === 'verify' && typeof args === 'object' && !Array.isArray(args)) {
        result = await (serviceInstance[methodName] as Function)(args);
      } else if (Array.isArray(args)) {
        result = await (serviceInstance[methodName] as Function)(...args);
      } else {
        // Fallback or handle other cases if necessary
        result = await (serviceInstance[methodName] as Function)();
      }

      setResponse(result);
    } catch (e: any) {
      setError(`API 메소드 호출 실패: ${e.message}${e.response ? ` (${e.response.status})` : ''}`);
      console.error('API Call Error:', e);
      if (e.response) {
        // Attempt to show response body if available in error
        e.response.text().then((text: string) => {
          console.error('Error Response Body:', text);
          setError(prev => `${prev}\nResponse: ${text}`);
        });
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="mx-auto w-full max-w-2xl">
      <CardHeader>
        <CardTitle>API Service Method Test</CardTitle>
        <CardDescription>
          Select a service method, provide parameters, and call it directly.
        </CardDescription>
        <div className="pt-4">
          <ConnectButton />
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* API Method Selection Dropdown */}
        <div className="space-y-2">
          <Label htmlFor="apiSelect">Select Method</Label>
          <Select
            value={selectedApiIndex === '-1' ? undefined : selectedApiIndex}
            onValueChange={handleApiSelectionChange}
          >
            <SelectTrigger id="apiSelect">
              <SelectValue placeholder="-- Select an API Method --" />
            </SelectTrigger>
            <SelectContent>
              {availableApiMethods.map((api, index) => (
                <SelectItem key={index} value={String(index)}>
                  {api.serviceName}.{api.methodName} ({api.description})
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Parameter Inputs */}
        {selectedApiDefinition?.params && selectedApiDefinition.params.length > 0 && (
          <Card className="bg-muted/50">
            <CardHeader>
              <CardTitle className="text-base">Parameters</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {selectedApiDefinition.params.map(param => (
                <div key={param.name} className="space-y-2">
                  <Label htmlFor={param.name}>{param.name}</Label>
                  <Input
                    id={param.name}
                    type={param.type === 'number' ? 'number' : 'text'}
                    value={paramValues[param.name] || ''}
                    onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
                      handleParamChange(e, param.name)
                    }
                    placeholder={param.placeholder || param.name}
                  />
                </div>
              ))}
            </CardContent>
          </Card>
        )}

        {/* Call Method Button */}
        <div>
          <Button
            onClick={handleCallMethod}
            disabled={loading || selectedApiIndex === '-1'}
            className="w-full sm:w-auto"
          >
            {loading ? 'Calling...' : 'Call Method'}
          </Button>
        </div>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <Terminal className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>
              <pre className="font-mono text-sm whitespace-pre-wrap">{error}</pre>
            </AlertDescription>
          </Alert>
        )}

        {/* Response Display */}
        {response !== null && (
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Response</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="bg-muted rounded-md p-4 font-mono text-sm break-all whitespace-pre-wrap">
                {typeof response === 'string' ? response : JSON.stringify(response, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}
      </CardContent>
    </Card>
  );
}
