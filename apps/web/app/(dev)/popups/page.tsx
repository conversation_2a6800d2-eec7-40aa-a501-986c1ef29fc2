'use client';

import * as React from 'react';
import {
  BaseDialog as Dialog,
  BaseDialogClose as DialogClose,
  BaseDialogContent as DialogContent,
  BaseDialogDescription as DialogDescription,
  BaseDialogFooter as Di<PERSON>Footer,
  BaseDialogHeader as Di<PERSON>Header,
  BaseDialogTitle as Di<PERSON><PERSON>itle,
  BaseDialogTrigger as DialogTrigger,
  BaseDialogBody as DialogBody,
} from '@/components/ui/base.dialog';
import { But<PERSON> } from '@repo/ui/components/button';
import { Input } from '@repo/ui/components/input';
import { Label } from '@repo/ui/components/label';
import { Checkbox } from '@repo/ui/components/checkbox';
import { Alert, AlertDescription, AlertTitle } from '@repo/ui/components/alert'; // Assuming Alert exists
import {
  Mail,
  CheckCircle,
  WalletMinimal,
  ShieldCheck,
  UserPlus,
  DollarSign,
  Copy,
  CreditCard,
  QrCode,
  Info,
  Link as LinkIcon,
} from 'lucide-react'; // Import necessary icons
import { PopButton } from '@/components/ui/pop.btn';
import { XlIcon } from '@/components/icons/xl-icon';

export default function PopupsDevPage() {
  const walletAddress = '******************************************'; // Example address

  return (
    <div className="container mx-auto grid grid-cols-1 gap-8 p-10 md:grid-cols-2 lg:grid-cols-3">
      {/* --- Enable Trading --- */}
      <div className="bg- rounded-lg border p-4">
        <h2 className="mb-2 text-lg font-semibold">Enable Trading (Check Wallet)</h2>
        <Dialog>
          <DialogTrigger asChild>
            <Button variant="destructive">Open: Enable Trading</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogClose className="hidden" />
            <DialogBody>
              <XlIcon name="wallet" alt="" />
              <DialogTitle className="text-center">Enable Trading</DialogTitle>
              <DialogDescription className="text-center">
                Let’s set up your wallet to trade on PredictGo
              </DialogDescription>
            </DialogBody>
            <DialogFooter className="gap-0">
              <DialogClose asChild>
                <PopButton className="flex-1">btn1</PopButton>
              </DialogClose>
              {/* <DialogClose asChild>
                <PopButton loading>btn2</PopButton>
              </DialogClose> */}
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
