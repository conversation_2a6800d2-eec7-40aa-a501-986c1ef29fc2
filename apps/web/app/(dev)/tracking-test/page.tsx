'use client';

import { useState, useEffect } from 'react';
import { trackingService, trackingLogger } from '@/lib/tracking';
import { env } from '@/lib/env';
import { Button } from '@repo/ui/components/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@repo/ui/components/card';
import { Badge } from '@repo/ui/components/badge';
import { Separator } from '@repo/ui/components/separator';
import { useTrackingConsent } from '@/hooks/use-tracking-consent';

export default function TrackingTestPage() {
  const [logs, setLogs] = useState<string[]>([]);
  const [userId, setUserId] = useState<string>('');
  const [isClient, setIsClient] = useState(false);
  const [trackingState, setTrackingState] = useState({
    isEnabled: false,
    adapters: [] as any[],
  });
  const { consentState, acceptConsent } = useTrackingConsent();

  // 클라이언트에서만 userId 생성 및 상태 설정 (hydration 미스매치 방지)
  useEffect(() => {
    setIsClient(true);
    setUserId('test-user-' + Date.now());
  }, []);

  // 트래킹 상태 실시간 업데이트
  useEffect(() => {
    if (!isClient) return;

    const updateTrackingState = () => {
      setTrackingState({
        isEnabled: trackingService.isEnabled(),
        adapters: trackingService.getEnabledAdapters(),
      });
    };

    // 초기 상태 설정
    updateTrackingState();

    // 정기적으로 상태 확인 (변경 감지)
    const interval = setInterval(updateTrackingState, 500);

    return () => clearInterval(interval);
  }, [isClient]);

  const addLog = (message: string) => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [`[${timestamp}] ${message}`, ...prev.slice(0, 19)]);
  };

  const clearLogs = () => {
    setLogs([]);
  };

  // GA4 전자상거래 이벤트 테스트
  const testPurchaseEvent = () => {
    const transactionId = 'TXN_' + Date.now();
    trackingService.trackPurchase({
      transactionId,
      marketId: 'market-test-123',
      outcomeId: 'outcome-yes',
      marketTitle: 'Bitcoin Price Prediction Test',
      category: 'Crypto',
      amount: 100,
      currency: 'USDC',
      price: 0.75,
      quantity: 1,
      userId,
      affiliation: 'PredictGO',
      coupon: 'TEST10',
    });
    addLog(`Purchase event sent: ${transactionId}`);
  };

  const testViewItemEvent = () => {
    trackingService.trackViewItem({
      marketId: 'market-test-456',
      outcomeId: 'outcome-no',
      marketTitle: 'Ethereum Price Prediction Test',
      category: 'Crypto',
      price: 0.65,
      currency: 'USDC',
      userId,
    });
    addLog('View Item event sent');
  };

  const testAddToWishlistEvent = () => {
    trackingService.trackAddToWishlist({
      marketId: 'market-test-789',
      outcomeId: 'outcome-yes',
      marketTitle: 'Stock Market Prediction Test',
      category: 'Finance',
      price: 0.8,
      currency: 'USDC',
      userId,
    });
    addLog('Add to Wishlist event sent');
  };

  const testBeginCheckoutEvent = () => {
    trackingService.trackBeginCheckout({
      marketId: 'market-test-101',
      outcomeId: 'outcome-no',
      marketTitle: 'Sports Prediction Test',
      category: 'Sports',
      price: 0.55,
      currency: 'USDC',
      userId,
    });
    addLog('Begin Checkout event sent');
  };

  const testRefundEvent = () => {
    const transactionId = 'TXN_' + (Date.now() - 1000);
    trackingService.trackRefund({
      transactionId,
      marketId: 'market-test-202',
      outcomeId: 'outcome-yes',
      marketTitle: 'Weather Prediction Test',
      category: 'Weather',
      amount: 50,
      currency: 'USDC',
      price: 0.7,
      quantity: 1,
      userId,
    });
    addLog(`Refund event sent: ${transactionId}`);
  };

  const testViewPromotionEvent = () => {
    trackingService.trackViewPromotion({
      promotionId: 'promo-test-2024',
      promotionName: 'Test Welcome Bonus',
      creativeName: 'test-banner',
      creativeSlot: 'header',
      locationId: 'test-page',
      userId,
    });
    addLog('View Promotion event sent');
  };

  const testSelectPromotionEvent = () => {
    trackingService.trackSelectPromotion({
      promotionId: 'promo-test-2024',
      promotionName: 'Test Welcome Bonus',
      creativeName: 'test-banner',
      creativeSlot: 'header',
      locationId: 'test-page',
      userId,
    });
    addLog('Select Promotion event sent');
  };

  // 기존 이벤트 테스트 (하위 호환성)
  const testMarketViewEvent = () => {
    trackingService.trackMarketView({
      marketId: 'market-legacy-303',
      marketTitle: 'Legacy Market View Test',
      category: 'Legacy',
      userId,
    });
    addLog('Legacy Market View event sent');
  };

  const testWalletConnectEvent = () => {
    trackingService.trackWalletConnect({
      walletType: 'MetaMask',
      userId,
    });
    addLog('Wallet Connect event sent');
  };

  const testUserLoginEvent = () => {
    trackingService.trackUserLogin({
      method: 'email',
      userId,
    });
    addLog('User Login event sent');
  };

  const testIdentifyUser = () => {
    trackingService.identify(userId, {
      email: '<EMAIL>',
      plan: 'premium',
      testUser: true,
    });
    addLog('User identified');
  };

  const testSetUserProperties = () => {
    trackingService.setUserProperties({
      theme: 'dark',
      language: 'ko',
      testSession: true,
    });
    addLog('User properties set');
  };

  const testCustomEvent = () => {
    trackingService.track({
      event: 'custom_test_event',
      properties: {
        customProp1: 'value1',
        customProp2: 123,
        customProp3: true,
        timestamp: Date.now(),
      },
      userId,
    });
    addLog('Custom event sent');
  };

  const enableDebugMode = () => {
    // 디버그 모드 활성화
    (window as any).TRACKING_DEBUG = true;
    addLog('Debug mode enabled - 콘솔에서 상세 로그 확인');

    // 현재 상태 출력
    console.log('=== 트래킹 시스템 상태 ===');
    console.log('Tracking Service Enabled:', trackingService.isEnabled());
    console.log('Enabled Adapters:', trackingService.getEnabledAdapters());
    console.log('GA Measurement ID:', env.NEXT_PUBLIC_GA_MEASUREMENT_ID);
    console.log('gtag function:', typeof (window as any).gtag);
    console.log('User ID:', userId);
  };

  // 직접 gtag 호출 테스트 (비교용)
  const testDirectGtagPurchase = () => {
    const transactionId = 'DIRECT_' + Date.now();
    if (typeof (window as any).gtag === 'function') {
      (window as any).gtag('event', 'purchase', {
        transaction_id: transactionId,
        value: 100,
        currency: 'USD',
        items: [
          {
            item_id: 'direct_test_123',
            item_name: 'Direct Test Product',
            category: 'Direct Test',
            quantity: 1,
            price: 100,
          },
        ],
      });
      addLog(`DIRECT gtag purchase event sent: ${transactionId}`);
      console.log('🔥 Direct gtag purchase event:', {
        transaction_id: transactionId,
        value: 100,
        currency: 'USD',
      });
    } else {
      addLog('ERROR: gtag function not available');
    }
  };

  const testDirectGtagCustom = () => {
    if (typeof (window as any).gtag === 'function') {
      (window as any).gtag('event', 'direct_custom_test', {
        event_category: 'direct_test',
        event_label: 'console_comparison',
        value: 42,
        custom_param: 'direct_value',
      });
      addLog('DIRECT gtag custom event sent');
      console.log('🔥 Direct gtag custom event sent');
    } else {
      addLog('ERROR: gtag function not available');
    }
  };

  // 강제로 트래킹 활성화 (테스트용)
  const forceEnableTracking = () => {
    // 먼저 동의 처리
    acceptConsent(true, true);

    // 트래킹 서비스 강제 초기화
    trackingService.initialize({
      gaId: env.NEXT_PUBLIC_GA_MEASUREMENT_ID,
      clarityProjectId: env.NEXT_PUBLIC_CLARITY_PROJECT_ID,
      enabled: true,
    });

    addLog('🚀 트래킹 강제 활성화 완료');
  };

  const checkTrackingStatus = () => {
    const status = {
      service: {
        enabled: trackingService.isEnabled(),
        adapters: trackingService.getEnabledAdapters().length,
        adapterNames: trackingService.getEnabledAdapters().map(a => a.constructor.name),
      },
      consent: consentState,
      env: {
        gaId: env.NEXT_PUBLIC_GA_MEASUREMENT_ID,
        clarityId: env.NEXT_PUBLIC_CLARITY_PROJECT_ID,
      },
      window: {
        gtag: typeof window !== 'undefined' ? typeof (window as any).gtag : 'undefined',
      },
    };

    console.log('🔍 트래킹 상태 상세 분석:', status);
    addLog('트래킹 상태 상세 분석 - 콘솔 확인');
  };

  // 리액티브 상태 사용
  const { isEnabled, adapters: enabledAdapters } = trackingState;

  // 클라이언트 사이드 렌더링 완료 전에는 로딩 상태 표시
  if (!isClient) {
    return (
      <div className="container mx-auto space-y-6 p-6">
        <div className="flex items-center justify-center">
          <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-gray-900"></div>
          <span className="ml-2">로딩 중...</span>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto space-y-6 p-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">트래킹 시스템 테스트</h1>
        <div className="flex items-center gap-2">
          <Badge variant={isEnabled ? 'default' : 'destructive'}>
            {isEnabled ? '활성화됨' : '비활성화됨'}
          </Badge>
          <Badge variant="outline">{enabledAdapters.length}개 어댑터</Badge>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>시스템 정보</CardTitle>
          <CardDescription>현재 트래킹 시스템 상태</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4">
            <div>
              <p className="text-sm font-medium">사용자 ID</p>
              <p className="text-muted-foreground text-sm">{userId || '생성 중...'}</p>
            </div>
            <div>
              <p className="text-sm font-medium">상태</p>
              <p className="text-muted-foreground text-sm">
                {isEnabled ? '트래킹 활성화' : '트래킹 비활성화'}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium">활성 어댑터</p>
              <p className="text-muted-foreground text-sm">
                {enabledAdapters.length}개 (
                {enabledAdapters.map(a => a.constructor.name).join(', ')})
              </p>
            </div>
            <div>
              <p className="text-sm font-medium">GA Measurement ID</p>
              <p className="text-muted-foreground text-sm">
                {env.NEXT_PUBLIC_GA_MEASUREMENT_ID || '설정되지 않음'}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium">gtag 함수</p>
              <p className="text-muted-foreground text-sm">
                {isClient && (window as any).gtag ? '로드됨' : '로드되지 않음'}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium">브라우저 환경</p>
              <p className="text-muted-foreground text-sm">{isClient ? '클라이언트' : '서버'}</p>
            </div>
            <div>
              <p className="text-sm font-medium">동의 상태</p>
              <p className="text-muted-foreground text-sm">
                {consentState
                  ? `GA: ${consentState.googleAnalytics ? '✅' : '❌'}, Clarity: ${consentState.microsoftClarity ? '✅' : '❌'}`
                  : '동의 상태 없음 ❌'}
              </p>
            </div>
            <div>
              <p className="text-sm font-medium">동의 타임스탬프</p>
              <p className="text-muted-foreground text-sm">
                {consentState?.timestamp
                  ? new Date(consentState.timestamp).toLocaleString()
                  : '없음'}
              </p>
            </div>
          </div>

          {/* 디버그 액션 버튼들 */}
          <Separator className="my-4" />
          <div className="flex gap-2">
            <Button onClick={forceEnableTracking} variant="destructive" size="sm">
              🚀 트래킹 강제 활성화
            </Button>
            <Button onClick={checkTrackingStatus} variant="outline" size="sm">
              🔍 상태 상세 분석
            </Button>
            <Button onClick={enableDebugMode} variant="outline" size="sm">
              🐛 디버그 모드 활성화
            </Button>
          </div>
        </CardContent>
      </Card>

      <div className="grid gap-6 lg:grid-cols-2">
        {/* GA4 전자상거래 이벤트 */}
        <Card>
          <CardHeader>
            <CardTitle>GA4 전자상거래 이벤트</CardTitle>
            <CardDescription>Google Analytics 4 표준 전자상거래 이벤트</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button onClick={testPurchaseEvent} className="w-full">
              Purchase Event 테스트
            </Button>
            <Button onClick={testViewItemEvent} variant="outline" className="w-full">
              View Item Event 테스트
            </Button>
            <Button onClick={testAddToWishlistEvent} variant="outline" className="w-full">
              Add to Wishlist Event 테스트
            </Button>
            <Button onClick={testBeginCheckoutEvent} variant="outline" className="w-full">
              Begin Checkout Event 테스트
            </Button>
            <Button onClick={testRefundEvent} variant="outline" className="w-full">
              Refund Event 테스트
            </Button>
            <Separator />
            <Button onClick={testViewPromotionEvent} variant="secondary" className="w-full">
              View Promotion Event 테스트
            </Button>
            <Button onClick={testSelectPromotionEvent} variant="secondary" className="w-full">
              Select Promotion Event 테스트
            </Button>
          </CardContent>
        </Card>

        {/* 기존 이벤트 및 기타 */}
        <Card>
          <CardHeader>
            <CardTitle>기존 이벤트 & 기타</CardTitle>
            <CardDescription>기존 이벤트와 사용자 관리 기능</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <Button onClick={testMarketViewEvent} variant="outline" className="w-full">
              Market View Event 테스트
            </Button>
            <Button onClick={testWalletConnectEvent} variant="outline" className="w-full">
              Wallet Connect Event 테스트
            </Button>
            <Button onClick={testUserLoginEvent} variant="outline" className="w-full">
              User Login Event 테스트
            </Button>
            <Separator />
            <Button onClick={testIdentifyUser} variant="secondary" className="w-full">
              사용자 식별 테스트
            </Button>
            <Button onClick={testSetUserProperties} variant="secondary" className="w-full">
              사용자 속성 설정 테스트
            </Button>
            <Button onClick={testCustomEvent} variant="secondary" className="w-full">
              커스텀 이벤트 테스트
            </Button>
            <Separator />
            <Button onClick={enableDebugMode} variant="destructive" className="w-full">
              🐛 디버그 모드 활성화
            </Button>
          </CardContent>
        </Card>

        {/* 직접 gtag 비교 테스트 */}
        <Card>
          <CardHeader>
            <CardTitle>🔥 직접 gtag 비교 테스트</CardTitle>
            <CardDescription>트래킹 서비스 vs 직접 gtag 호출 비교</CardDescription>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="rounded-md border border-yellow-200 bg-yellow-50 p-3">
              <p className="text-sm text-yellow-800">
                이 버튼들은 직접 gtag를 호출합니다. GA 디버거에서 이벤트가 나타나는지 확인해보세요.
              </p>
            </div>
            <Button
              onClick={testDirectGtagPurchase}
              variant="outline"
              className="w-full bg-orange-50 hover:bg-orange-100"
            >
              🔥 DIRECT gtag Purchase 테스트
            </Button>
            <Button
              onClick={testDirectGtagCustom}
              variant="outline"
              className="w-full bg-orange-50 hover:bg-orange-100"
            >
              🔥 DIRECT gtag Custom 테스트
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* 로그 */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>이벤트 로그</CardTitle>
              <CardDescription>최근 전송된 이벤트 로그 (최대 20개)</CardDescription>
            </div>
            <Button onClick={clearLogs} variant="outline" size="sm">
              로그 지우기
            </Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="bg-muted h-64 overflow-y-auto rounded-lg p-4">
            {logs.length === 0 ? (
              <p className="text-muted-foreground text-sm">아직 이벤트가 없습니다.</p>
            ) : (
              <div className="space-y-1 font-mono text-sm">
                {logs.map((log, index) => (
                  <div key={index} className="text-foreground">
                    {log}
                  </div>
                ))}
              </div>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 안내 사항 */}
      <Card>
        <CardHeader>
          <CardTitle>테스트 안내</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-muted-foreground space-y-2 text-sm">
            <p>
              • 이벤트 전송 후 브라우저 개발자 도구의 Network 탭에서 실제 요청을 확인할 수 있습니다.
            </p>
            <p>• Google Analytics: gtag 이벤트 확인</p>
            <p>• Microsoft Clarity: clarity 이벤트 확인</p>
            <p>• 환경변수 설정: NEXT_PUBLIC_GA_MEASUREMENT_ID, NEXT_PUBLIC_CLARITY_PROJECT_ID</p>
            <p>• 디버그 로그: NEXT_PUBLIC_TRACKING_DEBUG=true 설정 시 콘솔에서 확인 가능</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
