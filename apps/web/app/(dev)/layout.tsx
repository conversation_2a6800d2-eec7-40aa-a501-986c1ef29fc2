import { redirect } from 'next/navigation';
import { ReactNode } from 'react';

export default function DevLayout({ children }: { children: ReactNode }) {
  // Only allow access in development or staging environments
  if (process.env.NODE_ENV !== 'development' && process.env.NEXT_PUBLIC_VERCEL_ENV !== 'preview') {
    console.warn('Attempted access to dev route in non-dev/preview environment.');
    redirect('/'); // Or redirect to a 404 page
  }

  console.log(
    'Accessing dev route in environment:',
    process.env.NODE_ENV,
    process.env.NEXT_PUBLIC_VERCEL_ENV
  );

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      {/* Optional: Keep the development area title if desired */}
      {/* <h1 className="mb-4 text-xl font-semibold text-destructive">🚧 Development Only Area 🚧</h1> */}
      {children}
    </div>
  );
}
