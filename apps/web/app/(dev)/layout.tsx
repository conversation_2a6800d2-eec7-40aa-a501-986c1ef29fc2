import { redirect } from 'next/navigation';
import { ReactNode } from 'react';
import Link from 'next/link';
import { Button } from '@repo/ui/components/button';
import { Card, CardContent, CardHeader, CardTitle } from '@repo/ui/components/card';

export default function DevLayout({ children }: { children: ReactNode }) {
  // Only allow access in development or staging environments
  if (process.env.NODE_ENV !== 'development' && process.env.NEXT_PUBLIC_VERCEL_ENV !== 'preview') {
    console.warn('Attempted access to dev route in non-dev/preview environment.');
    redirect('/'); // Or redirect to a 404 page
  }

  console.log(
    'Accessing dev route in environment:',
    process.env.NODE_ENV,
    process.env.NEXT_PUBLIC_VERCEL_ENV
  );

  const devPages = [
    { href: '/api-test', title: 'API 테스트', description: 'API 요청 테스트' },
    { href: '/loading-demo', title: '로딩 데모', description: '로딩 상태 데모' },
    { href: '/popups', title: '팝업', description: '팝업 컴포넌트 테스트' },
    { href: '/tracking-test', title: '트래킹 테스트', description: 'GA4 전자상거래 트래킹 테스트' },
  ];

  return (
    <div className="bg-background min-h-screen">
      {/* Development Header */}
      <div className="bg-muted/40 border-b">
        <div className="container mx-auto px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-2xl">🚧</span>
              <h1 className="text-destructive text-lg font-semibold">Development Area</h1>
            </div>
            <Link href="/">
              <Button variant="outline" size="sm">
                메인으로 돌아가기
              </Button>
            </Link>
          </div>
        </div>
      </div>

      {/* Development Navigation */}
      <div className="container mx-auto px-4 py-6">
        <div className="mb-6">
          <Card>
            <CardHeader>
              <CardTitle>개발 도구</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 md:grid-cols-4">
                {devPages.map(page => (
                  <Link key={page.href} href={page.href}>
                    <Button
                      variant="outline"
                      className="h-auto w-full flex-col items-start p-4 text-left"
                    >
                      <span className="font-medium">{page.title}</span>
                      <span className="text-muted-foreground mt-1 text-xs">{page.description}</span>
                    </Button>
                  </Link>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Page Content */}
        <div className="w-full">{children}</div>
      </div>
    </div>
  );
}
