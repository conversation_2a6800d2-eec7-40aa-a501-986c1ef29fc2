'use client';

import PredictGoLoading from '@/components/icons/predict-go-loading';
import PredictGoSymbol from '@/components/icons/predict-go-symbol';

export default function LoadingDemoPage() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-cyan-50 p-8">
      <div className="mx-auto max-w-4xl">
        <h1 className="mb-8 text-3xl font-bold text-gray-900">PredictGO 로딩 애니메이션 데모</h1>

        {/* 사이즈 비교 */}
        <section className="mb-12">
          <h2 className="mb-6 text-xl font-semibold text-gray-800">다양한 크기</h2>
          <div className="grid grid-cols-2 gap-8 md:grid-cols-3 lg:grid-cols-6">
            <div className="text-center">
              <PredictGoLoading size="small" className="mx-auto mb-2" />
              <p className="text-sm text-gray-600">Small (24x24)</p>
            </div>
            <div className="text-center">
              <PredictGoLoading size="medium" className="mx-auto mb-2" />
              <p className="text-sm text-gray-600">Medium (40x40)</p>
            </div>
            <div className="text-center">
              <PredictGoLoading size="large" className="mx-auto mb-2" />
              <p className="text-sm text-gray-600">Large (64x64)</p>
            </div>
            <div className="text-center">
              <PredictGoLoading size="xl" className="mx-auto mb-2" />
              <p className="text-sm text-gray-600">XL (80x80)</p>
            </div>
            <div className="text-center">
              <PredictGoLoading size="2xl" className="mx-auto mb-2" />
              <p className="text-sm text-gray-600">2XL (96x96)</p>
            </div>
            <div className="text-center">
              <PredictGoLoading size="3xl" className="mx-auto mb-2" />
              <p className="text-sm text-gray-600">3XL (128x128)</p>
            </div>
          </div>
        </section>

        {/* 사용 사례 */}
        <section className="mb-12">
          <h2 className="mb-6 text-xl font-semibold text-gray-800">사용 사례</h2>

          {/* 인라인 로딩 */}
          <div className="mb-8">
            <h3 className="mb-4 text-lg font-medium text-gray-700">인라인 로딩</h3>
            <div className="flex items-center gap-3 rounded-lg bg-white p-4 shadow-sm">
              <PredictGoLoading size="small" />
              <span className="text-gray-700">데이터를 불러오는 중...</span>
            </div>
          </div>

          {/* 버튼 로딩 */}
          <div className="mb-8">
            <h3 className="mb-4 text-lg font-medium text-gray-700">버튼 로딩</h3>
            <button className="flex items-center gap-3 rounded-lg bg-blue-600 px-6 py-3 text-white transition-colors hover:bg-blue-700">
              <PredictGoLoading size="small" />
              예측 생성 중...
            </button>
          </div>

          {/* 센터 로딩 */}
          <div className="mb-8">
            <h3 className="mb-4 text-lg font-medium text-gray-700">센터 로딩</h3>
            <div className="flex h-48 items-center justify-center rounded-lg bg-white shadow-sm">
              <div className="text-center">
                <PredictGoLoading size="large" className="mx-auto mb-4" />
                <p className="text-gray-600">마켓 데이터 로딩 중...</p>
              </div>
            </div>
          </div>
        </section>

        {/* 원본 vs 애니메이션 비교 */}
        <section className="mb-12">
          <h2 className="mb-6 text-xl font-semibold text-gray-800">원본 vs 애니메이션 비교</h2>
          <div className="grid max-w-md grid-cols-2 gap-8">
            <div className="text-center">
              <div className="mx-auto mb-2 flex h-16 w-16 items-center justify-center">
                <PredictGoSymbol />
              </div>
              <p className="text-sm text-gray-600">원본 심볼</p>
            </div>
            <div className="text-center">
              <PredictGoLoading size="large" className="mx-auto mb-2" />
              <p className="text-sm text-gray-600">애니메이션 로딩</p>
            </div>
          </div>
        </section>

        {/* 카드형 로딩 */}
        <section className="mb-12">
          <h2 className="mb-6 text-xl font-semibold text-gray-800">카드형 로딩</h2>
          <div className="grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3">
            {[1, 2, 3].map(i => (
              <div key={i} className="rounded-lg bg-white p-6 shadow-sm">
                <div className="mb-4 flex h-32 items-center justify-center">
                  <PredictGoLoading size="medium" />
                </div>
                <div className="space-y-2">
                  <div className="h-4 animate-pulse rounded bg-gray-200"></div>
                  <div className="h-4 w-3/4 animate-pulse rounded bg-gray-200"></div>
                </div>
              </div>
            ))}
          </div>
        </section>

        {/* 구현 코드 */}
        <section>
          <h2 className="mb-6 text-xl font-semibold text-gray-800">사용법</h2>
          <div className="overflow-x-auto rounded-lg bg-gray-900 p-6">
            <pre className="text-sm text-green-400">
              {`import PredictGoLoading from '@/components/icons/predict-go-loading';

// 기본 사용
<PredictGoLoading />

// 크기 지정
<PredictGoLoading size="small" />   // 24x24
<PredictGoLoading size="medium" />  // 40x40
<PredictGoLoading size="large" />   // 64x64
<PredictGoLoading size="xl" />      // 80x80
<PredictGoLoading size="2xl" />     // 96x96
<PredictGoLoading size="3xl" />     // 128x128

// 추가 클래스 적용
<PredictGoLoading size="medium" className="mx-auto" />`}
            </pre>
          </div>
        </section>
      </div>
    </div>
  );
}
