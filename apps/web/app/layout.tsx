import type { Metadata } from 'next';

import '@repo/ui/globals.css';
import '@/styles/web.css';

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>_Mono, <PERSON> } from 'next/font/google';
import { headers } from 'next/headers';
import { ThemeProvider } from 'next-themes';
import { WagmiContextProvider } from '@/components/providers/wagmi-provider';
import PopupProvider from '@/components/ui/popup/popup.provider';

import { Toaster } from '@repo/ui/components/sonner';
import { TrackingConsent } from '@/components/common/tracking-consent';
import { TanstackQueryClientProvider } from '@/components/providers/tanstack-query-client-provider';
import { SessionManager } from '@/components/common/session-manager';
import { WelcomeFlowManager } from '@/components/ui/popup/welcome-flow-manager';
import { isProd } from '@/lib/env';

const fontSans = Geist({
  subsets: ['latin'],
  variable: '--font-sans',
});

const fontMono = Geist_Mono({
  subsets: ['latin'],
  variable: '--font-mono',
});

const fontInter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
});

export const metadata: Metadata = {
  robots: {
    index: isProd,
    follow: isProd,
  },
  title: 'Predict!Go',
  description:
    'Predict!Go is a Web3-based prediction market platform where you can predict anything and everything. Engage, trade, and earn rewards by forecasting outcomes across sports, politics, entertainment, and more.',
  icons: {
    icon: '/favicon.svg',
  },
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const headersObj = await headers();
  const cookies = headersObj.get('cookie');

  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${fontSans.variable} ${fontMono.variable} ${fontInter.variable} min-w-5xl antialiased`}
      >
        <ThemeProvider>
          <WagmiContextProvider cookies={cookies}>
            <TanstackQueryClientProvider>
              <PopupProvider>
                {children}
                <SessionManager />
                <WelcomeFlowManager />
                <Toaster richColors />
                <TrackingConsent />
              </PopupProvider>
            </TanstackQueryClientProvider>
          </WagmiContextProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
