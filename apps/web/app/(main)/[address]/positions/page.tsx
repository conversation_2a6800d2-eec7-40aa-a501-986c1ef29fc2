'use client';
import PageContainer from '@/components/layouts/page-container';
import {
  BaseTabs,
  BaseTabsContent,
  BaseTabsList,
  BaseTabsTrigger,
} from '@/components/ui/base.tabs';
import { pxToRem } from '@repo/ui/lib/utils';
import UserInfoSection from '@/components/common/user-info-section';
import UserStatsSection from '@/components/common/user-stats-section';
import UserActivitiesTab from '@/components/common/user-activities-tab';
import UserPositionsTab from '@/components/common/user-positions-tab';
import { useParams } from 'next/navigation';
import { Address } from 'viem';

export default function PositionsPage() {
  const params = useParams();
  const address = params.address as Address;

  if (!address) {
    return (
      <PageContainer title="Positions">
        <div className="flex items-center justify-center py-20">
          <div className="text-gray-600">Please connect your wallet</div>
        </div>
      </PageContainer>
    );
  }

  return (
    <PageContainer title="Positions" className="page">
      {/* User Info Section */}
      <UserInfoSection userAddress={address} />

      {/* Values Info Section */}
      <UserStatsSection userAddress={address} />

      {/* Tab Section */}
      <section
        style={
          {
            '--tab-trigger-width': pxToRem(200),
          } as React.CSSProperties
        }
      >
        <BaseTabs defaultValue="positions">
          <div className="border-b-line border-b">
            <BaseTabsList className="border-none">
              <BaseTabsTrigger className="min-w-(--tab-trigger-width)" value="positions">
                Positions
              </BaseTabsTrigger>
              <BaseTabsTrigger className="min-w-(--tab-trigger-width)" value="activity">
                Activity
              </BaseTabsTrigger>
            </BaseTabsList>
          </div>
          <BaseTabsContent value="positions">
            <UserPositionsTab userAddress={address} />
          </BaseTabsContent>
          <BaseTabsContent value="activity">
            <UserActivitiesTab userAddress={address} />
          </BaseTabsContent>
        </BaseTabs>
      </section>
    </PageContainer>
  );
}
