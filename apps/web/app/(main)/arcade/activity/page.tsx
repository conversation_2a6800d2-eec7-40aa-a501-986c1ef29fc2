'use client';

import CommonAvatar from '@/components/ui/avatar-image';
import { usePredictActivities } from '@/hooks/query/activity';
import type { PredictActivity } from '@/hooks/query/activity/use-activity';
import Link from 'next/link';
import React, { useState } from 'react';
import { INNER_LINKS } from '@/lib/constants';

export default function ActivitiesPage() {
  const [page, setPage] = useState(0);
  const [allActivities, setAllActivities] = useState<PredictActivity[]>([]);

  const { data, isLoading, error } = usePredictActivities({
    page,
    limit: 50,
  });

  // 새로운 데이터가 로드되면 기존 활동 목록에 추가
  React.useEffect(() => {
    if (data?.activities) {
      if (page === 0) {
        setAllActivities(data.activities);
      } else {
        setAllActivities(prev => [...prev, ...data.activities]);
      }
    }
  }, [data, page]);

  // 정렬된 활동 목록
  const hasMore = data?.totalLength ? allActivities.length < data.totalLength : false;

  const handleLoadMore = () => {
    setPage(prev => prev + 1);
  };

  if (error) {
    return (
      <div className="px-space-30 py-space-30 mx-auto w-full max-w-[1440px]">
        <div className="py-space-40 flex flex-col items-center justify-center text-center">
          <p className="text-lg font-semibold text-red-500">Error loading activities</p>
          <p className="text-gray-3 text-base">Please try again later</p>
        </div>
      </div>
    );
  }

  return (
    <div className="px-space-30 py-space-30 mx-auto w-full max-w-[1440px]">
      <h1 className="mb-space-30 text-size-lg text-mid-dark font-bold">Activities</h1>

      {/* 활동 목록 헤더 */}
      <div className="text-size-xs px-space-30 text-gray-3 border-b-line py-space-20 flex justify-between border-b">
        <div>Market</div>
        <div>Time</div>
      </div>

      {/* 활동 목록 */}
      <div className="divide-line divide-y">
        {isLoading && page === 0 ? (
          <div className="py-space-40 flex justify-center">
            <p className="text-gray-3">Loading activities...</p>
          </div>
        ) : allActivities.length > 0 ? (
          allActivities.map((activity, index) => (
            <div
              key={`${activity.marketId}-${activity.userAddress}-${activity.timestamp}-${index}`}
              className="hover:bg-gray-1 px-space-30 py-space-20 flex items-center justify-between transition-colors"
            >
              <div className="gap-space-15 flex min-w-0 flex-1 items-center">
                <CommonAvatar
                  imageUrl={activity.marketImageUrl}
                  size="md3"
                  alt={activity.marketTitle}
                />
                <div className="flex min-w-0 flex-1 flex-col">
                  <Link
                    href={INNER_LINKS.MAIN.MARKETS.DETAIL(activity.marketId)}
                    className="text-size-sm text-mid-dark font-semibold hover:underline"
                  >
                    {activity.marketTitle}
                  </Link>
                  <div className="gap-space-6 flex items-center">
                    <CommonAvatar
                      imageUrl={activity.userAvatarUrl}
                      size="sm"
                      alt={activity.userNickname}
                    />
                    <div className="text-size-xs text-mid-dark">
                      <Link
                        href={INNER_LINKS.MAIN.ADDRESS_POSITIONS(activity.userAddress)}
                        className="font-semibold hover:underline"
                      >
                        {activity.userNickname}
                      </Link>
                      {' predicted on '}
                      <span
                        className="font-medium"
                        style={{
                          color:
                            activity.outcomeOrder === 0
                              ? 'var(--color-graph-1)'
                              : 'var(--color-graph-2)',
                        }}
                      >
                        {activity.outcome}
                      </span>
                      {' at '}
                      <span className="font-semibold">{activity.amount}</span>
                    </div>
                  </div>
                </div>
              </div>
              <div className="gap-space-30 flex items-center">
                <span className="text-icon-dark text-size-sm flex-shrink-0 font-semibold">
                  {activity.relativeTime}
                </span>
              </div>
            </div>
          ))
        ) : (
          <div className="py-space-40 flex flex-col items-center justify-center text-center">
            <p className="text-gray-3 text-lg font-semibold">No activities found</p>
            <p className="text-gray-3 text-base">No prediction activities available</p>
          </div>
        )}
      </div>

      {/* Load More 버튼 */}
      {hasMore && (
        <div className="mt-space-30 flex justify-center">
          <button
            onClick={handleLoadMore}
            disabled={isLoading}
            className="px-space-30 py-space-10 bg-primary hover:bg-primary-dark rounded-lg text-white disabled:cursor-not-allowed disabled:opacity-50"
          >
            {isLoading ? 'Loading...' : 'Load More'}
          </button>
        </div>
      )}
    </div>
  );
}
