import { cn } from '@repo/ui/lib/utils';
import SvgIcon from '@/components/icons/svg-icon';
import CommonAvatar from '@/components/ui/avatar-image';
import Link from 'next/link';
import { INNER_LINKS } from '@/lib/constants';

// 금액 포맷팅 함수
export const formatAmount = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

// 사용자 데이터 타입
export interface User {
  id: string;
  username: string;
  avatarUrl: string;
  volume: number;
  profit: number;
  isCurrentUser?: boolean;
}

interface RankUserProps {
  /**
   * 사용자 정보
   */
  user: User;

  /**
   * 사용자의 순위 (1부터 시작)
   */
  rank: number;

  /**
   * 표시할 금액 값
   */
  amount: number;

  /**
   * 금액이 음수일 경우 빨간색으로 표시할지 여부
   */
  showNegativeInRed?: boolean;

  /**
   * 추가 CSS 클래스
   */
  className?: string;
}

// TODO: change text size and color
export default function RankUser({ user, rank, amount, className }: RankUserProps) {
  // 1~3위는 트로피 아이콘 표시, 나머지는 숫자로 표시
  const rankDisplay =
    rank === 1 ? (
      <SvgIcon name="GoldTrophyIcon" />
    ) : rank === 2 ? (
      <SvgIcon name="SilverTrophyIcon" />
    ) : rank === 3 ? (
      <SvgIcon name="BronzeTrophyIcon" />
    ) : (
      <span className="text-dark-deep text-size-xxs">{rank}</span>
    );

  return (
    <div
      className={cn(
        'p-space-10 flex items-center justify-between rounded-lg',
        user.isCurrentUser ? 'bg-sky-50' : 'hover:bg-gray-1',
        className
      )}
    >
      <div className="gap-space-60 flex items-center">
        <div className="w-8 text-center">{rankDisplay}</div>
        <div className="gap-space-20 flex items-center">
          <CommonAvatar imageUrl={user.avatarUrl} size="md" alt={user.username} />
          <Link
            href={INNER_LINKS.MAIN.ADDRESS_POSITIONS(user.username)}
            className={cn(
              'text-size-sm max-w-[120px] truncate font-semibold',
              user.isCurrentUser ? 'text-sky-500' : ''
            )}
          >
            {user.username}
          </Link>
        </div>
      </div>
      <div className="text-size-sm text-icon-dark font-semibold">{formatAmount(amount)}</div>
    </div>
  );
}
