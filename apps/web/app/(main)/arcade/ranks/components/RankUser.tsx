import { cn } from '@repo/ui/lib/utils';
import SvgIcon from '@/components/icons/svg-icon';
import CommonAvatar from '@/components/ui/avatar-image';
import Link from 'next/link';
import { INNER_LINKS } from '@/lib/constants';

interface RankUserProps {
  userAvatarUrl: string;
  userUsername: string;
  isCurrentUser?: boolean;
  rank: number;
  amount: string;
}

export function RankUserSkeleton() {
  return (
    <div className="p-space-10 flex animate-pulse items-center justify-between rounded-lg">
      <div className="gap-space-60 flex items-center">
        <div className="flex w-8 justify-center">
          <div className="h-4 w-4 rounded bg-gray-200"></div>
        </div>
        <div className="gap-space-20 flex items-center">
          <div className="size-10 rounded-full bg-gray-200"></div>
          <div className="space-y-1">
            <div className="h-4 w-20 rounded bg-gray-200"></div>
          </div>
        </div>
      </div>
      <div className="h-4 w-16 rounded bg-gray-200"></div>
    </div>
  );
}

export function RankUserEmptyState({ title }: { title: string }) {
  return (
    <div className="p-space-30 flex flex-col items-center justify-center text-center">
      <SvgIcon name="AlertIcon" className="mb-4 text-gray-400" />
      <h3 className="text-size-lg mb-2 font-semibold text-gray-600">No {title} Data</h3>
      <p className="text-size-sm text-gray-500">
        {title} rankings will appear here once available.
      </p>
    </div>
  );
}

export default function RankUser({
  userAvatarUrl,
  userUsername,
  isCurrentUser,
  rank,
  amount,
}: RankUserProps) {
  const rankDisplay =
    rank === 1 ? (
      <SvgIcon name="GoldTrophyIcon" />
    ) : rank === 2 ? (
      <SvgIcon name="SilverTrophyIcon" />
    ) : rank === 3 ? (
      <SvgIcon name="BronzeTrophyIcon" />
    ) : (
      <span className="text-dark-deep text-size-xxs">{rank}</span>
    );

  return (
    <div
      className={cn(
        'p-space-10 flex items-center justify-between rounded-lg',
        isCurrentUser ? 'bg-sky-50' : 'hover:bg-gray-1'
      )}
    >
      <div className="gap-space-60 flex items-center">
        <div className="w-8 text-center">{rankDisplay}</div>
        <div className="gap-space-20 flex items-center">
          <CommonAvatar imageUrl={userAvatarUrl} size="md" alt={userUsername} />
          <Link
            href={INNER_LINKS.MAIN.ADDRESS_POSITIONS(userUsername)}
            className={cn(
              'text-size-sm max-w-[120px] truncate font-semibold',
              isCurrentUser ? 'text-sky-500' : ''
            )}
          >
            {userUsername}
          </Link>
        </div>
      </div>
      <div className="text-size-sm text-icon-dark font-semibold">${amount}</div>
    </div>
  );
}
