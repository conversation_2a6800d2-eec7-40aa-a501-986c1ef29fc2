'use client';

import type { LeaderboardPeriod } from '@/lib/api/leaderboard/leaderboard.schema.server';
import RankUser, { RankUserSkeleton, RankUserEmptyState } from './RankUser';
import { usePredictLeaderboard } from '@/hooks/query/leaderboard';

interface VolumeColumnProps {
  period: LeaderboardPeriod;
}

export default function RankVolumeColumn({ period }: VolumeColumnProps) {
  const { data, isLoading, error } = usePredictLeaderboard('VOLUME', period);

  return (
    <div className="flex-1 overflow-hidden">
      <div className="p-space-15 gap-space-6 border-line flex items-center border-b">
        <h3 className="font-semibold">Volume</h3>
      </div>

      <div className="p-space-15 space-y-space-15">
        {isLoading ? (
          // Loading skeleton
          Array.from({ length: 5 }).map((_, i) => <RankUserSkeleton key={i} />)
        ) : !data || data.length === 0 ? (
          // Empty state
          <RankUserEmptyState title="Volume" />
        ) : (
          // Data
          data.map(user => (
            <RankUser
              key={user.userAddress}
              userAvatarUrl={user.userAvatarUrl}
              userUsername={user.userUsername}
              rank={user.rank}
              amount={user.amount}
              isCurrentUser={user.isCurrentUser}
            />
          ))
        )}
      </div>
    </div>
  );
}
