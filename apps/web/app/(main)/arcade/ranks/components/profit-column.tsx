'use client';

import { useProfitLeaderboard } from '@/hooks/query/leaderboard/use-leaderboard';
import { LeaderboardPeriod } from '@/lib/api/leaderboard/leaderboard.schema.server';
import RankUser from './RankUser';

interface ProfitColumnProps {
  period: LeaderboardPeriod;
}

export default function ProfitColumn({ period }: ProfitColumnProps) {
  const { data: profitUsers = [], isLoading } = useProfitLeaderboard(period);

  if (isLoading) {
    return (
      <div className="flex-1 overflow-hidden">
        <div className="p-space-15 gap-space-6 border-line flex items-center border-b">
          <h3 className="font-semibold">Profit</h3>
        </div>
        <div className="p-space-15">
          <div className="text-center text-gray-500">Loading...</div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex-1 overflow-hidden">
      <div className="p-space-15 gap-space-6 border-line flex items-center border-b">
        <h3 className="font-semibold">Profit</h3>
      </div>

      <div className="p-space-15 space-y-space-15">
        {profitUsers.map(item => (
          <RankUser
            key={item.userAddress}
            user={{
              id: item.userAddress,
              username: item.userNickname,
              avatarUrl: item.userAvatarUrl || '',
              volume: 0,
              profit: parseFloat(item.userValue.replace(/[$,]/g, '')),
            }}
            rank={item.userRank}
            amount={parseFloat(item.userValue.replace(/[$,]/g, ''))}
            showNegativeInRed={true}
          />
        ))}
      </div>
    </div>
  );
}
