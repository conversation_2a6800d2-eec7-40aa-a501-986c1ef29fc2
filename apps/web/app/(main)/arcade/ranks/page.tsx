'use client';

import {
  BaseTabs,
  BaseTabsContent,
  BaseTabsList,
  BaseTabsTrigger,
} from '@/components/ui/base.tabs';
import RankChannelLeaderboardTab from './components/rank-channel-leaderboard-tab';
import RankPredictLeaderboardTab from './components/rank-predict-leaderboard-tab';

export default function RanksPage() {
  return (
    <div className="page">
      <h1 className="mb-space-20 text-2xl font-bold">Ranks</h1>
      <BaseTabs defaultValue="predict">
        <BaseTabsList className="w-full">
          <BaseTabsTrigger value="predict">Predict</BaseTabsTrigger>
          <BaseTabsTrigger value="channel">Channel</BaseTabsTrigger>
        </BaseTabsList>
        <BaseTabsContent value="predict">
          <RankPredictLeaderboardTab />
        </BaseTabsContent>
        <BaseTabsContent value="channel">
          <RankChannelLeaderboardTab />
        </BaseTabsContent>
      </BaseTabs>
    </div>
  );
}
