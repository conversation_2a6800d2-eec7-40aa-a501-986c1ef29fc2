'use client';
import { BaseButton, GreenButton, InfoButton, NeutralButton } from '@/components/ui/base.button';
import { BaseInput } from '@/components/ui/base.input';
import { DollarInput } from '@/components/ui/dollar-input';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/ui/base.select';
import { BaseTextarea } from '@/components/ui/base.textarea';
import SimpleAlert from '@/components/ui/simple.alert';
import { useCreateMarketFlow, useMarketValidate } from '@/hooks/query/market';
import { useCategories } from '@/hooks/query/category';
import { toAmount } from '@/lib/format';
import { useGlobalStore } from '@/store/global.store';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { toast } from '@repo/ui/components/sonner';
import { pxToRem } from '@repo/ui/lib/utils';
import { useRouter } from 'next/navigation';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import EthicalValidationFailPopup from '@/components/ui/popup/ethical-validation-fail-popup';
import { fillDevData } from '@/lib/utils/dev-data';
import { Popup } from '@/components/ui/popup';
import DepositChannelCollateralButton from '@/components/actions/deposit-channel-collateral-button';
import { useChannelCollateral } from '@/hooks/query/channel';
import { ICON_PATH, INNER_LINKS } from '@/lib/constants';
import Image from 'next/image';
import ExamplePopup from '@/components/ui/popup/example-popup';
import { Checkbox } from '@repo/ui/components/checkbox';

const CreateMarketFormSchema = z
  .object({
    title: z
      .string()
      .min(2, 'Title must be at least 2 characters long')
      .max(100, 'Title cannot exceed 100 characters'),
    description: z
      .string()
      .min(2, 'Description must be at least 2 characters long')
      .max(2000, 'Description cannot exceed 2000 characters'),
    imageUrl: z.string().optional(),
    predictionDeadline: z.number().min(1, 'Please set a prediction end time'),
    resultConfirmDeadline: z.number().min(1, 'Please set a result confirmation end time'),
    category: z.string().min(2, 'Please select a category').max(50, 'Category name is too long'),
    collateralAmount: z.number().min(50, 'Minimum deposit amount is $50'),
    outcomes: z
      .array(z.string().min(1, 'Outcome cannot be empty'))
      .min(2, 'At least 2 outcomes are required')
      .max(10, 'Maximum 10 outcomes allowed'),
    tags: z
      .array(z.string().min(1, 'Tag cannot be empty').max(20, 'Tag cannot exceed 20 characters'))
      .max(3, 'Maximum 3 tags allowed')
      .optional(),
    broadcastURL: z.string().url('Please enter a valid URL format').optional().or(z.literal('')),
    referenceURL: z
      .string()
      .min(1, 'Reference URL is required')
      .url('Please enter a valid URL format'),
    image: z.instanceof(File).optional(),
  })
  .refine(
    data => {
      return data.resultConfirmDeadline > data.predictionDeadline;
    },
    {
      message: 'Result confirmation end time must be later than prediction end time',
      path: ['resultConfirmDeadline'],
    }
  );

export type CreateMarketFormValues = z.infer<typeof CreateMarketFormSchema>;

const PLACEHOLDER_TEXT = {
  deposit: 'Minimum Amount $50',
  category: 'Select Category',
  tag: 'Please create tag(max. 20 characters)',
  'market-title': 'Write a prediction title.(max. 100 characters)',
  'market-outcomes': 'Write an outcome.(max. 50 characters)',
  'broadcast-url':
    'Please enter the URL of a live video related to the prediction.(Youtube, Twitch...)',
  'reference-url': 'Please enter a URL related to the prediction.',
  description:
    'Please write in detail about the explanation of predictions and the rules by which the prediction is resolved.',
  'prediction-end-time': 'YYYY-MM-DD  hh:mm',
  'confirmation-end-time': 'YYYY-MM-DD  hh:mm',
  'dispute-period': '',
};

// 로컬 타임존을 유지하면서 datetime-local 형식으로 변환하는 헬퍼 함수
const formatDateTimeLocal = (timestamp: number): string => {
  const date = new Date(timestamp * 1000);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  return `${year}-${month}-${day}T${hours}:${minutes}`;
};

export default function ChannelsPredictionsCreatePage() {
  const [tags, setTags] = useState<string[]>([]);
  const [currentTag, setCurrentTag] = useState('');
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string>('');
  const [validationId, setValidationId] = useState<string | null>(null);
  const [titleValidationError, setTitleValidationError] = useState<boolean>(false);
  const [showEthicalFailPopup, setShowEthicalFailPopup] = useState<boolean>(false);
  const [showExamplePopup, setShowExamplePopup] = useState<boolean>(false);
  const { data: collateral } = useChannelCollateral();
  const safeSmartAccountAddress = useGlobalStore(v => v.safeSmartAccount?.address);
  const { data: categories, isLoading: isCategoriesLoading } = useCategories();
  const marketValidate = useMarketValidate();
  const createMarketTransaction = useCreateMarketFlow();

  const form = useForm<CreateMarketFormValues>({
    resolver: zodResolver(CreateMarketFormSchema),
    defaultValues: {
      title: '',
      description: '',
      imageUrl: '',
      predictionDeadline: 0,
      resultConfirmDeadline: 0,
      category: '',
      collateralAmount: undefined,
      outcomes: ['', ''],
      tags: [],
      broadcastURL: '',
      referenceURL: '',
    },
  });

  const { watch, setValue } = form;
  const outcomes = watch('outcomes');

  const addOutcome = () => {
    const currentOutcomes = watch('outcomes');
    if (currentOutcomes.length < 10) {
      setValue('outcomes', [...currentOutcomes, '']);
    }
  };

  const removeOutcome = (index: number) => {
    const currentOutcomes = watch('outcomes');
    if (currentOutcomes.length > 2) {
      const newOutcomes = currentOutcomes.filter((_, i) => i !== index);
      setValue('outcomes', newOutcomes);
    }
  };

  const updateOutcome = (index: number, value: string) => {
    const currentOutcomes = watch('outcomes');
    const newOutcomes = [...currentOutcomes];
    newOutcomes[index] = value;
    setValue('outcomes', newOutcomes);
  };

  const addTag = () => {
    if (currentTag.trim() && tags.length < 3 && !tags.includes(currentTag.trim())) {
      const newTags = [...tags, currentTag.trim()];
      setTags(newTags);
      setValue('tags', newTags);
      setCurrentTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    const newTags = tags.filter(tag => tag !== tagToRemove);
    setTags(newTags);
    setValue('tags', newTags);
  };

  const handleTagKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      addTag();
    }
  };

  const handleEthicalReview = async (e: React.MouseEvent<HTMLButtonElement>) => {
    e.preventDefault();
    const title = form.getValues('title');
    if (!title.trim()) {
      toast.error('Please enter a title.');
      return;
    }

    try {
      setTitleValidationError(false);
      setShowEthicalFailPopup(false); // 팝업 닫기 시도
      const validationResult = await marketValidate.mutateAsync({
        title: title,
      });

      if (!validationResult.isEthical) {
        setTitleValidationError(true);
        setShowEthicalFailPopup(true); // 팝업 열기
        return;
      }

      if (!validationResult.validationId) {
        toast.error('Failed to get validation ID.');
        setTitleValidationError(true);
        return;
      }

      setValidationId(validationResult.validationId);
      toast.success('Ethical review completed.');
    } catch (error) {
      console.error('Ethical review failed:', error);
      setTitleValidationError(true);
      if (error instanceof Error) {
        toast.error('Ethical review failed: ' + error.message);
      } else {
        toast.error('Ethical review failed.');
      }
    }
  };

  const handleEthicalFailPopupClose = () => {
    setShowEthicalFailPopup(false);
    setTitleValidationError(true);
    setValidationId(null);
  };

  // 개발용 자동 데이터 채우기 함수
  const handleFillDevData = () => {
    fillDevData(form);
    // 태그 상태도 초기화
    setTags([]);
    setCurrentTag('');
    // 검증 상태 초기화
    setValidationId(null);
    setTitleValidationError(false);
  };

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    if (!file.type.startsWith('image/')) {
      toast.error('Please select an image file');
      return;
    }

    if (file.size > 1 * 1024 * 1024) {
      toast.error('File size must be less than 1MB');
      return;
    }

    setImageFile(file);
    const reader = new FileReader();
    reader.onload = e => {
      const result = e.target?.result as string;
      setImagePreview(result);
    };
    reader.readAsDataURL(file);
  };

  async function onSubmit(values: CreateMarketFormValues) {
    if (!safeSmartAccountAddress) {
      return;
    }

    // Check if collateralAmount exceeds available collateral
    const maxAllowedAmount = collateral?.available ? parseFloat(collateral.available) : 0;
    if (values.collateralAmount > maxAllowedAmount) {
      toast.error(
        `The collateral amount cannot exceed available collateral (${maxAllowedAmount.toLocaleString()}).`
      );
      return;
    }

    // validationId가 없으면 에러
    if (!validationId) {
      toast.error('Get the ethical review first.');
      return;
    }

    const filteredData = {
      ...values,
      outcomes: values.outcomes.filter(outcome => outcome.trim() !== ''),
      tags: values.tags?.filter(tag => tag.trim() !== '') || [],
    };

    const reqData = {
      maker: safeSmartAccountAddress,
      channelId: safeSmartAccountAddress,
      title: filteredData.title,
      description: filteredData.description,
      predictionDeadline: filteredData.predictionDeadline * 1000,
      resultConfirmDeadline: filteredData.resultConfirmDeadline * 1000,
      disputedPeriod: '30m',
      category: filteredData.category,
      collateralAmount: BigInt(toAmount(filteredData.collateralAmount)),
      outcomes: filteredData.outcomes,
      tags: filteredData.tags,
      referenceURL: filteredData.referenceURL,
      validationId: validationId,
    };

    await createMarketTransaction.mutateAsync({
      ...reqData,
      image: imageFile || undefined,
    });
  }

  const [isTermsAccepted, setIsTermsAccepted] = useState(false);
  return (
    <div className="page p-4">
      {process.env.NODE_ENV === 'development' && (
        <div className="mb-4 flex justify-end">
          <BaseButton
            type="button"
            variant="outline"
            size="sm"
            onClick={handleFillDevData}
            className="border-yellow-300 bg-yellow-100 text-yellow-800 hover:bg-yellow-200"
          >
            🎲 Fill Random Data
          </BaseButton>
        </div>
      )}
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="gap-space-30 flex w-full flex-col">
          <section data-role="section" className="gap-space-30 flex items-center">
            <button
              type="button"
              data-role="upload-image"
              className="relative size-[80px]"
              onClick={() => document.getElementById('image-upload')?.click()}
            >
              <div
                className="bg-gray-2 border-line h-full w-full rounded-full border"
                style={{
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  backgroundImage: `url('${imagePreview}')`,
                }}
              ></div>
              <div
                style={{
                  right: '0px',
                  bottom: '0px',
                }}
                className="bg-sky absolute flex size-[24px] items-center justify-center rounded-full"
              >
                <svg
                  width="10"
                  height="10"
                  viewBox="0 0 10 10"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M3.86229 9.5V0.5H6.13771V9.5H3.86229ZM0.5 6.13771V3.86229H9.5V6.13771H0.5Z"
                    fill="white"
                  />
                </svg>
              </div>
              <input
                id="image-upload"
                type="file"
                accept="image/*"
                onChange={handleImageUpload}
                className="hidden"
              />
            </button>
            <div className="text-sm text-gray-500">
              <div className="mb-1">
                Please upload a representative image for the prediction to be generated. If not
                uploaded, it will be replaced with a default image.
              </div>
              <div>
                At least <b>80px X 80px</b> recommended.
                <br />
                <b>JPG or PNG</b> is allowed. File size up to <b>1MB.</b>
              </div>
            </div>
          </section>

          <section data-role="section" className="gap-space-30 flex items-start">
            <div className="flex flex-[0.25] flex-col">
              <FormField
                control={form.control}
                name="collateralAmount"
                render={({ field }) => (
                  <FormItem data-role="write-deposit" className="space-y-3">
                    <div className="flex items-center justify-between">
                      <FormLabel className="block text-sm font-semibold">Deposit</FormLabel>
                      <div className="gap-space-10 flex items-center">
                        <div className="text-size-xxs text-gray-3 font-medium">
                          Deposit Balance ${!collateral ? '...' : collateral?.available}
                        </div>
                        <DepositChannelCollateralButton
                          style={{
                            height: '20px',
                            borderRadius: '4px',
                            fontSize: '10px',
                            width: pxToRem(80),
                          }}
                          variant="dark"
                        >
                          Add Deposit
                        </DepositChannelCollateralButton>
                      </div>
                    </div>
                    <FormControl>
                      <DollarInput
                        placeholder={PLACEHOLDER_TEXT.deposit}
                        value={field.value || 0}
                        onChange={field.onChange}
                        maxValue={collateral?.available ? parseFloat(collateral.available) : 0}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="py-space-10 px-[5px]">
                <SimpleAlert description="Twice the set deposit amount is the maximum volume for each option." />
              </div>
            </div>

            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem data-role="select-category" className="flex-[0.25] space-y-3">
                  <FormLabel className="block text-sm font-semibold">Category</FormLabel>
                  <FormControl>
                    <BaseSelect onValueChange={field.onChange} value={field.value}>
                      <BaseSelectTrigger className="bg-gray-2 w-full">
                        <BaseSelectValue placeholder={PLACEHOLDER_TEXT.category} />
                      </BaseSelectTrigger>
                      <BaseSelectContent>
                        {isCategoriesLoading ? (
                          <BaseSelectItem value="undefined" disabled>
                            Loading categories...
                          </BaseSelectItem>
                        ) : (
                          categories?.map(category => (
                            <BaseSelectItem
                              key={category.categoryName}
                              value={category.categoryName}
                            >
                              {category.categoryName}
                            </BaseSelectItem>
                          ))
                        )}
                      </BaseSelectContent>
                    </BaseSelect>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex flex-[0.5] flex-col">
              <div data-role="write-tag" className="space-y-3">
                <div className="flex items-center">
                  <label className="block text-sm font-semibold">Tags</label>
                  &nbsp;
                  <span className="text-size-xs text-yes-green font-medium">
                    (Up to 3 tags allowed)
                  </span>
                </div>
                <BaseInput
                  placeholder={PLACEHOLDER_TEXT.tag}
                  value={currentTag}
                  onChange={e => setCurrentTag(e.target.value)}
                  onKeyDown={handleTagKeyPress}
                  disabled={tags.length >= 3}
                  maxLength={20}
                />
                <div className="flex flex-wrap gap-2">
                  {tags.map((tag, index) => (
                    <div
                      key={index}
                      className="rounded-round-md px-space-10 bg-gray-1 border-line flex h-(--tag-height) items-center border"
                    >
                      <span className="text-size-xs text-gray-3 mr-2">{tag}</span>
                      <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        className="text-gray-500 hover:text-gray-700"
                        aria-label="Remove tag"
                      >
                        <svg
                          width="12"
                          height="12"
                          viewBox="0 0 12 12"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <path
                            d="M9 3L3 9M9 9L3 3"
                            stroke="#3B424B"
                            strokeWidth="2"
                            strokeLinecap="round"
                          />
                        </svg>
                      </button>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </section>

          <section data-role="section" className="">
            <header className="mb-space-20 text-size-sm flex items-center justify-between font-medium">
              <div>Quest</div>
              <div className="gap-space-10 flex items-center">
                <p className="text-size-xs text-yes-green">
                  The Invalid Outcome is automatically added during the resolution phase, so you
                  don't need to add it manually.
                </p>
                <NeutralButton type="button">
                  <Image src={ICON_PATH.LEARN_MORE} alt="Learn More" width={16} height={16} />
                  Learn More
                </NeutralButton>
              </div>
            </header>
            <div className="space-y-4">
              <div className="gap-space-20 flex items-center justify-between">
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem data-role="write-market-title" className="flex-1 space-y-2">
                      <FormControl>
                        <BaseInput
                          placeholder={PLACEHOLDER_TEXT['market-title']}
                          {...field}
                          className={
                            titleValidationError ? 'border-red-500 focus:border-red-500' : ''
                          }
                          onChange={e => {
                            field.onChange(e);
                            if (titleValidationError) {
                              setTitleValidationError(false);
                            }
                            if (validationId) {
                              setValidationId(null);
                            }
                          }}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                <GreenButton
                  type="button"
                  onClick={handleEthicalReview}
                  disabled={marketValidate.isPending || !!validationId}
                >
                  {marketValidate.isPending ? (
                    <div className="flex items-center gap-2">
                      <div className="h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent"></div>
                      Reviewing...
                    </div>
                  ) : validationId ? (
                    <div className="flex items-center gap-2">
                      <svg
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                      >
                        <path
                          d="M20 6L9 17L4 12"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        />
                      </svg>
                      Reviewed
                    </div>
                  ) : (
                    'Ethical Review'
                  )}
                </GreenButton>
              </div>
              <div data-role="market-outcomes" className="gap-space-10 flex flex-col">
                <div className="gap-space-10 flex flex-col">
                  {outcomes.map((outcome, index) => (
                    <div key={index} className="gap-space-10 flex items-center">
                      <div className="text-size-xs font-semibold">{index + 1}</div>
                      <div className="relative flex-1">
                        <BaseInput
                          className="pr-10"
                          placeholder={PLACEHOLDER_TEXT['market-outcomes']}
                          value={outcome}
                          onChange={e => updateOutcome(index, e.target.value)}
                        />
                        {outcomes.length > 2 && (
                          <button
                            type="button"
                            onClick={() => removeOutcome(index)}
                            className="absolute top-1/2 right-2 -translate-y-1/2 rounded-sm p-1 hover:bg-gray-100"
                          >
                            <svg
                              width="16"
                              height="16"
                              viewBox="0 0 16 16"
                              fill="none"
                              xmlns="http://www.w3.org/2000/svg"
                            >
                              <rect width="16" height="16" rx="8" fill="#3B424B" />
                              <path
                                d="M11.129 4.8712C11.3788 5.12101 11.3788 5.52649 11.129 5.7763L8.9051 8.00022L11.129 10.2241C11.3788 10.4739 11.3788 10.8794 11.129 11.1292C10.8792 11.379 10.4737 11.379 10.2239 11.1292L8 8.90532L5.77608 11.1292C5.52627 11.379 5.12079 11.379 4.87098 11.1292C4.62104 10.8793 4.62117 10.4739 4.87098 10.2241L7.0949 8.00022L4.87098 5.7763C4.62104 5.52636 4.62118 5.12101 4.87098 4.8712C5.12079 4.6214 5.52614 4.62126 5.77608 4.8712L8 7.09512L10.2239 4.8712C10.4737 4.6214 10.8791 4.62126 11.129 4.8712Z"
                                fill="white"
                              />
                            </svg>
                          </button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
                {outcomes.length < 10 && (
                  <BaseButton
                    type="button"
                    variant="neutral"
                    size="sm"
                    className="text-size-xs px-space-10 w-[125px] justify-center self-end"
                    onClick={addOutcome}
                  >
                    <div>
                      <Image src={ICON_PATH.BTN_ICON} alt="Add Outcome" width={20} height={20} />
                    </div>
                    Add Outcome
                  </BaseButton>
                )}
              </div>
            </div>
          </section>

          <section data-role="section" className="space-y-space-30">
            <FormField
              control={form.control}
              name="broadcastURL"
              render={({ field }) => (
                <FormItem data-role="write-broadcast-url" className="space-y-space-15">
                  <div className="flex items-center">
                    <FormLabel className="text-sm font-semibold">Broadcast Video URL</FormLabel>
                    &nbsp;
                    <span className="text-yes-green text-size-xs">(Optional)</span>
                  </div>
                  <FormControl>
                    <BaseInput placeholder={PLACEHOLDER_TEXT['broadcast-url']} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="referenceURL"
              render={({ field }) => (
                <FormItem data-role="write-reference-url" className="space-y-space-15">
                  <FormLabel className="block text-sm font-semibold">Reference URL</FormLabel>
                  <FormControl>
                    <BaseInput placeholder={PLACEHOLDER_TEXT['reference-url']} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem data-role="write-description" className="space-y-space-15">
                  <div className="flex items-center justify-between">
                    <FormLabel className="block text-sm font-semibold">Description</FormLabel>
                    <div className="gap-space-10 flex items-center">
                      <p className="text-size-xs text-yes-green">
                        Current estimates apply, but final odds lock in upon betting closure.
                      </p>
                      <NeutralButton
                        fontSize="xs"
                        className="px-space-10"
                        onClick={e => {
                          e.preventDefault();
                          setShowExamplePopup(true);
                        }}
                      >
                        <Image src={ICON_PATH.EXAMPLE_DARK} alt="Example" width={16} height={16} />
                        Example
                      </NeutralButton>
                    </div>
                  </div>
                  <FormControl>
                    <BaseTextarea placeholder={PLACEHOLDER_TEXT.description} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </section>

          <section data-role="section" className="gap-space-30 flex items-start">
            <FormField
              control={form.control}
              name="predictionDeadline"
              render={({ field }) => (
                <FormItem
                  data-role="select-prediction-end-time"
                  className="space-y-space-15 w-full max-w-[320px]"
                >
                  <FormLabel className="block text-sm font-medium">Prediction end</FormLabel>
                  <FormControl>
                    <BaseInput
                      {...field}
                      type="datetime-local"
                      placeholder={PLACEHOLDER_TEXT['prediction-end-time']}
                      value={field.value ? formatDateTimeLocal(field.value) : ''}
                      onChange={e => {
                        const dateValue = e.target.value;
                        const seconds = dateValue
                          ? Math.floor(new Date(dateValue).getTime() / 1000)
                          : 0;
                        field.onChange(seconds);
                      }}
                    />
                  </FormControl>
                  <SimpleAlert description="Prediction is not possible after the set time has passed." />
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="resultConfirmDeadline"
              render={({ field }) => (
                <FormItem
                  data-role="select-confirmation-end-time"
                  className="space-y-space-15 w-full max-w-[320px]"
                >
                  <FormLabel className="block text-sm font-medium">
                    Result confirmation end
                  </FormLabel>
                  <FormControl>
                    <BaseInput
                      {...field}
                      type="datetime-local"
                      placeholder={PLACEHOLDER_TEXT['confirmation-end-time']}
                      value={field.value ? formatDateTimeLocal(field.value) : ''}
                      onChange={e => {
                        const dateValue = e.target.value;
                        const predictionDeadline = form.getValues('predictionDeadline');

                        // Check if prediction end time is set first
                        if (!predictionDeadline || predictionDeadline === 0) {
                          toast.error('Please set the prediction end time first.');
                          return;
                        }

                        const seconds = dateValue
                          ? Math.floor(new Date(dateValue).getTime() / 1000)
                          : 0;

                        // Check if result confirmation end is after prediction end
                        if (dateValue && seconds <= predictionDeadline) {
                          toast.error(
                            'Result confirmation end time must be later than prediction end time.'
                          );
                          return;
                        }

                        field.onChange(seconds);
                      }}
                    />
                  </FormControl>
                  <SimpleAlert description="If the result are not confirmed by this time, it will automatically be set to 'Invalid,' and you may lose your deposit." />
                  <FormMessage />
                </FormItem>
              )}
            />

            <div
              data-role="select-dispute-period"
              className="space-y-space-15 w-full max-w-[340px]"
            >
              <label className="block text-sm font-medium">Disputed Period</label>
              <div className="w-full">
                <div className="rounded-md border border-gray-300 bg-gray-50 px-3 py-2 text-sm text-gray-500">
                  30 minutes
                </div>
              </div>
              <SimpleAlert description="After the verifier confirms the result, it is the time during which an objection to the result can be filed. Users who wish to raise an objection must deposit a bond equal to the verifier's bond. If the dispute is upheld, the channel leader must determine the final result." />
            </div>
          </section>

          <div className="my-8 border-t border-gray-200"></div>

          <div>
            <div className="flex flex-row items-start space-y-0 space-x-3">
              <Checkbox
                checked={isTermsAccepted}
                id="terms-accepted"
                onCheckedChange={v => {
                  setIsTermsAccepted(v === 'indeterminate' ? false : v);
                }}
              />
              <div className="space-y-1 leading-none select-none">
                <label htmlFor="terms-accepted" className="text-sm font-normal">
                  I agree to the
                  <span className="font-semibold underline">&nbsp;Terms of Use&nbsp;</span>
                  of prediction creation and deposit system.
                </label>
              </div>
            </div>
          </div>

          <div className="mt-8 flex space-x-4">
            <InfoButton
              type="submit"
              size="lg"
              fontSize="base"
              width={pxToRem(256)}
              disabled={createMarketTransaction.isPending || !isTermsAccepted}
            >
              {createMarketTransaction.isPending ? 'Creating...' : 'Create Prediction'}
            </InfoButton>
          </div>
        </form>
      </Form>

      {showEthicalFailPopup && (
        <Popup isOpen={showEthicalFailPopup} onClose={handleEthicalFailPopupClose}>
          <EthicalValidationFailPopup onEditQuestion={handleEthicalFailPopupClose} />
        </Popup>
      )}

      {showExamplePopup && (
        <ExamplePopup isOpen={showExamplePopup} onClose={() => setShowExamplePopup(false)} />
      )}
    </div>
  );
}
