import {
  BaseTabs,
  BaseTabsContent,
  BaseTabsList,
  BaseTabsTrigger,
} from '@/components/ui/base.tabs';
import { pxToRem } from '@repo/ui/lib/utils';
import ActivityTab from './tabs/market-activities-tab';
import MarketCommentsTab from './tabs/market-comment-tab';
import TopPredictorsTab from './tabs/market-top-predictors-tab';

interface MarketTabsProps {
  marketId: string;
}

export default function MarketTabs({ marketId }: MarketTabsProps) {
  return (
    <div
      style={
        {
          '--tab-trigger-width': pxToRem(200),
        } as React.CSSProperties
      }
      className="mt-space-30"
    >
      <BaseTabs defaultValue="comments">
        <div className="border-b-line border-b">
          <BaseTabsList className="border-none">
            <BaseTabsTrigger className="min-w-(--tab-trigger-width)" value="comments">
              Comments
            </BaseTabsTrigger>
            <BaseTabsTrigger className="min-w-(--tab-trigger-width)" value="predictors">
              Top Predictors
            </BaseTabsTrigger>
            <BaseTabsTrigger className="min-w-(--tab-trigger-width)" value="activity">
              Activities
            </BaseTabsTrigger>
          </BaseTabsList>
        </div>
        <BaseTabsContent value="comments" className="pt-space-20">
          <MarketCommentsTab marketId={marketId} />
        </BaseTabsContent>
        <BaseTabsContent value="predictors" className="pt-space-20">
          <TopPredictorsTab />
        </BaseTabsContent>
        <BaseTabsContent value="activity" className="pt-space-20">
          <ActivityTab marketId={marketId} />
        </BaseTabsContent>
      </BaseTabs>
    </div>
  );
}
