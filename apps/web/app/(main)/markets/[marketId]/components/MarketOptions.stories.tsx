import type { Meta, StoryObj } from '@storybook/react';
import MarketOptions from './MarketOptions';

const meta = {
  title: 'Markets/MarketOptions',
  component: MarketOptions,
} satisfies Meta<typeof MarketOptions>;

export default meta;
type Story = StoryObj<typeof MarketOptions>;

// Binary prediction story
export const BinaryPrediction: Story = {
  args: {
    predictionType: 'binary',
    yesPercentage: 65,
    noPercentage: 35,
    handleClick: selection => console.log('Selected:', selection),
  },
};

// Multiple options story
const mockOptions = [
  { text: 'Option 1', percentage: '30%', volume: '$100' },
  { text: 'Option 2', percentage: '45%', volume: '$200' },
  { text: 'Option 3', percentage: '15%', volume: '$300' },
  { text: 'Option 4', percentage: '10%', volume: '$400' },
];

export const MultipleOptions: Story = {
  args: {
    predictionType: 'multiple',
    options: mockOptions,
    handleClick: selection => console.log('Selected:', selection),
  },
};

// No data state
export const NoData: Story = {
  args: {
    predictionType: 'multiple',
    options: [],
    handleClick: () => {},
  },
};

// With custom className
export const WithCustomClass: Story = {
  args: {
    ...BinaryPrediction.args,
    className: 'max-w-md mx-auto',
  },
};
