import React, { useState, useEffect } from 'react';
import { cn } from '@repo/ui/lib/utils';
import Link from 'next/link';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/ui/base.select';
import CommonAvatar from '@/components/ui/avatar-image';
import { useMarketActivities } from '@/hooks/query/market/use-market-activities';
import { useMarketById } from '@/hooks/query/market';

// 선택지 타입
type Option = {
  id: string;
  label: string;
  colorName?: string;
};

// 정렬 옵션 타입
type SortOption = 'latest' | 'volume';

// 정렬 옵션 목록
const sortOptions = [
  { value: 'latest', label: 'Latest' },
  { value: 'volume', label: 'Volume' },
];

interface ActivityTabProps {
  className?: string;
  marketId: string;
}

export default function ActivityTab({ className, marketId }: ActivityTabProps) {
  // Get market data to build options list
  const { data } = useMarketById(marketId);

  // Build options list from market outcomes (no "all" option)
  const marketOptions: Option[] =
    data?.marketOutcomes.map((outcome, index) => ({
      id: outcome.outcome,
      label: outcome.outcome,
      colorName: `graph-${(index % 4) + 1}` as const, // Cycle through graph colors
    })) || [];

  // Set default selected option to the first outcome
  const [selectedOption, setSelectedOption] = useState<string>(marketOptions[0]?.id || '');
  const [sortBy, setSortBy] = useState<SortOption>('latest');

  // Update selectedOption when marketOptions change and selectedOption is empty
  useEffect(() => {
    if (marketOptions.length > 0 && !selectedOption && marketOptions[0]) {
      setSelectedOption(marketOptions[0].id);
    }
  }, [marketOptions, selectedOption]);

  // Get activities data for the selected option
  const {
    data: activitiesData,
    isLoading,
    error,
  } = useMarketActivities(marketId, selectedOption, {
    page: 0,
    limit: 50,
  });

  // Transform activities data for UI
  const activities = activitiesData?.activities || [];

  // Sort activities (no filtering needed since we get data for specific outcome)
  const sortedActivities = [...activities].sort((a, b) => {
    if (sortBy === 'latest') {
      return new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime();
    } else {
      // Extract numeric value from formatted amount for volume sorting
      const amountA = parseFloat(a.amount.replace(/[$,]/g, '')) || 0;
      const amountB = parseFloat(b.amount.replace(/[$,]/g, '')) || 0;
      return amountB - amountA;
    }
  });

  if (error) {
    return (
      <div className={cn('', className)}>
        <div className="py-space-40 flex flex-col items-center justify-center text-center">
          <p className="text-gray-3 text-lg font-semibold">Failed to load activities</p>
          <p className="text-gray-3 text-base">Please try again later</p>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('', className)}>
      {/* 필터 영역 */}
      <div className="mb-space-20 gap-space-20 flex">
        {/* 정렬 필터 */}
        <BaseSelect value={sortBy} onValueChange={value => setSortBy(value as SortOption)}>
          <BaseSelectTrigger className="w-[150px]">
            <BaseSelectValue placeholder="Sort by" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {sortOptions.map(option => (
              <BaseSelectItem key={option.value} value={option.value}>
                {option.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>

        {/* 선택지 필터 */}
        <BaseSelect value={selectedOption} onValueChange={value => setSelectedOption(value)}>
          <BaseSelectTrigger className="w-[180px]">
            <BaseSelectValue placeholder="Filter by option" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {marketOptions.map(option => (
              <BaseSelectItem key={option.id} value={option.id}>
                {option.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>
      </div>

      {/* 활동 목록 */}
      {isLoading ? (
        <div className="py-space-40 flex items-center justify-center">
          <p className="text-gray-3 text-base">Loading activities...</p>
        </div>
      ) : sortedActivities.length > 0 ? (
        <div className="gap-space-20 flex flex-col">
          <div className="text-size-xs text-gray-3 border-b-line py-space-30 flex justify-between border-b">
            <div>Activity</div>
            <div>Period</div>
          </div>

          {sortedActivities.map((activity, index) => {
            // Get the primary outcome for display
            const primaryOutcome = activity.outcomes[0];
            const option =
              marketOptions.find(opt => opt.id === primaryOutcome?.outcome) || marketOptions[0];

            return (
              <div
                key={`${activity.user.address}-${activity.timestamp}-${index}`}
                className="hover:bg-gray-1 rounded-round-sm flex items-center justify-between transition-colors"
              >
                <div className="gap-space-15 flex min-w-0 flex-1 items-center">
                  <CommonAvatar
                    imageUrl={activity.user.imageUrl || '/default-avatar.svg'}
                    size="md"
                    alt={activity.user.nickname}
                  />
                  <div className="min-w-0 flex-1">
                    <div className="text-size-sm text-mid-dark">
                      <Link
                        href={`/${activity.user.address}/positions`}
                        className="font-semibold hover:underline"
                      >
                        {activity.user.nickname}
                      </Link>
                      {' predicted on '}
                      <span
                        className="font-medium"
                        style={{
                          color: option?.colorName ? `var(--color-${option.colorName})` : undefined,
                        }}
                      >
                        &ldquo;{primaryOutcome?.outcome || 'Unknown'}&rdquo;
                      </span>
                      {' at '}
                      <span className="font-semibold">{activity.amount}</span>
                    </div>
                  </div>
                </div>
                <div className="text-icon-dark text-size-sm flex-shrink-0 font-semibold">
                  {activity.relativeTime}
                </div>
              </div>
            );
          })}
        </div>
      ) : (
        <div className="py-space-40 flex flex-col items-center justify-center text-center">
          <p className="text-gray-3 text-lg font-semibold">No activity yet</p>
          <p className="text-gray-3 text-base">Predict the result first!</p>
        </div>
      )}
    </div>
  );
}
