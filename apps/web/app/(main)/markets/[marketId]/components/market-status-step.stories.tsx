import type { Meta, StoryObj } from '@storybook/react';
import MarketStatusStep from './market-status-step';

const meta: Meta<typeof MarketStatusStep> = {
  title: 'MarketStatusStep',
  component: MarketStatusStep,
};

export default meta;
type Story = StoryObj<typeof MarketStatusStep>;

export const Default: Story = {
  args: {
    currentStatus: 'predict',
  },
};

export const Predict: Story = {
  args: {
    currentStatus: 'predict',
  },
};

export const ReviewingPrediction: Story = {
  args: {
    currentStatus: 'review',
  },
};

export const OutcomeProposed: Story = {
  args: {
    currentStatus: 'proposed',
  },
};

export const Disputed: Story = {
  args: {
    currentStatus: 'disputed',
  },
};

export const PredictionClose: Story = {
  args: {
    currentStatus: 'closed',
  },
};
