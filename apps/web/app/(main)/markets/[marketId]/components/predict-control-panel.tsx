import PredictButton from '@/components/actions/predict-button';
import MarketCountdown from '@/components/common/market-countdown';
import PredictionMarketCardStatus from '@/components/common/prediction-market-card-status';
import SvgIcon from '@/components/icons/svg-icon';
import CommonAvatar from '@/components/ui/avatar-image';
import { DollarInput } from '@/components/ui/dollar-input';
import { useMyUSDCBalance } from '@/hooks/use-usdc-balance';
import { useMarketById } from '@/hooks/query/market';
import { cn } from '@repo/ui/lib/utils';
import { useCallback, useState } from 'react';
import type { MarketOutcome } from '@/lib/api/market/market.transform';
import { Tooltip, TooltipTrigger, TooltipContent } from '@repo/ui/components/tooltip';

interface PredictControlPanelProps {
  marketId: string;
  selectedOutcome?: MarketOutcome | null;
}

export default function PredictControlPanel({
  marketId,
  selectedOutcome,
}: PredictControlPanelProps) {
  const { balance: userBalance } = useMyUSDCBalance();
  const { data: marketData, isLoading, isError } = useMarketById(marketId);
  const [amount, setAmount] = useState<number>(0);
  const userBalanceNumber = userBalance ? parseFloat(userBalance) : 0;

  if (isLoading) {
    return <div className="p-space-30">로딩 중...</div>;
  }

  if (isError || !marketData) {
    return <div className="p-space-30">마켓 정보를 불러올 수 없습니다.</div>;
  }

  // Use selectedOutcome or fall back to first outcome
  const currentOutcome = selectedOutcome ?? marketData.marketOutcomes[0];

  if (!currentOutcome) {
    return <div className="p-space-30">선택된 결과가 없습니다.</div>;
  }

  const currentRawOutcomeVolume = marketData.marketOutcomes.find(
    o => o.order === currentOutcome.order
  )?.rawVolume;

  if (currentRawOutcomeVolume === undefined) {
    return <div className="p-space-30">결과 데이터를 찾을 수 없습니다.</div>;
  }

  const availableToPredict = marketData.marketMaxOutcomeVolume
    .minus(currentRawOutcomeVolume)
    .dividedBy(1000000)
    .toNumber();

  const handleAmountChange = useCallback((value: number) => {
    setAmount(value);
  }, []);

  const handleAddAmount = useCallback(
    (addValue: number) => {
      const newAmount = amount + addValue;
      if (newAmount <= userBalanceNumber) {
        setAmount(newAmount);
      }
    },
    [amount, userBalanceNumber]
  );

  const handleMaxAmount = useCallback(() => {
    setAmount(Math.min(userBalanceNumber, availableToPredict));
  }, [userBalanceNumber, availableToPredict]);

  const handlePredictSuccess = useCallback(() => {
    setAmount(0); // Reset amount after successful prediction
  }, []);

  const percentage = currentOutcome.rawVolume
    .dividedBy(marketData.marketMaxOutcomeVolume)
    .multipliedBy(100)
    .toNumber();

  return (
    <div className={cn('flex flex-col')}>
      <h1 className="pb-space-30 dashboard-h2">Prediction</h1>

      {/* Market Info */}
      <div className="mb-space-30 flex items-center">
        <div className="mr-space-15 flex-shrink-0">
          <CommonAvatar
            imageUrl={marketData.marketAvatarImageUrl}
            size="md2"
            alt={marketData.marketTitle}
          />
        </div>
        <div className="flex-1">
          <h2 className="text-size-sm text-dark line-clamp-1 font-semibold">
            {marketData.marketTitle}
          </h2>
          {currentOutcome.outcome && (
            <div className="gap-space-6 text-size-xs flex items-center">
              <span className="text-mid-dark font-semibold">{currentOutcome.outcome}</span>
              {currentOutcome.formattedVolume && (
                <span className="text-gray-3">({currentOutcome.formattedVolume})</span>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Available to Predict */}
      <div className="mb-space-30">
        <div className="mb-space-20 flex items-center">
          <h3 className="text-size-sm font-semibold">Available to Predict</h3>
          &nbsp;
          <Tooltip>
            <TooltipTrigger>
              <SvgIcon name="RedAlertIcon" />
            </TooltipTrigger>
            <TooltipContent>
              <p>
                Maximum predictable volume per
                <br />
                outcome set by the channel owner
              </p>
            </TooltipContent>
          </Tooltip>
        </div>

        {/* Progress bar */}
        <div className="mb-space-6 bg-gray-1 relative h-2 rounded-full">
          <div
            className="bg-no-red absolute top-0 left-0 h-full rounded-full"
            style={{
              width: `${percentage}%`,
            }}
          ></div>
        </div>

        <div className="flex justify-between">
          <span className="text-size-xs text-no-red font-semibold">
            ${currentOutcome.formattedVolume}
          </span>
          <div className="text-right">
            <span className="text-size-xs text-gray-3">Max</span>
            <span className="ml-space-6 text-size-xs font-semibold">
              ${marketData.marketMaxOutcomeVolumeFormatted}
            </span>
          </div>
        </div>
      </div>

      <div className="mb-space-10">
        <h3 className="text-size-sm mb-space-10 font-semibold">Amount</h3>
        <div className="bg-gray-2 px-space-10 py-space-20 flex flex-col">
          <div className="text-size-xs text-gray-3 mb-space-10 text-right">
            My Balance ${userBalance}
          </div>
          <div className="mb-space-10">
            <DollarInput
              placeholder="$1"
              className="text-dark text-size-xl px-space-10 h-(--input-height-lg) text-right font-bold"
              value={amount}
              onChange={handleAmountChange}
              maxValue={userBalanceNumber}
              minValue={1}
            />
          </div>
          <div className="gap-space-10 flex justify-end">
            <button
              className="border-line py-space-6 px-space-10 text-size-sm hover:bg-gray-1 rounded-sm border bg-white font-medium transition-colors"
              onClick={() => handleAddAmount(1)}
            >
              +$1
            </button>
            <button
              className="border-line py-space-6 px-space-10 text-size-sm hover:bg-gray-1 rounded-sm border bg-white font-medium transition-colors"
              onClick={() => handleAddAmount(20)}
            >
              +$20
            </button>
            <button
              className="border-line py-space-6 px-space-10 text-size-sm hover:bg-gray-1 rounded-sm border bg-white font-medium transition-colors"
              onClick={() => handleAddAmount(100)}
            >
              +$100
            </button>
            <button
              className="border-line py-space-6 px-space-10 text-size-sm hover:bg-gray-1 rounded-sm border bg-white font-medium transition-colors"
              onClick={handleMaxAmount}
            >
              Max
            </button>
          </div>
        </div>
      </div>

      {/* Predict Button */}
      <div className="mb-space-10">
        <PredictButton
          textSize="base"
          marketId={marketId}
          outcome={currentOutcome.outcome}
          amount={amount}
          className="w-full font-bold"
          onSuccess={handlePredictSuccess}
          size="xl2"
        />
      </div>

      {/* Market Status and Time Remaining */}
      <div className="mb-space-15 gap-space-8 flex items-center justify-end">
        <PredictionMarketCardStatus status={marketData.marketStatus} />
        <MarketCountdown endTime={marketData.marketNextDeadline} />
      </div>

      {/* Additional Info */}
      <div className="gap-space-6 flex flex-col">
        <div className="text-size-xs flex justify-between font-semibold">
          <span className="text-gray-3">Execution Fee</span>
          <span className="font-medium">0.1</span>
        </div>
        <div className="text-size-xs flex justify-between font-semibold">
          <span className="text-gray-3">Estimated odds</span>
          <span className="font-medium">0.00%</span>
        </div>
        <div className="text-size-sm flex justify-between font-bold">
          <span className="font-medium">Estimated Winnings</span>
          <span className="font-medium">0.00</span>
        </div>

        <p className="text-no-red text-size-xs">
          Current estimates apply, but final odds lock in upon betting closure.
        </p>
      </div>
    </div>
  );
}
