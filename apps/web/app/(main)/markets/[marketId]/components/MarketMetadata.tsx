import CommonAvatar from '@/components/ui/avatar-image';
import { formatEndTime } from '@/lib/format';

interface MarketMetadataProps {
  title: string;
  endTime?: Date | string | number;
  avatarUrl?: string;
}

export default function MarketMetadata({ title, endTime, avatarUrl }: MarketMetadataProps) {
  return (
    <div className="gap-space-30 flex items-center">
      <div>
        <CommonAvatar imageUrl={avatarUrl} size="lg" alt={title} />
      </div>
      <div className="flex flex-1 justify-between">
        <div className="gap-space-20 flex flex-col">
          <h1 className="text-size-base text-dark-deep font-bold">{title}</h1>
          <div className="text-size-xs flex items-center">
            <span className="text-gray-3">Ends: </span>
            &nbsp;
            <span className="font-semibold">
              {endTime ? formatEndTime(endTime) : 'No end date specified'}
            </span>
          </div>
        </div>
        <div className="gap-space-20 flex flex-col"></div>
      </div>
    </div>
  );
}
