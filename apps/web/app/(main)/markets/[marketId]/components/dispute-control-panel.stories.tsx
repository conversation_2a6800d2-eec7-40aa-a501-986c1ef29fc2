import type { Meta, StoryObj } from '@storybook/react';
import DisputeControlPanel from './dispute-control-panel';

const meta: Meta<typeof DisputeControlPanel> = {
  title: 'DisputeControlPanel',
  component: DisputeControlPanel,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof DisputeControlPanel>;

/**
 * Default DisputeControlPanel component
 */
export const Default: Story = {
  args: {
    proposedOutcome: 'Yes',
    disputeEndTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    disputeAmount: '$500',
  },
};

/**
 * DisputeControlPanel with "No" outcome
 */
export const NoOutcome: Story = {
  args: {
    proposedOutcome: 'No',
    disputeEndTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    disputeAmount: '$500',
  },
};

/**
 * DisputeControlPanel with short time remaining
 */
export const ShortTimeRemaining: Story = {
  args: {
    proposedOutcome: 'Yes',
    disputeEndTime: new Date(Date.now() + 2 * 60 * 60 * 1000), // 2 hours from now
    disputeAmount: '$500',
  },
};

/**
 * DisputeControlPanel with large dispute amount
 */
export const LargeDisputeAmount: Story = {
  args: {
    proposedOutcome: 'Yes',
    disputeEndTime: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    disputeAmount: '$10,000',
  },
};
