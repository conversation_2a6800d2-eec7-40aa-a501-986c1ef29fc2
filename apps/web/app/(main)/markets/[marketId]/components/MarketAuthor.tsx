import { cn } from '@repo/ui/lib/utils';
import CommonAvatar from '@/components/ui/avatar-image';
import SvgIcon from '@/components/icons/svg-icon';
import Link from 'next/link';
import { INNER_LINKS } from '@/lib/constants';
import { Tooltip, TooltipTrigger, TooltipContent } from '@repo/ui/components/tooltip';
import { useChannelByIdWithUser } from '@/hooks/query/channel';
import { useToggleChannelSubscription } from '@/hooks/query/channel/use-toggle-channel-subscription';
import ChannelSubscription from '@/app/(main)/channels/[channelId]/components/channel-subscription';
import { useGlobalStore } from '@/store/global.store';
import { useState, useEffect } from 'react';

interface MarketAuthorProps {
  channelId: string;
  channelName: string;
  avatarUrl?: string;
  className?: string;
  totalVolume?: string;
  totalPredictors?: number;
  currentPredictions: number;
}

const NEED_PREDICTS = 10;
export default function MarketAuthor({
  channelId,
  channelName,
  avatarUrl,
  className,
  totalVolume,
  totalPredictors,
  currentPredictions,
}: MarketAuthorProps) {
  const isNeedPredicts = currentPredictions < NEED_PREDICTS;
  const { data: channelData, refetch } = useChannelByIdWithUser(channelId);
  const toggleSubscription = useToggleChannelSubscription();
  const currentUserAddress = useGlobalStore(v => v.safeSmartAccount?.address);
  const [optimisticSubscribed, setOptimisticSubscribed] = useState<boolean | undefined>(
    channelData?.channelIsSubscribed
  );

  useEffect(() => {
    setOptimisticSubscribed(channelData?.channelIsSubscribed);
  }, [channelData?.channelIsSubscribed]);

  const handleToggleSubscription = async () => {
    if (!channelId || typeof optimisticSubscribed !== 'boolean') return;
    const previousState = optimisticSubscribed;
    setOptimisticSubscribed(!optimisticSubscribed);

    try {
      await toggleSubscription.mutateAsync({
        channelId: channelId,
        isCurrentlySubscribed: optimisticSubscribed,
      });
      await refetch();
    } catch (error) {
      setOptimisticSubscribed(previousState);
    }
  };

  const renderChannelSubscription = () => {
    if (!currentUserAddress) return null;
    if (!channelData?.userAddress) return null;
    if (currentUserAddress.toLowerCase() === channelData.userAddress.toLowerCase()) return null;

    return (
      <ChannelSubscription
        channelName={channelName}
        channelId={channelId}
        isSubscribed={optimisticSubscribed || false}
        onToggle={handleToggleSubscription}
        isLoading={toggleSubscription.isPending}
      />
    );
  };

  return (
    <div className={cn('text-size-xs flex items-center justify-between', className)}>
      <div className="gap-space-30 flex items-center">
        {/* Created by */}
        <Link href={`${INNER_LINKS.MAIN.CHANNELS.DETAIL(channelId)}`}>
          <div className="gap-space-10 flex items-center">
            <div className="text-size-xxs text-gray-3">Created by:</div>
            <div className="gap-space-6 border-line flex items-center rounded-full border">
              <CommonAvatar imageUrl={avatarUrl} size="sm" alt={channelName} />
              <span className="text-size-xxs text-icon-dark pr-space-12 font-semibold">
                {channelName}
              </span>
            </div>
          </div>
        </Link>

        {/* Total Volume */}
        <div className="gap-space-5 flex items-center">
          <SvgIcon name="DollarIcon" />
          <span className="text-size-xs text-gray-3">Total Volume:</span>
          <span className="font-semibold">${totalVolume}</span>
        </div>

        {/* Total Predictors */}
        <div className="gap-space-5 flex items-center">
          <SvgIcon name="MemberIcon" />
          <span className="text-size-xs text-gray-3">Total Predictors:</span>
          <span className="font-semibold">{totalPredictors}</span>
        </div>

        {/* Channel subscribed */}
        <div className="gap-space-5 flex items-center">{renderChannelSubscription()}</div>
      </div>

      {/* Current Predictions */}
      {isNeedPredicts && (
        <div className="gap-space-5 flex items-center">
          <SvgIcon name="BombIcon" />
          <span className="text-size-xs text-gray-3">Needs 10 predicts</span>
          <span className="text-no-red font-semibold">
            {currentPredictions}/{NEED_PREDICTS}
          </span>
          <Tooltip>
            <TooltipTrigger>
              <SvgIcon name="RedAlertIcon" />
            </TooltipTrigger>
            <TooltipContent>
              <p>Minimum 10 predictions needed.</p>
              <p>All predicts refunded if not met.</p>
            </TooltipContent>
          </Tooltip>
        </div>
      )}
    </div>
  );
}
