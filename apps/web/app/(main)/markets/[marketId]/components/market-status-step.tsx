import type { MarketStatusEnum } from '@/lib/api/market/market.schema.server';
import { cn } from '@repo/ui/lib/utils';
import { Check } from 'lucide-react';

interface MarketStatusStepProps {
  marketStatus: MarketStatusEnum;
}

type MarketStatusStep = {
  status: MarketStatusEnum;
  label: string;
};

const STEPS = [
  {
    status: 'Predict',
    label: 'Predict',
  },
  {
    status: 'Reviewing Prediction',
    label: 'Reviewing Prediction',
  },
  {
    status: 'Outcome Proposed',
    label: 'Outcome Proposed',
  },
  {
    status: 'Disputed',
    label: 'Disputed',
  },
  {
    status: 'Prediction close',
    label: 'Prediction close',
  },
];

const StepIcon = ({ completed }: { completed: boolean }) => {
  if (completed) {
    return (
      <div className="flex size-[16px] items-center justify-center rounded-full bg-(--color-point-3)">
        <Check className="size-[10px] text-(--color-white)" strokeWidth={2.5} />
      </div>
    );
  }
  return <div className="size-[16px] rounded-full bg-(--color-icon-gray)" />;
};
const checkCurrentStatus = (status: MarketStatusEnum) => {
  switch (status) {
    case 'OPEN':
      return 'Predict';
    case 'REVIEWING':
      return 'Reviewing Prediction';
    case 'DISPUTABLE':
      return 'Outcome Proposed';
    case 'DISPUTED':
      return 'Disputed';
    default:
      return 'Prediction close';
  }
};
export default function MarketStatusStep({ marketStatus }: MarketStatusStepProps) {
  const currentIndex = STEPS.findIndex(s => s.status === checkCurrentStatus(marketStatus));

  return (
    <div className="flex flex-col">
      {STEPS.map((step, index) => {
        const isCompleted = index <= currentIndex;
        const isLastStep = index === STEPS.length - 1;
        let lineIsBlue = false;

        if (isCompleted && !isLastStep) {
          const nextStepIsCompleted = index + 1 <= currentIndex;
          if (nextStepIsCompleted) {
            lineIsBlue = true;
          }
        }
        return (
          <div key={step.status} className="flex items-start">
            <div className="mr-3 flex flex-col items-center">
              <StepIcon completed={isCompleted} />
              {!isLastStep && (
                <div
                  className={cn('w-0.5', lineIsBlue ? 'bg-sky' : 'bg-gray-3')}
                  style={{ height: '28px' }}
                />
              )}
            </div>

            <div className="text-size-sm mt-[-1px] font-semibold">
              <span className={cn('font-semibold', isCompleted ? 'text-sky' : 'text-gray-3')}>
                {`0${index + 1}`}
              </span>
              <span className={cn('ml-1.5', isCompleted ? 'text-mid-dark' : 'text-gray-3')}>
                {step.label}
              </span>
            </div>
          </div>
        );
      })}
    </div>
  );
}
