import type { MarketStatusEnum } from '@/lib/api/market/market.schema.server';
import { cn } from '@repo/ui/lib/utils';
import { Check } from 'lucide-react';

interface MarketStatusStepProps {
  marketStatus: MarketStatusEnum;
}

type MarketStatusStep = {
  status: string; // Changed to string as it can be a mapped label now
  label: string;
};

const ALL_STEPS: MarketStatusStep[] = [
  {
    status: 'Predict',
    label: 'Predict',
  },
  {
    status: 'Reviewing Prediction',
    label: 'Reviewing Prediction',
  },
  {
    status: 'Outcome Proposed',
    label: 'Outcome Proposed',
  },
  {
    status: 'Disputed',
    label: 'Disputed',
  },
  {
    status: 'Prediction close',
    label: 'Prediction close',
  },
];

const StepIcon = ({ completed }: { completed: boolean }) => {
  if (completed) {
    return (
      <div className="flex size-[16px] items-center justify-center rounded-full bg-(--color-point-3)">
        <Check className="size-[10px] text-(--color-white)" strokeWidth={2.5} />
      </div>
    );
  }
  return <div className="size-[16px] rounded-full bg-(--color-icon-gray)" />;
};

// Map MarketStatusEnum to a conceptual step label
const getMappedStatusLabel = (status: MarketStatusEnum): string | null => {
  switch (status) {
    case 'OPEN':
      return 'Predict';
    case 'REVIEWING':
      return 'Reviewing Prediction';
    case 'DISPUTABLE':
      return 'Outcome Proposed';
    case 'DISPUTED':
      return 'Disputed';
    case 'CLOSED_WITHOUT_DISPUTE':
    case 'CLOSED_WITH_DISPUTE_ACCEPTED':
    case 'CLOSED_WITH_DISPUTE_REJECTED':
    case 'CANCELLED_WITH_OUTCOME_NOT_PROPOSED':
      return 'Prediction close';
    case 'CANCELLED_WITH_UNMET':
      return null; // Special case: no steps completed
    case 'CANCELLED_WITH_INVALID':
      return 'Prediction close'; // Special case: all steps completed except 'Disputed'
    default:
      return null; // Should not happen with exhaustive enum
  }
};

export default function MarketStatusStep({ marketStatus }: MarketStatusStepProps) {
  const mappedStatusLabel = getMappedStatusLabel(marketStatus);

  let stepsToRender = ALL_STEPS;
  let finalCompletionIndex: number = -1; // Default to no steps completed

  if (marketStatus === 'CANCELLED_WITH_UNMET') {
    // No steps completed, finalCompletionIndex remains -1
  } else if (marketStatus === 'CANCELLED_WITH_INVALID') {
    stepsToRender = ALL_STEPS.filter(step => step.status !== 'Disputed');
    // In this case, 'Prediction close' should be the last completed step in the filtered list
    finalCompletionIndex = stepsToRender.findIndex(s => s.status === 'Prediction close');
  } else if (mappedStatusLabel) {
    finalCompletionIndex = ALL_STEPS.findIndex(s => s.status === mappedStatusLabel);
  }

  return (
    <div className="flex flex-col">
      {stepsToRender.map((step, index) => {
        // For CANCELLED_WITH_UNMET, finalCompletionIndex is -1, so isCompleted will always be false.
        const isCompleted = index <= finalCompletionIndex;
        const isLastStep = index === stepsToRender.length - 1;
        let lineIsBlue = false;

        if (isCompleted && !isLastStep) {
          const nextStepIsCompleted = index + 1 <= finalCompletionIndex;
          if (nextStepIsCompleted) {
            lineIsBlue = true;
          }
        }
        return (
          <div key={step.status} className="flex items-start">
            <div className="mr-3 flex flex-col items-center">
              <StepIcon completed={isCompleted} />
              {!isLastStep && (
                <div
                  className={cn('w-0.5', lineIsBlue ? 'bg-sky' : 'bg-gray-3')}
                  style={{ height: '28px' }}
                />
              )}
            </div>

            <div className="text-size-sm mt-[-1px] font-semibold">
              <span className={cn('font-semibold', isCompleted ? 'text-sky' : 'text-gray-3')}>
                {`0${index + 1}`}
              </span>
              <span className={cn('ml-1.5', isCompleted ? 'text-mid-dark' : 'text-gray-3')}>
                {step.label}
              </span>
            </div>
          </div>
        );
      })}
    </div>
  );
}
