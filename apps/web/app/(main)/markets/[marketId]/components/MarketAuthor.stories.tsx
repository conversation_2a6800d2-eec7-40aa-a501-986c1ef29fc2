import type { Meta, StoryObj } from '@storybook/react';
import MarketAuthor from './MarketAuthor';

const meta = {
  title: 'Markets/MarketAuthor',
  component: MarketAuthor,
} satisfies Meta<typeof MarketAuthor>;

export default meta;
type Story = StoryObj<typeof MarketAuthor>;

// Default story with minimal props
export const Default: Story = {
  args: {
    channelName: 'JohnDoe',
    totalVolume: '$1,234',
    totalPredictors: 42,
    currentPredictions: '3/10',
  },
};

// With custom avatar
export const WithAvatar: Story = {
  args: {
    ...Default.args,
    avatarUrl: 'https://i.pravatar.cc/150?img=1',
  },
};

// With high volume and predictors
export const HighVolume: Story = {
  args: {
    ...WithAvatar.args,
    totalVolume: '$1,234,567',
    totalPredictors: 1024,
    currentPredictions: '9/10',
  },
};

// With minimum required props
export const Minimal: Story = {
  args: {
    channelName: 'minimal',
  },
};

// With custom class name
export const WithCustomClass: Story = {
  args: {
    ...WithAvatar.args,
    className: 'p-4 bg-gray-100 rounded-lg',
  },
};
