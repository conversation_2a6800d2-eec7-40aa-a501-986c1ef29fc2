import { cn } from '@repo/ui/lib/utils';
import { useState } from 'react';
import { ChevronDown } from 'lucide-react';
import {
  BaseDropdownMenu,
  BaseDropdownMenuTrigger,
  BaseDropdownMenuContent,
  BaseDropdownMenuItem,
} from '@/components/ui/base.dropdown-menu';

interface Position {
  outcome: string;
  value: string;
}

interface PositionsDropdownProps {
  positions: Position[];
}

export default function PositionsDropdown({ positions }: PositionsDropdownProps) {
  const [selectedPosition, setSelectedPosition] = useState<Position>(
    positions[0] || { outcome: '', value: '' }
  );
  const getPositionStyle = (outcome: string) => {
    switch (outcome.toLowerCase()) {
      case 'yes':
        return 'bg-yes-green/50 text-yes-green border-yes';
      case 'no':
        return 'bg-no-red/50 text-no-red border-no';
      default:
        return 'bg-sky/50 text-sky border-sky';
    }
  };

  if (positions.length === 0) return null;

  return (
    <BaseDropdownMenu>
      <BaseDropdownMenuTrigger
        className={cn(
          'px-space-8 flex h-[20px] items-center justify-between gap-1 rounded-full border',
          getPositionStyle(selectedPosition.outcome)
        )}
      >
        <span className="text-xs font-medium">
          {selectedPosition.value} {selectedPosition.outcome}
        </span>
        <ChevronDown size={12} />
      </BaseDropdownMenuTrigger>
      <BaseDropdownMenuContent>
        {positions.map(position => (
          <BaseDropdownMenuItem
            key={position.outcome}
            className={cn(
              'px-space-8 cursor-pointer py-1 text-xs',
              selectedPosition.outcome === position.outcome && 'bg-muted'
            )}
            onClick={() => setSelectedPosition(position)}
          >
            <div className="flex w-full justify-between">
              <span>{position.value}</span>
              <span>{position.outcome}</span>
            </div>
          </BaseDropdownMenuItem>
        ))}
      </BaseDropdownMenuContent>
    </BaseDropdownMenu>
  );
}
