import { cn } from '@repo/ui/lib/utils';

interface MarketRulesProps {
  description: string;
}

export default function MarketRules({ description }: MarketRulesProps) {
  return (
    <div className={cn('gap-space-20 flex flex-col')}>
      <header>
        <h2 className="dashboard-h2">Rules</h2>
      </header>
      <p className="text-size-sm13 border-line bg-gray-2 px-space-20 py-space-30 text-gray-3 rounded-round-sm border">
        {description}
      </p>
    </div>
  );
}
