import MarketCountdown from '@/components/common/market-countdown';
import { useMarketById } from '@/hooks/query/market';

interface PredictionCancelledPanelProps {
  marketId: string;
  status: 'CANCELLED_WITH_UNMET' | 'CANCELLED_WITH_INVALID' | 'CANCELLED_WITH_OUTCOME_NOT_PROPOSED';
}

export default function PredictionCancelledPanel({
  marketId,
  status,
}: PredictionCancelledPanelProps) {
  const { data } = useMarketById(marketId);
  const getMessage = () => {
    switch (status) {
      case 'CANCELLED_WITH_UNMET':
        return 'The prediction was cancelled because the outcome was not met.';
      case 'CANCELLED_WITH_INVALID':
        return 'The prediction was cancelled because the outcome was invalid.';
      case 'CANCELLED_WITH_OUTCOME_NOT_PROPOSED':
        return 'The prediction was cancelled because the outcome was not proposed.';
      default:
        return 'The prediction was cancelled.';
    }
  };

  return (
    <div className="flex flex-col">
      <h1 className="pb-space-30 dashboard-h2">Prediction Cancelled</h1>
      <div className="flex flex-col items-center">
        <svg
          width="50"
          height="50"
          viewBox="0 0 50 50"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect width="50" height="50" rx="25" fill="#3B424B" />
          <path
            d="M19.58 33.58L12.4 26.4C11.62 25.62 11.62 24.36 12.4 23.58C13.18 22.8 14.44 22.8 15.22 23.58L21 29.34L34.76 15.58C35.54 14.8 36.8 14.8 37.58 15.58C38.36 16.36 38.36 17.62 37.58 18.4L22.4 33.58C21.64 34.36 20.36 34.36 19.58 33.58Z"
            fill="white"
          />
        </svg>
        <p className="text-size-sm text-gray-3 mt-space-30">{getMessage()}</p>
      </div>

      {/* Time Remaining */}
      <div className="mb-space-15 pt-space-60 gap-space-8 flex items-center justify-end">
        {data?.marketNextDeadline && <MarketCountdown endTime={data.marketNextDeadline} />}
      </div>
    </div>
  );
}
