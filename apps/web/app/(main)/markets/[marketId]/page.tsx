'use client';

import { useParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import { pxToRem } from '@repo/ui/lib/utils';
import { useMarketById } from '@/hooks/query/market';
import MarketMetadata from './components/MarketMetadata';
import MarketAuthor from './components/MarketAuthor';
import MarketOutcomes from './components/market-outcomes';
import MarketRules from './components/MarketRules';
import MarketTabs from './components/MarketTabs';
import PredictControlPanel from './components/predict-control-panel';
import DisputeControlPanel from './components/dispute-control-panel';
import MarketStatusStep from './components/market-status-step';
import { ExternalLink } from 'lucide-react';
import { MarketOutcome } from '@/lib/api/market/market.transform';
import PredictionClosedPanel from './components/prediction-closed-panel';
import PredictionReviewPanel from './components/prediction-review-panel';
import PredictGoLoading from '@/components/icons/predict-go-loading';

export default function MarketDetailPage() {
  const params = useParams();
  const marketId = params.marketId as string;
  const [selectedOutcome, setSelectedOutcome] = useState<MarketOutcome | null>(null);
  const { data, isLoading, isError } = useMarketById(marketId);

  useEffect(() => {
    if (data && data.marketOutcomes && data.marketOutcomes.length > 0 && !selectedOutcome) {
      const firstOutcome = data.marketOutcomes[0];
      if (firstOutcome) {
        setSelectedOutcome(firstOutcome);
      }
    }
  }, [data, selectedOutcome]);

  const handleClickOutcome = (outcome: MarketOutcome) => {
    if (!data) return;
    setSelectedOutcome(outcome);
  };

  if (isLoading || !data) {
    return (
      <div className="flex h-full flex-1 items-center justify-center">
        <PredictGoLoading size="xl" />
      </div>
    );
  }

  if (isError) {
    // TODO: add Error UI
    return (
      <div className="p-space-30 text-center">
        <div className="mb-2 text-lg font-semibold text-gray-700">Failed to load market</div>
        <div className="text-sm text-gray-500">Please refresh the page or try again later.</div>
      </div>
    );
  }

  const renderControlPanel = () => {
    switch (data.marketStatus) {
      case 'OPEN':
        return <PredictControlPanel marketId={marketId} selectedOutcome={selectedOutcome} />;
      case 'DISPUTABLE':
        return <DisputeControlPanel marketId={marketId} />;
      case 'DISPUTED':
        return <PredictionReviewPanel marketId={marketId} />;
      default:
        return <PredictionClosedPanel marketId={marketId} />;
    }
  };

  return (
    <div
      data-page="market-detail"
      style={
        {
          '--aside-width': pxToRem(340),
        } as React.CSSProperties
      }
      className="flex flex-col"
    >
      <div className="flex pr-(--aside-width)">
        <section className="p-space-30 mx-auto max-w-[1300px] flex-1">
          <div className="mb-space-30">
            <div className="flex items-center justify-between">
              <MarketMetadata
                avatarUrl={data.marketAvatarImageUrl}
                title={data.marketTitle}
                endTime={data.marketPredictionDeadline}
              />
            </div>
          </div>

          <div className="pb-space-30 border-line border-b">
            <MarketAuthor
              channelId={data.channelId}
              channelName={data.channelName}
              avatarUrl={data.channelAvatarImageUrl}
              totalVolume={data.marketTotalVolumeFormatted}
              totalPredictors={data.marketParticipants}
              currentPredictions={data.marketPredictCount}
            />
          </div>

          <div className="my-space-30">
            {data.marketOutcomes && data.marketOutcomes.length > 0 && (
              <MarketOutcomes
                totalVolume={data.marketTotalVolume}
                handleClickOutcome={handleClickOutcome}
                outcomes={data.marketOutcomes}
              />
            )}
          </div>

          <MarketRules description={data?.marketDescription || ''} />
          <div className="gap-space-10 mt-space-30 flex items-center">
            <span className="text-size-sm text-mid-dark font-semibold">Reference URL</span>
            <svg
              width="1"
              height="10"
              viewBox="0 0 1 10"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path d="M0 10V0H1V10H0Z" fill="#C1C1C1" />
            </svg>
            <a
              href={data.marketReferenceUrl}
              className="text-size-sm text-gray-3 flex items-center underline"
              target="_blank"
              rel="noopener noreferrer"
            >
              {data.marketReferenceUrl}
              <ExternalLink className="text-mid-dark ml-1" size={14} />
            </a>
          </div>
          <MarketTabs marketId={marketId} />
        </section>
      </div>

      <aside className="border-l-line pt-space-30 fixed top-[109px] right-0 bottom-0 z-10 w-(--aside-width) overflow-y-auto border-l bg-white">
        <div className="px-space-30 pb-space-30">{renderControlPanel()}</div>
        <div className="border-t-line p-space-30 border-t">
          <MarketStatusStep marketStatus={data.marketStatus} />
        </div>
      </aside>
    </div>
  );
}
