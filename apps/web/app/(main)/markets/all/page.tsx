'use client';

import EmptyMarketList from '@/components/common/empty-list';
import {
  PredictionMarketCard,
  PredictionMarketCardSkeleton,
} from '@/components/common/prediction-market-card';
import { useMarketsWithAll } from '@/hooks/query/market';
import {
  GetMarketFilterWithAllEnum,
  GetMarketsOrderEnum,
} from '@/lib/api/market/market.schema.server';
import { useGlobalStore } from '@/store/global.store';
import { pxToRem } from '@repo/ui/lib/utils';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useState } from 'react';
import MarketViewControl from '../market-view-control';
import { updateSearchParams } from '@/lib/utils';

const limit = 50;

export default function AllMarketsPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const isAsideOpen = useGlobalStore(state => state.isAsideOpen);

  const orderParam = searchParams.get('order') as GetMarketsOrderEnum;
  const statusParam = searchParams.get('status') as GetMarketFilterWithAllEnum;

  const initialStatus: GetMarketFilterWithAllEnum = statusParam || 'ALL';
  const initialOrder = orderParam || 'VOLUME';

  const [selectedStatus, setSelectedStatus] = useState<GetMarketFilterWithAllEnum>(initialStatus);
  const [sortBy, setSortBy] = useState<GetMarketsOrderEnum>(initialOrder);

  const handleSortChange = (option: GetMarketsOrderEnum) => {
    setSortBy(option);
    updateSearchParams({ order: option }, router, searchParams, pathname);
  };

  const handleStatusChange = (status: GetMarketFilterWithAllEnum) => {
    setSelectedStatus(status);
    updateSearchParams({ status }, router, searchParams, pathname);
  };
  const { data: allMarkets, isLoading: allMarketsLoading } = useMarketsWithAll({
    page: 0,
    limit,
    order: sortBy,
    filter: selectedStatus,
  });

  console.log('selectedStatus', selectedStatus);
  console.log('allMarkets', allMarkets);

  return (
    <div
      data-page="all-markets"
      style={
        {
          '--aside-width': pxToRem(340),
        } as React.CSSProperties
      }
      className={`relative flex ${isAsideOpen ? 'pr-(--aside-width)' : 'pr-0'}`}
    >
      <section className="min-w-0 flex-1">
        <header className="p-space-30 pb-space-30">
          <h1 className="text-size-lg text-mid-dark font-bold">All Markets</h1>
        </header>

        <div className="flex-1">
          <div className="px-[calc(30px-var(--market-card-border-width))] py-10">
            {allMarketsLoading ? (
              <div className="grid grid-cols-[repeat(auto-fill,minmax(var(--market-card-width),1fr))] gap-x-7 gap-y-14 overflow-hidden p-[var(--market-card-border-width)]">
                {Array.from({ length: 12 }).map((_, index) => (
                  <div key={index} className="relative w-full">
                    <PredictionMarketCardSkeleton />
                    <span className="absolute -top-7 right-0 left-0 h-[1px] [background:var(--color-line,#E3E3E3)]" />
                  </div>
                ))}
              </div>
            ) : allMarkets && allMarkets.markets && allMarkets.markets.length > 0 ? (
              <div className="grid grid-cols-[repeat(auto-fill,minmax(var(--market-card-width),1fr))] gap-x-7 gap-y-14 overflow-hidden p-[var(--market-card-border-width)]">
                {allMarkets.markets.map((item, index) => (
                  <div key={item.marketId || index} className="relative w-full">
                    <PredictionMarketCard item={item} />
                    <span className="absolute -top-7 right-0 left-0 h-[1px] [background:var(--color-line,#E3E3E3)]" />
                  </div>
                ))}
              </div>
            ) : (
              <div className="mt-space-60">
                <EmptyMarketList />
              </div>
            )}
          </div>
        </div>
      </section>
      <aside
        className={`border-l-line fixed top-0 bottom-0 z-10 w-(--aside-width) overflow-y-auto border-l bg-white pt-[100px] ${
          isAsideOpen ? 'right-0' : 'hidden'
        }`}
      >
        <MarketViewControl
          category={null}
          selectedStatus={selectedStatus}
          sortBy={sortBy}
          onSortChange={handleSortChange}
          onStatusChange={handleStatusChange}
        />
      </aside>
    </div>
  );
}
