'use client';

import EmptyMarketList from '@/components/common/empty-list';
import {
  PredictionMarketCard,
  PredictionMarketCardSkeleton,
} from '@/components/common/prediction-market-card';
import { BaseButton } from '@/components/ui/base.button';
import { useCategoryStats } from '@/hooks/query/category/use-category-stats';
import { MarketListItem } from '@/hooks/query/market/market.mapper';
import { useMarketsByCategory } from '@/hooks/query/market/use-markets-by-category';
import {
  GetMarketFilterWithAllEnum,
  GetMarketsOrderEnum,
} from '@/lib/api/market/market.schema.server';
import { useGlobalStore } from '@/store/global.store';
import { pxToRem } from '@repo/ui/lib/utils';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import MarketViewControl from './market-view-control';
import { updateSearchParams } from '@/lib/utils';

const limit = 50;

export default function MarketsPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const isAsideOpen = useGlobalStore(state => state.isAsideOpen);

  const category = searchParams.get('category');
  const { data: categoryStats } = useCategoryStats(category || '');

  const orderParam = searchParams.get('order') as GetMarketsOrderEnum;
  const statusParam = searchParams.get('status') as GetMarketFilterWithAllEnum;

  const initialStatus: GetMarketFilterWithAllEnum = statusParam || 'ALL';
  const initialOrder = orderParam || 'VOLUME';

  const [selectedStatus, setSelectedStatus] = useState<GetMarketFilterWithAllEnum>(initialStatus);
  const [currentPage, setCurrentPage] = useState(0);
  const [accumulatedMarkets, setAccumulatedMarkets] = useState<MarketListItem[]>([]);
  const [sortBy, setSortBy] = useState<GetMarketsOrderEnum>(initialOrder);

  const handleSortChange = (option: GetMarketsOrderEnum) => {
    setSortBy(option);
    setCurrentPage(0);
    setAccumulatedMarkets([]);
    updateSearchParams({ order: option }, router, searchParams, pathname);
  };

  const handleStatusChange = (status: GetMarketFilterWithAllEnum) => {
    setSelectedStatus(status);
    updateSearchParams({ status }, router, searchParams, pathname);
  };

  const {
    data: categoryMarkets,
    isLoading: categoryLoading,
    isFetching,
  } = useMarketsByCategory(category || '', {
    page: currentPage,
    limit,
    order: sortBy,
    filter: selectedStatus,
  });

  // 새 데이터가 로드될 때 누적
  useEffect(() => {
    if (categoryMarkets && categoryMarkets.markets) {
      if (currentPage === 0) {
        // 첫 페이지거나 새로운 정렬일 때는 기존 데이터 초기화
        setAccumulatedMarkets(categoryMarkets.markets);
      } else {
        // 추가 페이지일 때는 기존 데이터에 추가
        setAccumulatedMarkets(prev => [...prev, ...categoryMarkets.markets]);
      }
    }
  }, [categoryMarkets, currentPage]);

  // 정렬이나 카테고리 변경 시 데이터 초기화
  useEffect(() => {
    setCurrentPage(0);
    setAccumulatedMarkets([]);
  }, [sortBy, category]);

  const handleLoadMore = () => {
    setCurrentPage(prev => prev + 1);
  };

  const hasMoreData =
    categoryMarkets && categoryMarkets.markets && categoryMarkets.markets.length === limit;

  // 카테고리가 없는 경우 빈 상태 표시
  if (!category) {
    return (
      <div
        data-page="category-markets"
        style={
          {
            '--aside-width': pxToRem(340),
          } as React.CSSProperties
        }
        className={`relative flex ${isAsideOpen ? 'pr-(--aside-width)' : 'pr-0'}`}
      >
        <section className="min-w-0 flex-1">
          <header className="p-space-30 pb-space-30">
            <h1 className="text-size-lg text-mid-dark font-bold">Category Markets</h1>
          </header>
          <div className="flex min-h-[400px] flex-col items-center justify-center">
            <div className="text-gray-3 text-center">
              <p className="mb-2 text-lg">카테고리를 선택해주세요</p>
              <p className="text-sm">
                특정 카테고리의 마켓을 확인하려면 category 파라미터를 추가해주세요.
              </p>
            </div>
          </div>
        </section>
        <aside
          className={`border-l-line fixed top-0 bottom-0 z-10 w-(--aside-width) overflow-y-auto border-l bg-white pt-[100px] ${
            isAsideOpen ? 'right-0' : 'hidden'
          }`}
        >
          <MarketViewControl
            category={null}
            selectedStatus={selectedStatus}
            sortBy={sortBy}
            onSortChange={handleSortChange}
            onStatusChange={handleStatusChange}
          />
        </aside>
      </div>
    );
  }

  return (
    <div
      data-page="category-markets"
      style={
        {
          '--aside-width': pxToRem(340),
        } as React.CSSProperties
      }
      className={`relative flex ${isAsideOpen ? 'pr-(--aside-width)' : 'pr-0'}`}
    >
      <section className="min-w-0 flex-1">
        <header className="p-space-30 pb-space-30">
          <h1 className="text-size-lg text-mid-dark font-bold">{category} Markets</h1>
          {categoryStats && (
            <div className="text-mid-dark mt-3 flex gap-6 text-sm">
              <div className="flex items-center gap-1">
                <span className="text-gray-3">Volume</span>
                <span className="text-primary font-medium">
                  ${categoryStats.totalVolumeFormatted}
                </span>
              </div>
              <div className="flex items-center gap-1">
                <span className="text-gray-3">Live Markets</span>
                <span className="text-primary font-medium">{categoryStats.liveMarketCount}</span>
              </div>
            </div>
          )}
        </header>

        <div className="flex-1">
          <div className="px-[calc(30px-var(--market-card-border-width))] py-10">
            {categoryLoading && currentPage === 0 ? (
              <div className="grid grid-cols-[repeat(auto-fill,minmax(var(--market-card-width),1fr))] gap-x-7 gap-y-14 overflow-hidden p-[var(--market-card-border-width)]">
                {Array.from({ length: 12 }).map((_, index) => (
                  <div key={index} className="relative w-full">
                    <PredictionMarketCardSkeleton />
                    <span className="absolute -top-7 right-0 left-0 h-[1px] [background:var(--color-line,#E3E3E3)]" />
                  </div>
                ))}
              </div>
            ) : accumulatedMarkets.length > 0 ? (
              <>
                <div className="grid grid-cols-[repeat(auto-fill,minmax(var(--market-card-width),1fr))] gap-x-7 gap-y-14 overflow-hidden p-[var(--market-card-border-width)]">
                  {accumulatedMarkets.map((item, index) => (
                    <div key={item.marketId || index} className="relative w-full">
                      <PredictionMarketCard item={item} />
                      <span className="absolute -top-7 right-0 left-0 h-[1px] [background:var(--color-line,#E3E3E3)]" />
                    </div>
                  ))}
                </div>

                {/* Load More 버튼 */}
                {hasMoreData && (
                  <div className="flex justify-center pt-10">
                    <BaseButton
                      variant="default"
                      onClick={handleLoadMore}
                      disabled={isFetching}
                      className="px-12 py-3"
                    >
                      {isFetching ? 'Loading...' : 'Load More'}
                    </BaseButton>
                  </div>
                )}
              </>
            ) : (
              <div className="mt-space-60">
                <EmptyMarketList />
              </div>
            )}
          </div>
        </div>
      </section>
      <aside
        className={`border-l-line fixed top-0 bottom-0 z-10 w-(--aside-width) overflow-y-auto border-l bg-white pt-[100px] ${
          isAsideOpen ? 'right-0' : 'hidden'
        }`}
      >
        <MarketViewControl
          category={category}
          selectedStatus={selectedStatus}
          sortBy={sortBy}
          onSortChange={handleSortChange}
          onStatusChange={handleStatusChange}
        />
      </aside>
    </div>
  );
}
