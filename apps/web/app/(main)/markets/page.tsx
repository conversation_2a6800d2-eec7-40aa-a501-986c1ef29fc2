'use client';

import { useState, useEffect } from 'react';
import { useSearchParams, useRouter, usePathname } from 'next/navigation';
import { MarketList } from '@/components/common/market-list';
import {
  PredictionMarketCard,
  PredictionMarketCardSkeleton,
} from '@/components/common/prediction-market-card';
import { BaseButton } from '@/components/ui/base.button';
import { pxToRem } from '@repo/ui/lib/utils';
import { GetMarketsOrderEnum, MarketStatusEnum } from '@/lib/api/market/market.schema.server';
import { useMarketsByCategory } from '@/hooks/query/market/use-markets-by-category';
import { MarketListItem } from '@/hooks/query/market/market.mapper';
import { useGlobalStore } from '@/store/global.store';
import MarketViewControl, { StatusFilterOption } from './market-view-control';
import EmptyMarketList from '@/components/common/empty-list';

const limit = 50;

export default function MarketsPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  const isAsideOpen = useGlobalStore(state => state.isAsideOpen);

  const category = searchParams.get('category');
  const orderParam = searchParams.get('order') as GetMarketsOrderEnum;
  const statusParam = searchParams.get('status') as StatusFilterOption;

  // URL에서 읽은 초기 상태
  const initialStatus: StatusFilterOption = statusParam || 'ALL';
  const initialOrder = orderParam || 'VOLUME';

  const [selectedStatus, setSelectedStatus] = useState<StatusFilterOption>(initialStatus);
  const [currentPage, setCurrentPage] = useState(0);
  const [accumulatedMarkets, setAccumulatedMarkets] = useState<MarketListItem[]>([]);
  const [sortBy, setSortBy] = useState<GetMarketsOrderEnum>(initialOrder);

  // URL parameter 업데이트 함수
  const updateSearchParams = (updates: Record<string, string | number | null>) => {
    const newParams = new URLSearchParams(searchParams.toString());

    Object.entries(updates).forEach(([key, value]) => {
      if (value !== null && value !== undefined) {
        newParams.set(key, value.toString());
      } else {
        newParams.delete(key);
      }
    });

    router.push(`${pathname}?${newParams.toString()}`);
  };

  const handleSortChange = (option: GetMarketsOrderEnum) => {
    setSortBy(option);
    setCurrentPage(0);
    setAccumulatedMarkets([]);
    updateSearchParams({ order: option });
  };

  const handleStatusChange = (status: StatusFilterOption) => {
    setSelectedStatus(status);
    updateSearchParams({ status: status === 'ALL' ? null : status });
  };

  // 카테고리 기반 마켓 데이터
  const {
    data: categoryMarkets,
    isLoading: categoryLoading,
    isFetching,
  } = useMarketsByCategory(category || '', undefined, {
    page: currentPage,
    limit,
    order: sortBy,
  });

  // 새 데이터가 로드될 때 누적
  useEffect(() => {
    if (categoryMarkets && categoryMarkets.markets) {
      if (currentPage === 0) {
        // 첫 페이지거나 새로운 정렬일 때는 기존 데이터 초기화
        setAccumulatedMarkets(categoryMarkets.markets);
      } else {
        // 추가 페이지일 때는 기존 데이터에 추가
        setAccumulatedMarkets(prev => [...prev, ...categoryMarkets.markets]);
      }
    }
  }, [categoryMarkets, currentPage]);

  // 정렬이나 카테고리 변경 시 데이터 초기화
  useEffect(() => {
    setCurrentPage(0);
    setAccumulatedMarkets([]);
  }, [sortBy, category]);

  const handleLoadMore = () => {
    setCurrentPage(prev => prev + 1);
  };

  const hasMoreData =
    categoryMarkets && categoryMarkets.markets && categoryMarkets.markets.length === limit;

  const getStatusFilter = (): MarketStatusEnum[] | undefined => {
    if (selectedStatus === 'ALL') return undefined;
    return [selectedStatus as MarketStatusEnum];
  };

  return (
    <div
      data-page="all-markets"
      style={
        {
          '--aside-width': pxToRem(340),
        } as React.CSSProperties
      }
      className={`relative flex ${isAsideOpen ? 'pr-(--aside-width)' : 'pr-0'}`}
    >
      <section className="min-w-0 flex-1">
        <header className="p-space-30 pb-space-30">
          <h1 className="text-size-lg text-mid-dark font-bold">
            {category ? `${category} Markets` : 'All Markets'}
          </h1>
        </header>

        {category ? (
          // 카테고리별 마켓 표시
          <div className="flex-1">
            <div className="px-[calc(30px-var(--market-card-border-width))] py-10">
              {categoryLoading && currentPage === 0 ? (
                <div className="grid grid-cols-[repeat(auto-fill,minmax(var(--market-card-width),1fr))] gap-x-7 gap-y-14 overflow-hidden p-[var(--market-card-border-width)]">
                  {Array.from({ length: 12 }).map((_, index) => (
                    <div key={index} className="relative w-full">
                      <PredictionMarketCardSkeleton />
                      <span className="absolute -top-7 right-0 left-0 h-[1px] [background:var(--color-line,#E3E3E3)]" />
                    </div>
                  ))}
                </div>
              ) : accumulatedMarkets.length > 0 ? (
                <>
                  <div className="grid grid-cols-[repeat(auto-fill,minmax(var(--market-card-width),1fr))] gap-x-7 gap-y-14 overflow-hidden p-[var(--market-card-border-width)]">
                    {accumulatedMarkets.map((item, index) => (
                      <div key={item.marketId || index} className="relative w-full">
                        <PredictionMarketCard item={item} />
                        <span className="absolute -top-7 right-0 left-0 h-[1px] [background:var(--color-line,#E3E3E3)]" />
                      </div>
                    ))}
                  </div>

                  {/* Load More 버튼 */}
                  {hasMoreData && (
                    <div className="flex justify-center pt-10">
                      <BaseButton
                        variant="default"
                        onClick={handleLoadMore}
                        disabled={isFetching}
                        className="px-12 py-3"
                      >
                        {isFetching ? 'Loading...' : 'Load More'}
                      </BaseButton>
                    </div>
                  )}
                </>
              ) : (
                <div className="mt-space-60">
                  <EmptyMarketList />
                </div>
              )}
            </div>
          </div>
        ) : (
          <MarketList page={0} limit={50} order={sortBy} status={getStatusFilter()} />
        )}
      </section>
      <aside
        className={`border-l-line fixed top-0 bottom-0 z-10 w-(--aside-width) overflow-y-auto border-l bg-white pt-[100px] ${
          isAsideOpen ? 'right-0' : 'hidden'
        }`}
      >
        <MarketViewControl
          category={category}
          selectedStatus={selectedStatus}
          sortBy={sortBy}
          onSortChange={handleSortChange}
          onStatusChange={handleStatusChange}
        />
      </aside>
    </div>
  );
}
