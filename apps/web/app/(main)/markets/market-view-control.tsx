import { cn } from '@repo/ui/lib/utils';
import { BaseButton } from '@/components/ui/base.button';
import SvgIcon from '@/components/icons/svg-icon';
import { GetMarketsOrderEnum, MarketStatusEnum } from '@/lib/api/market/market.schema.server';

type StatusFilterOption = 'ALL' | 'OPEN' | 'DISPUTABLE';

interface MarketViewControlProps {
  className?: string;
  category?: string | null;
  selectedStatus: StatusFilterOption;
  sortBy: GetMarketsOrderEnum;
  onSortChange: (option: GetMarketsOrderEnum) => void;
  onStatusChange: (status: StatusFilterOption) => void;
}

export default function MarketViewControl({
  className,
  category,
  selectedStatus,
  sortBy,
  onSortChange,
  onStatusChange,
}: MarketViewControlProps) {
  return (
    <div className={cn('p-space-30 gap-space-30 flex flex-col', className)}>
      {!category && (
        <div className="border-line pb-space-30 border-b">
          <h2 className="mb-space-20 text-lg font-semibold">Status</h2>
          <div className="space-y-space-15">
            <div className="flex items-center justify-between">
              <label
                htmlFor="status-all"
                className="cursor-pointer text-sm leading-none font-medium"
              >
                All
              </label>
              <input
                type="radio"
                id="status-all"
                name="status-filter"
                checked={selectedStatus === 'ALL'}
                onChange={() => onStatusChange('ALL')}
                className="h-4 w-4 border-gray-300 bg-gray-100 text-blue-600 focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div className="flex items-center justify-between">
              <label
                htmlFor="status-bet-live"
                className="cursor-pointer text-sm leading-none font-medium"
              >
                Bet Live
              </label>
              <input
                type="radio"
                id="status-bet-live"
                name="status-filter"
                checked={selectedStatus === 'OPEN'}
                onChange={() => onStatusChange('OPEN')}
                className="h-4 w-4 border-gray-300 bg-gray-100 text-blue-600 focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div className="flex items-center justify-between">
              <label
                htmlFor="status-disputable"
                className="cursor-pointer text-sm leading-none font-medium"
              >
                Disputable
              </label>
              <input
                type="radio"
                id="status-disputable"
                name="status-filter"
                checked={selectedStatus === 'DISPUTABLE'}
                onChange={() => onStatusChange('DISPUTABLE')}
                className="h-4 w-4 border-gray-300 bg-gray-100 text-blue-600 focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>
        </div>
      )}

      <div>
        <h2 className="mb-space-20 text-lg font-semibold">Sort by</h2>
        <div className="gap-space-10 grid grid-cols-2">
          <SortButton active={sortBy === 'VOLUME'} onClick={() => onSortChange('VOLUME')}>
            <SvgIcon name="SortVolumeIcon" />
            Volume
          </SortButton>
          <SortButton active={sortBy === 'NEWEST'} onClick={() => onSortChange('NEWEST')}>
            <SvgIcon name="SortNewestIcon" />
            Newest
          </SortButton>
          <SortButton active={sortBy === 'ENDING_SOON'} onClick={() => onSortChange('ENDING_SOON')}>
            <SvgIcon name="SortEndingSoonIcon" />
            Ending soon
          </SortButton>
          <SortButton active={sortBy === 'COMPETITIVE'} onClick={() => onSortChange('COMPETITIVE')}>
            <SvgIcon name="SortCompetitiveIcon" />
            Competitive
          </SortButton>
        </div>
      </div>
    </div>
  );
}

interface SortButtonProps {
  children: React.ReactNode;
  active: boolean;
  onClick: () => void;
}

function SortButton({ children, active, onClick }: SortButtonProps) {
  return (
    <BaseButton
      variant="neutral"
      className={cn('text-mid-dark flex w-full items-center justify-start text-sm', {
        'border-sky': active,
      })}
      onClick={onClick}
    >
      {children}
    </BaseButton>
  );
}

export type { StatusFilterOption };
