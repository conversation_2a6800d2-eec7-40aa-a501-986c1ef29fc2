'use client';

import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/ui/base.select';
import TableMarketColumn from '@/components/ui/TableMarketColumn';
import { usePortfolioDisputes } from '@/hooks/query/portfolio';
import { DisputeOrder } from '@/lib/api/portfolio/portfolio.schema.server';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/ui/components/table';
import { useState } from 'react';

const sortOptions = [
  { value: 'newest' as DisputeOrder, label: 'Newest' },
  { value: 'value' as DisputeOrder, label: 'Value' },
];

export default function PortfoiloDisputeTab() {
  const [sortBy, setSortBy] = useState<DisputeOrder>('NEWEST');
  const [page, setPage] = useState(0);

  const {
    data: disputesData,
    isLoading,
    error,
  } = usePortfolioDisputes({
    page,
    limit: 20,
    order: sortBy,
  });

  if (isLoading && page === 0) {
    return (
      <div className="mt-space-30">
        <div className="flex items-center justify-center py-20">
          <div className="text-gray-600">Loading disputes...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mt-space-30">
        <div className="flex items-center justify-center py-20">
          <div className="text-red-600">Failed to load disputes</div>
        </div>
      </div>
    );
  }

  const disputes = disputesData?.disputes || [];

  return (
    <div className="mt-space-30">
      {/* 필터 영역 */}
      <div className="mb-space-20 gap-space-10 flex">
        {/* 정렬 필터 */}
        <BaseSelect value={sortBy} onValueChange={value => setSortBy(value as DisputeOrder)}>
          <BaseSelectTrigger className="w-[150px]">
            <BaseSelectValue placeholder="Sort by" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {sortOptions.map(option => (
              <BaseSelectItem key={option.value} value={option.value}>
                {option.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>
      </div>

      {/* 테이블 */}
      <div className="w-full">
        <Table>
          <TableHeader>
            <TableRow className="border-b-line">
              <TableHead className="text-size-sm text-gray-3 font-semibold">Market</TableHead>
              <TableHead className="text-size-sm text-gray-3 font-semibold">Deposit</TableHead>
              <TableHead className="text-size-sm text-gray-3 font-semibold">Status</TableHead>
              <TableHead className="text-size-sm text-gray-3 font-semibold">Reward</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {disputes.length > 0 ? (
              disputes.map(dispute => (
                <TableRow key={dispute.id} className="hover:bg-gray-1 border-0">
                  <TableCell>
                    <TableMarketColumn
                      title={dispute.marketTitle}
                      avatarUrl={dispute.marketImageUrl || ''}
                      outcome={{
                        label: 'Dispute',
                        order: 1,
                      }}
                    />
                  </TableCell>
                  <TableCell>
                    <div className="text-mid-dark text-size-sm font-medium">{dispute.amount}</div>
                  </TableCell>
                  <TableCell>
                    <div className="text-size-sm font-medium text-gray-600">
                      {dispute.marketStatusText}
                    </div>
                  </TableCell>
                  <TableCell>
                    <div className="text-size-sm font-medium text-gray-600">{dispute.reward}</div>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={4} className="py-10 text-center">
                  <div className="text-gray-600">No disputes found</div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
