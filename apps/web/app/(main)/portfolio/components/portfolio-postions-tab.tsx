'use client';

import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/ui/base.select';
import TableMarketColumn from '@/components/ui/TableMarketColumn';
import { usePortfolioPositions } from '@/hooks/query/portfolio';
import { PortfolioPositionItem } from '@/hooks/query/portfolio/use-portfolio-positions';
import { PositionFilter, PositionOrder } from '@/lib/api/portfolio/portfolio.schema.server';
import { formatCurrency } from '@/lib/format';
import ClaimAllButton from '@/components/actions/claim-all-button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/ui/components/table';
import { cn } from '@repo/ui/lib/utils';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { useState } from 'react';

const statusOptions = [
  { value: 'all' as PositionFilter, label: 'All' },
  { value: 'live' as PositionFilter, label: 'Live' },
  { value: 'ended' as PositionFilter, label: 'Ended' },
];

const sortOptions = [
  { value: 'volume' as PositionOrder, label: 'Volume' },
  { value: 'newest' as PositionOrder, label: 'Newest' },
];

export const columns: ColumnDef<PortfolioPositionItem>[] = [
  {
    accessorKey: 'market',
    header: 'Market',
    cell: ({ row }) => {
      const position = row.original;
      return (
        <TableMarketColumn
          title={position.marketTitle}
          avatarUrl={position.marketImageUrl || ''}
          outcome={{
            label: position.outcome,
            order: 1,
          }}
        />
      );
    },
  },
  {
    accessorKey: 'value',
    header: () => <div className="text-right">Value</div>,
    cell: ({ row }) => (
      <div className="text-mid-dark text-size-sm font-medium">
        {formatCurrency(row.original.value)}
      </div>
    ),
  },
  {
    accessorKey: 'profit/loss',
    header: () => <div className="text-right">To Win</div>,
    cell: ({ row }) => (
      <div className="flex items-center justify-end gap-1">
        <span className={cn('text-size-sm font-semibold')}>
          ${row.original.estimatedWinFormatted}
        </span>
        <span
          className={cn('text-size-sm font-semibold')}
        >{`(${row.original.estimatedOddsFormartted})`}</span>
      </div>
    ),
  },
];

export default function PortfolioPositionsTab() {
  const [statusFilter, setStatusFilter] = useState<PositionFilter>('LIVE');
  const [sortBy, setSortBy] = useState<PositionOrder>('VOLUME');
  const [page, setPage] = useState(0);
  const [rowSelection, setRowSelection] = useState({});

  const {
    data: positionsData,
    isLoading,
    error,
  } = usePortfolioPositions({
    page,
    limit: 20,
    filter: statusFilter === 'LIVE' ? undefined : statusFilter,
    order: sortBy,
  });
  const positions = positionsData?.positions || [];
  const claimableMarkets = positions
    .filter(
      position =>
        position.marketStatus.startsWith('CLOSED') || position.marketStatus.startsWith('CANCELLED')
    )
    .reduce((acc, position) => {
      const existingMarket = acc.find(market => market.marketId === position.marketId);
      if (existingMarket) {
        existingMarket.estimatedWinFormatted = (
          parseFloat(existingMarket.estimatedWinFormatted) +
          parseFloat(position.estimatedWinFormatted)
        ).toString();
      } else {
        acc.push({
          marketId: position.marketId,
          marketTitle: position.marketTitle,
          marketImageUrl: position.marketImageUrl,
          value: position.value,
          marketStatus: position.marketStatus,
          outcome: position.outcome,
          outcomeOrder: position.outcomeOrder,
          estimatedWinFormatted: position.estimatedWinFormatted,
          estimatedOddsFormartted: position.estimatedOddsFormartted,
        });
      }

      return acc;
    }, [] as PortfolioPositionItem[]);

  const table = useReactTable<PortfolioPositionItem>({
    data: positions,
    columns,
    getCoreRowModel: getCoreRowModel(),
    onRowSelectionChange: setRowSelection,
    state: {
      rowSelection,
    },
  });

  if (isLoading && page === 0) {
    return (
      <div className={cn('mt-space-30')}>
        <div className="flex items-center justify-center py-20">
          <div className="text-gray-600">Loading positions...</div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={cn('mt-space-30')}>
        <div className="flex items-center justify-center py-20">
          <div className="text-red-600">Failed to load positions</div>
        </div>
      </div>
    );
  }

  return (
    <div className={cn('mt-space-30')}>
      {/* 필터 영역 */}
      <div className="mb-space-20 gap-space-10 flex justify-between">
        <div className="gap-space-10 flex">
          {/* 상태 필터 */}
          <BaseSelect
            value={statusFilter}
            onValueChange={value => setStatusFilter(value as PositionFilter)}
          >
            <BaseSelectTrigger className="w-[150px]">
              <BaseSelectValue placeholder="Status" />
            </BaseSelectTrigger>
            <BaseSelectContent>
              {statusOptions.map(option => (
                <BaseSelectItem key={option.value} value={option.value}>
                  {option.label}
                </BaseSelectItem>
              ))}
            </BaseSelectContent>
          </BaseSelect>

          {/* 정렬 필터 */}
          <BaseSelect value={sortBy} onValueChange={value => setSortBy(value as PositionOrder)}>
            <BaseSelectTrigger className="w-[150px]">
              <BaseSelectValue placeholder="Sort by" />
            </BaseSelectTrigger>
            <BaseSelectContent>
              {sortOptions.map(option => (
                <BaseSelectItem key={option.value} value={option.value}>
                  {option.label}
                </BaseSelectItem>
              ))}
            </BaseSelectContent>
          </BaseSelect>
        </div>

        {/* Claim All 버튼 */}
        <ClaimAllButton positions={claimableMarkets} />
      </div>

      {/* 테이블 */}
      <div className="w-full">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id} className="border-b-line">
                {headerGroup.headers.map(header => (
                  <TableHead className="text-size-sm text-gray-3 font-semibold" key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow key={row.id} className="hover:bg-gray-1 border-0">
                  {row.getVisibleCells().map(cell => (
                    <TableCell key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="py-10 text-center">
                  <div className="text-gray-600">No positions found</div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
