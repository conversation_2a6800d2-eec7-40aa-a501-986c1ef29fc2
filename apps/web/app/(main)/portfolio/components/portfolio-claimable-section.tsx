'use client';

import { pxToRem } from '@repo/ui/lib/utils';
import { useClaimableDispute } from '@/hooks/query/portfolio';
import { formatCurrency } from '@/lib/format';
import { useMemo } from 'react';
import DisputeClaimButton from '@/components/actions/dispute-claim-button';

export default function PortfolioClaimableSection() {
  const { data: claimableData, isLoading: claimableLoading } = useClaimableDispute();

  const claimableAmount = useMemo(() => {
    return claimableData?.formattedClaimable || 0;
  }, [claimableData]);

  return (
    <div
      style={
        {
          '--value-input-height': pxToRem(42),
          '--base-button-width': pxToRem(140),
        } as React.CSSProperties
      }
      className="gap-space-30 pb-space-40 border-b-line flex flex-col border-b"
    >
      <h2 className="dashboard-h2">Deposits & Rewards</h2>
      <div className="gap-space-15 flex flex-col">
        <h3 className="dashboard-h3">Claimable</h3>
        <div className="gap-space-10 flex h-(--value-input-height)">
          <div className="border-line px-space-20 bg-gray-2 flex h-full min-w-[510px] items-center justify-end border">
            <span className="text-mid-dark text-size-lg font-bold">
              {claimableLoading ? 'Loading...' : formatCurrency(claimableAmount)}
            </span>
          </div>
          <DisputeClaimButton className="h-full w-(--base-button-width)" />
        </div>
      </div>
    </div>
  );
}
