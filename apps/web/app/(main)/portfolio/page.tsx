'use client';

import { BaseButton } from '@/components/ui/base.button';
import WithdrawButton from '@/components/actions/withdraw-button';
import {
  BaseTabs,
  BaseTabsList,
  BaseTabsTrigger,
  BaseTabsContent,
} from '@/components/ui/base.tabs';
import PortfolioPositionsTab from './components/portfolio-postions-tab';
import PortfolioActivityTab from './components/portfolio-activities-tab';
import PortfoiloDisputeTab from './components/portfolio-dispute-tab';
import PortfolioDashboard from './components/portfolio-dashboard';
import PortfolioClaimableSection from './components/portfolio-claimable-section';
import { useClaimableDispute, useMyPortfolio } from '@/hooks/query/portfolio';
import { formatCurrency } from '@/lib/format';
import { useMemo } from 'react';

export default function PortfolioPage() {
  const { data: claimableData, isLoading: claimableLoading } = useClaimableDispute();
  const { data: portfolioData } = useMyPortfolio();

  const portfolioMetrics = useMemo(() => {
    return {
      claimableAmount: claimableData?.formattedClaimable || 0,
      portfolioValue: portfolioData?.formattedPositionsValue || 0,
    };
  }, [claimableData, portfolioData]);

  return (
    <div className="page">
      <h1 className="text-size-lg text-mid-dark mb-space-30 font-bold">Portfolio</h1>

      <PortfolioDashboard />

      {/* Funds Section */}
      <section className="gap-space-60 flex items-center">
        <div className="px-space-20 py-space-30 flex-1">
          <div className="text-size-sm font-semibold">Funds</div>
        </div>
        <div className="px-space-20 py-space-30 flex flex-1 items-center justify-between">
          <div className="pl-space-20 text-size-xl text-mid-dark font-bold">
            {portfolioMetrics ? formatCurrency(portfolioMetrics.portfolioValue) : '$0'}
          </div>
          <div className="gap-space-20 pr-space-20 flex items-center">
            <BaseButton className="px-space-60" variant="info">
              Deposit
            </BaseButton>
            <WithdrawButton className="px-space-60" />
          </div>
        </div>
      </section>

      {/* Tabs Section */}
      <section>
        <BaseTabs defaultValue="positions">
          <div className="border-b-line border-b">
            <BaseTabsList className="border-none">
              <BaseTabsTrigger className="min-w-(--tab-trigger-width)" value="positions">
                Positions
              </BaseTabsTrigger>
              <BaseTabsTrigger className="min-w-(--tab-trigger-width)" value="activity">
                Activity
              </BaseTabsTrigger>
              <BaseTabsTrigger className="min-w-(--tab-trigger-width)" value="dispute">
                Dispute
              </BaseTabsTrigger>
            </BaseTabsList>
          </div>
          <BaseTabsContent value="positions">
            <PortfolioPositionsTab />
          </BaseTabsContent>
          <BaseTabsContent value="activity">
            <PortfolioActivityTab />
          </BaseTabsContent>
          <BaseTabsContent value="dispute">
            <PortfolioClaimableSection />
            <PortfoiloDisputeTab />
          </BaseTabsContent>
        </BaseTabs>
      </section>
    </div>
  );
}
