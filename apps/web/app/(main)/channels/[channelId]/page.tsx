'use client';

import {
  PredictionMarketCard,
  PredictionMarketCardSkeleton,
} from '@/components/common/prediction-market-card';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/ui/base.select';
import { useChannelMarkets } from '@/hooks/query/channel';
import { useParams } from 'next/navigation';
import { useState } from 'react';

type FilterOption = 'live' | 'ended';
const filterOptions = [
  { value: 'live', label: 'Live' },
  { value: 'ended', label: 'Resolved' },
];

export default function ChannelDetailPage() {
  const params = useParams();
  const channelId = params.channelId as string;
  const [filter, setFilter] = useState<FilterOption>('live');

  const {
    data: marketsData,
    isLoading,
    error,
  } = useChannelMarkets(channelId, {
    status: filter,
    order: 'createdAt',
  });

  // 에러 상태 처리
  if (error) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-red-500">Failed to load markets</div>
      </div>
    );
  }

  const markets = marketsData?.markets || [];

  // 상태별 카운트 - 실제 데이터 기반으로 계산
  const totalCount = markets.length;
  const disputeCount = markets.filter(market => market.marketStatusText === 'Win a dispute').length;
  const noDisputeCount = totalCount - disputeCount;

  return (
    <div className="flex flex-col">
      <div className="mb-space-20 mt-space-40 flex items-center justify-between">
        {/* 필터 */}
        <BaseSelect value={filter} onValueChange={value => setFilter(value as FilterOption)}>
          <BaseSelectTrigger className="w-[150px]">
            <BaseSelectValue placeholder="필터" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {filterOptions.map(option => (
              <BaseSelectItem key={option.value} value={option.value}>
                {option.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>

        {/* 상태 카운트 */}
        <div className="gap-space-15 text-size-sm flex items-center">
          <span className="text-mid-dark">
            <span className="font-semibold">Total:</span> {totalCount}
          </span>
          <span className="text-mid-dark">
            <span className="font-semibold">Dispute:</span> {disputeCount}
          </span>
          <span className="text-mid-dark">
            <span className="font-semibold">No dispute:</span> {noDisputeCount}
          </span>
        </div>
      </div>

      {/* 마켓 카드 그리드 */}
      {isLoading ? (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <PredictionMarketCardSkeleton key={i} />
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {markets.map(market => (
            <PredictionMarketCard key={market.marketId} item={market} />
          ))}
        </div>
      )}
    </div>
  );
}
