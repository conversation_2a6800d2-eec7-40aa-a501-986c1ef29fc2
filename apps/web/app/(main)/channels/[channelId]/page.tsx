'use client';

import {
  PredictionMarketCard,
  PredictionMarketCardSkeleton,
} from '@/components/common/prediction-market-card';
import EmptyMarketList from '@/components/common/empty-list';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/ui/base.select';
import { useChannelMarkets } from '@/hooks/query/channel';
import { MarketStatus } from '@/lib/api/channel/channel.schema.server';
import { useChannelId } from '@/hooks/use-channel-id';
import { useState } from 'react';

type FilterOption = MarketStatus;
const filterOptions = [
  { value: 'LIVE', label: 'Live' },
  { value: 'ENDED', label: 'Resolved' },
];

export default function ChannelDetailPage() {
  const channelId = useChannelId();
  const [filter, setFilter] = useState<FilterOption>('LIVE');

  const {
    data: marketsData,
    isLoading,
    error,
  } = useChannelMarkets(channelId, {
    status: filter,
    order: 'CREATED_AT',
  });

  // 에러 상태 처리
  if (error) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-red-500">Failed to load markets</div>
      </div>
    );
  }
  const markets = marketsData?.markets || [];
  return (
    <div className="flex flex-col">
      <div className="mb-space-20 mt-space-40 flex items-center justify-between">
        {/* 필터 */}
        <BaseSelect value={filter} onValueChange={value => setFilter(value as FilterOption)}>
          <BaseSelectTrigger className="w-[150px]">
            <BaseSelectValue placeholder="필터" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {filterOptions.map(option => (
              <BaseSelectItem key={option.value} value={option.value}>
                {option.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>
      </div>

      {/* 마켓 카드 그리드 */}
      {isLoading ? (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <PredictionMarketCardSkeleton key={i} />
          ))}
        </div>
      ) : markets.length === 0 ? (
        <div className="flex min-h-[400px] items-center justify-center">
          <EmptyMarketList />
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {markets.map(market => (
            <PredictionMarketCard key={market.marketId} item={market} />
          ))}
        </div>
      )}
    </div>
  );
}
