'use client';

import EmptyMarketList from '@/components/common/empty-list';
import {
  PredictionMarketCard,
  PredictionMarketCardSkeleton,
} from '@/components/common/prediction-market-card';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/ui/base.select';
import { useChannelMarkets } from '@/hooks/query/channel';
import { useChannelId } from '@/hooks/use-channel-id';
import { GetMarketFilterEnum } from '@/lib/api/market/market.schema.server';
import { useState } from 'react';

const filterOptions = [
  { value: 'LIVE', label: 'Live' },
  { value: 'ENDED', label: 'Resolved' },
];

export default function ChannelDetailPage() {
  const channelId = useChannelId();
  const [filter, setFilter] = useState<GetMarketFilterEnum>('LIVE');

  const {
    data: marketsData,
    isLoading,
    error,
  } = useChannelMarkets(channelId, {
    filter,
    order: 'NEWEST',
    // TODO: add paginations
  });

  // TODO: Proper 에러 상태 처리
  if (error) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-red-500">Failed to load markets</div>
      </div>
    );
  }
  const markets = marketsData?.markets || [];
  return (
    <div className="flex flex-col">
      <div className="mb-space-20 mt-space-40 flex items-center justify-between">
        {/* 필터 */}
        <BaseSelect value={filter} onValueChange={value => setFilter(value as GetMarketFilterEnum)}>
          <BaseSelectTrigger className="w-[150px]">
            <BaseSelectValue placeholder="필터" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {filterOptions.map(option => (
              <BaseSelectItem key={option.value} value={option.value}>
                {option.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>
      </div>

      {/* 마켓 카드 그리드 */}
      {isLoading ? (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 8 }).map((_, i) => (
            <PredictionMarketCardSkeleton key={i} />
          ))}
        </div>
      ) : markets.length === 0 ? (
        <div className="flex min-h-[400px] items-center justify-center">
          <EmptyMarketList />
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {markets.map(market => (
            <PredictionMarketCard key={market.marketId} item={market} />
          ))}
        </div>
      )}
    </div>
  );
}
