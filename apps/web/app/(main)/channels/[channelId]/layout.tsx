'use client';
import SvgIcon from '@/components/icons/svg-icon';
import CommonAvatar from '@/components/ui/avatar-image';
import { BaseButton } from '@/components/ui/base.button';
import { BaseTabs, BaseTabsList, BaseTabsTrigger } from '@/components/ui/base.tabs';
import { useChannelByIdWithUser } from '@/hooks/query/channel';
import { useToggleChannelSubscription } from '@/hooks/query/channel/use-toggle-channel-subscription';
import { INNER_LINKS } from '@/lib/constants';
import { Switch } from '@repo/ui/components/switch';
import { pxToRem } from '@repo/ui/lib/utils';
import Link from 'next/link';
import { useParams, usePathname } from 'next/navigation';
import { useEffect, useState } from 'react';
import ChannelBanner from './components/channel-banner';
import PredictGoLoading from '@/components/icons/predict-go-loading';

export default function ChannelLayout({ children }: { children: React.ReactNode }) {
  const params = useParams();
  const pathname = usePathname();
  const channelId = params.channelId as string;

  const { data, isLoading, error, refetch } = useChannelByIdWithUser(channelId);
  const toggleSubscription = useToggleChannelSubscription();
  const [optimisticSubscribed, setOptimisticSubscribed] = useState<boolean | undefined>(
    data?.channelIsSubscribed
  );

  useEffect(() => {
    setOptimisticSubscribed(data?.channelIsSubscribed);
  }, [data?.channelIsSubscribed]);

  const handleToggleSubscription = async () => {
    if (!channelId || typeof optimisticSubscribed !== 'boolean') return;
    const previousState = optimisticSubscribed;
    setOptimisticSubscribed(!optimisticSubscribed);

    try {
      await toggleSubscription.mutateAsync({
        channelId: channelId,
        isCurrentlySubscribed: optimisticSubscribed,
      });

      await refetch();
    } catch (error) {
      setOptimisticSubscribed(previousState);
      console.error('Failed to toggle subscription:', error);
    }
  };

  const getActiveTab = () => {
    if (pathname.includes('/leaderboard')) return 'leaderboard';
    if (pathname.includes('/posts')) return 'posts';
    return '';
  };

  if (isLoading || !data) {
    return (
      <div className="page p-4">
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/10">
            <PredictGoLoading size="xl" />
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="page p-4">
        <div className="flex min-h-[400px] items-center justify-center">
          <div className="text-lg text-red-500">
            {error ? 'Failed to load channel' : 'Channel not found'}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="page p-4">
      <section className="mb-8">
        <div className="mb-6">
          <ChannelBanner imageUrl={data.channelBannerUrl} />
        </div>
        <div className="mb-6 flex items-start gap-6">
          <div className="shrink-0">
            <CommonAvatar
              imageUrl={data.channelAvatarUrl}
              size="lg"
              alt={data.channelName}
              className="h-20 w-20"
            />
          </div>
          <div className="flex-1">
            <h1 className="text-dark-deep mb-2 text-2xl font-bold">{data.channelName}</h1>

            <div className="mb-3 flex items-center gap-4">
              <Link
                href={INNER_LINKS.MAIN.ADDRESS_POSITIONS(channelId)}
                className="flex items-center"
              >
                <SvgIcon name="CrownIcon" className="size-[14px]" />
                <span className="text-size-xs text-gray-3">{data.userNickname}</span>
              </Link>
              <div className="flex items-center text-sm text-gray-500">
                <SvgIcon name="MemberIcon" className="size-[14px]" />
                <span className="text-size-xs text-mid-dark font-semibold">
                  {data.channelIsLeaderLive ? 'Online' : 'Offline'}
                </span>
              </div>
            </div>

            <div className="gap-space-20 text-size-xs flex">
              <div className="gap-space-5 flex items-center">
                <SvgIcon name="SubscriptionInversedIcon" />
                <span className="text-gray-3">Subscribers</span>
                <span className="text-icon-dark">{data.channelSubscribers.toLocaleString()}</span>
              </div>
              <div className="gap-space-5 flex items-center">
                <SvgIcon name="DollarIcon" />
                <span className="text-gray-3">Total Volume: </span>
                <span className="text-icon-dark">
                  ${Number(data.channelTotalVolume).toLocaleString()}
                </span>
              </div>
              <div className="gap-space-5 flex items-center">
                <SvgIcon name="MarketInversedIcon" />
                <span className="text-gray-3">Total Markets: </span>
                <span className="text-icon-dark">{data.channelTotalMarkets}</span>
              </div>
              <div className="gap-space-5 flex items-center">
                <SvgIcon name="SubscriptionInversedIcon" />
                <span className="text-gray-3">Subscribers: </span>
                <span className="text-icon-dark">{data.channelSubscribers.toLocaleString()}</span>
              </div>
              {!data.isMyChannel && (
                <div className="gap-space-5 flex items-center">
                  <Switch
                    id={`subscribe-${channelId}`}
                    checked={optimisticSubscribed || false}
                    onCheckedChange={handleToggleSubscription}
                    disabled={toggleSubscription.isPending}
                    className="data-[state=checked]:bg-sky data-[state=unchecked]:bg-gray-3"
                  />
                  <label htmlFor={`subscribe-${channelId}`} className="text-size-xs">
                    {optimisticSubscribed ? 'Subscribed' : 'Subscribe'}
                  </label>
                </div>
              )}
            </div>
            {data.channelDescription && (
              <div className="text-size-sm text-gray-3 mt-space-20">{data.channelDescription}</div>
            )}
          </div>

          <div className="gap-space-10 flex">
            {data.channelSns.map((social, index) => (
              <a
                key={`${social.snsType}-${index}`}
                href={social.snsUrl || '#'}
                target="_blank"
                rel="noopener noreferrer"
                className="rounded-full transition-colors hover:bg-gray-100"
                aria-label={social.snsType}
              >
                <SvgIcon name="DiscordIcon" className="size-[24px]" />
              </a>
            ))}
          </div>
        </div>
        {data.isMyChannel && (
          <div className="flex justify-end">
            <Link href={INNER_LINKS.MAIN.CREATE_PREDICTION} className="flex items-center">
              <BaseButton variant="info" size="lg" className="w-[256px] px-8">
                <SvgIcon data-label="icon" name="MarketIcon" className="size-[24px] text-white" />
                Create Prediction
              </BaseButton>
            </Link>
          </div>
        )}
      </section>

      <section
        style={
          {
            '--tab-trigger-width': pxToRem(200),
          } as React.CSSProperties
        }
      >
        <BaseTabs defaultValue="markets">
          <div className="border-b-line border-b">
            <BaseTabsList className="border-none">
              <Link href={INNER_LINKS.MAIN.CHANNELS.DETAIL(channelId)}>
                <BaseTabsTrigger
                  className="min-w-(--tab-trigger-width)"
                  value="markets"
                  data-state={getActiveTab() === '' ? 'active' : 'inactive'}
                >
                  Markets
                </BaseTabsTrigger>
              </Link>
              <Link href={INNER_LINKS.MAIN.CHANNELS.LEADERBOARD(channelId)}>
                <BaseTabsTrigger
                  className="min-w-(--tab-trigger-width)"
                  value="leaderboard"
                  data-state={getActiveTab() === 'leaderboard' ? 'active' : 'inactive'}
                >
                  Leaderboard
                </BaseTabsTrigger>
              </Link>
              <Link href={INNER_LINKS.MAIN.CHANNELS.POSTS.ROOT(channelId)}>
                <BaseTabsTrigger
                  className="min-w-(--tab-trigger-width)"
                  value="posts"
                  data-state={getActiveTab() === 'posts' ? 'active' : 'inactive'}
                >
                  Posts
                </BaseTabsTrigger>
              </Link>
            </BaseTabsList>
          </div>
          {children}
        </BaseTabs>
      </section>
    </div>
  );
}
