'use client';
import SvgIcon from '@/components/icons/svg-icon';
import CommonAvatar from '@/components/ui/avatar-image';
import { BaseButton } from '@/components/ui/base.button';
import { BaseTabs, BaseTabsList, BaseTabsTrigger } from '@/components/ui/base.tabs';
import { useChannelByIdWithUser } from '@/hooks/query/channel';
import { useChannelCollateral } from '@/hooks/query/channel/use-channel-collateral';
import { useToggleChannelSubscription } from '@/hooks/query/channel/use-toggle-channel-subscription';
import { INNER_LINKS } from '@/lib/constants';
import { Skeleton } from '@repo/ui/components/skeleton';
import { pxToRem } from '@repo/ui/lib/utils';
import Link from 'next/link';
import { usePathname, useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import { useChannelId } from '@/hooks/use-channel-id';
import ChannelBanner from './components/channel-banner';
import ChannelStats from './components/channel-stats';
import ChannelSubscription from './components/channel-subscription';
import { useGlobalStore } from '@/store/global.store';
import { Popup } from '@/components/ui/popup';
import InsufficientDepositBalancePopup from '@/components/ui/popup/insufficient-deposit-balance-popup';

// Loading skeleton component
function ChannelSkeleton() {
  return (
    <div className="page p-4">
      <section className="mb-8">
        <div className="mb-6">
          <Skeleton className="h-(--banner-image-height) w-full" />
        </div>
        <div className="mb-6 flex items-start gap-6">
          <div className="shrink-0">
            <Skeleton className="h-20 w-20 rounded-full" />
          </div>
          <div className="flex-1">
            <Skeleton className="mb-2 h-8 w-64" />
            <div className="mb-3 flex items-center gap-4">
              <Skeleton className="h-4 w-24" />
            </div>
            <div className="gap-space-20 flex">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-32" />
              <Skeleton className="h-4 w-28" />
            </div>
          </div>
          <div className="gap-space-10 flex">
            <Skeleton className="h-6 w-6 rounded-full" />
          </div>
        </div>
      </section>
    </div>
  );
}

function ChannelError() {
  return (
    <div className="page p-4">
      <div className="flex min-h-[400px] items-center justify-center">
        <div className="gap-space-20 flex max-w-md flex-col items-center justify-center text-center">
          <SvgIcon name="ExcalmationIcon" className="size-[48px] text-gray-400" />
          <div className="gap-space-10 flex flex-col items-center">
            <h2 className="text-size-xl font-bold text-gray-600">Channel Not Found</h2>
            <p className="text-size-base text-gray-500">
              We couldn't find the channel you're looking for. It may have been moved, deleted, or
              you may not have permission to view it.
            </p>
          </div>
          <div className="gap-space-10 flex flex-wrap justify-center">
            <BaseButton
              variant="outline"
              size="md"
              onClick={() => window.location.reload()}
              className="min-w-[100px]"
            >
              <SvgIcon name="CheckIcon" className="mr-2 size-4" />
              Retry
            </BaseButton>
            <Link href={INNER_LINKS.HOME}>
              <BaseButton variant="default" size="md" className="min-w-[100px]">
                <SvgIcon name="ChannelIcon" className="mr-2 size-4" />
                Go Home
              </BaseButton>
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}

// User info component
function UserInfo({
  userNickname,
  isLeaderLive,
  channelId,
}: {
  userNickname?: string;
  isLeaderLive?: boolean;
  channelId: string;
}) {
  return (
    <div className="mb-3 flex items-center gap-4">
      <Link
        href={INNER_LINKS.MAIN.ADDRESS_POSITIONS(channelId)}
        className="gap-space-5 flex items-center"
      >
        <SvgIcon name="CrownIcon" className="size-[14px]" />
        <span className="text-size-xs text-gray-3">
          {userNickname || <Skeleton className="h-4 w-16" />}
        </span>
        {isLeaderLive !== undefined && (
          <div
            className={`ml-[1px] size-[6px] rounded-full ${
              isLeaderLive ? 'bg-red-500' : 'bg-gray-400'
            }`}
          />
        )}
      </Link>
    </div>
  );
}

// Social links component
function SocialLinks({ channelSns }: { channelSns?: Array<{ snsType: string; snsUrl?: string }> }) {
  return (
    <div className="gap-space-10 flex">
      {channelSns ? (
        channelSns.map((social, index) => (
          <a
            key={`${social.snsType}-${index}`}
            href={social.snsUrl || '#'}
            target="_blank"
            rel="noopener noreferrer"
            className="rounded-full transition-colors hover:bg-gray-100"
            aria-label={social.snsType}
          >
            <SvgIcon name="DiscordIcon" className="size-[24px]" />
          </a>
        ))
      ) : (
        <Skeleton className="h-6 w-6 rounded-full" />
      )}
    </div>
  );
}

// Tab navigation component
function TabNavigation({
  channelId,
  getActiveTab,
}: {
  channelId: string;
  getActiveTab: () => string;
}) {
  const tabs = [
    {
      value: 'markets',
      label: 'Markets',
      href: INNER_LINKS.MAIN.CHANNELS.DETAIL(channelId),
      active: '',
    },
    {
      value: 'leaderboard',
      label: 'Leaderboard',
      href: INNER_LINKS.MAIN.CHANNELS.LEADERBOARD(channelId),
      active: 'leaderboard',
    },
    {
      value: 'posts',
      label: 'Posts',
      href: INNER_LINKS.MAIN.CHANNELS.POSTS.ROOT(channelId),
      active: 'posts',
    },
  ];

  return (
    <section style={{ '--tab-trigger-width': pxToRem(200) } as React.CSSProperties}>
      <BaseTabs defaultValue="markets">
        <div className="border-b-line border-b">
          <BaseTabsList className="border-none">
            {tabs.map(tab => (
              <Link key={tab.value} href={tab.href}>
                <BaseTabsTrigger
                  className="min-w-(--tab-trigger-width)"
                  value={tab.value}
                  data-state={getActiveTab() === tab.active ? 'active' : 'inactive'}
                >
                  {tab.label}
                </BaseTabsTrigger>
              </Link>
            ))}
          </BaseTabsList>
        </div>
      </BaseTabs>
    </section>
  );
}

export default function ChannelLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname();
  const router = useRouter();
  const channelId = useChannelId();
  const { data, isLoading, error, refetch } = useChannelByIdWithUser(channelId);
  const { data: collateralData } = useChannelCollateral();
  const toggleSubscription = useToggleChannelSubscription();
  const currentUserAddress = useGlobalStore(v => v.safeSmartAccount?.address);
  const [optimisticSubscribed, setOptimisticSubscribed] = useState<boolean | undefined>(
    data?.channelIsSubscribed
  );
  const [isInsufficientDepositPopupOpen, setIsInsufficientDepositPopupOpen] = useState(false);

  useEffect(() => {
    setOptimisticSubscribed(data?.channelIsSubscribed);
  }, [data?.channelIsSubscribed]);

  const handleToggleSubscription = async () => {
    if (!channelId || typeof optimisticSubscribed !== 'boolean') return;
    const previousState = optimisticSubscribed;
    setOptimisticSubscribed(!optimisticSubscribed);

    try {
      await toggleSubscription.mutateAsync({
        channelId: channelId,
        isCurrentlySubscribed: optimisticSubscribed,
      });
      await refetch();
    } catch (error) {
      setOptimisticSubscribed(previousState);
    }
  };

  const getActiveTab = () => {
    if (pathname.includes('/leaderboard')) return 'leaderboard';
    if (pathname.includes('/posts')) return 'posts';
    return '';
  };

  if (isLoading || !data) {
    return <ChannelSkeleton />;
  }

  if (error) {
    return <ChannelError />;
  }

  const renderChannelSubscription = () => {
    if (!currentUserAddress) return null;
    if (!data?.userAddress) return null;
    if (currentUserAddress.toLowerCase() === data.userAddress.toLowerCase()) return null;

    return (
      <ChannelSubscription
        channelName={data.channelName}
        channelId={channelId}
        isSubscribed={optimisticSubscribed || false}
        onToggle={handleToggleSubscription}
        isLoading={toggleSubscription.isPending}
      />
    );
  };

  const handleCreatePrediction = () => {
    const availableBalance = Number(collateralData?.totalFormatted) || 0;

    if (availableBalance < 50) {
      setIsInsufficientDepositPopupOpen(true);
    } else {
      router.push(INNER_LINKS.MAIN.CREATE_PREDICTION);
    }
  };

  const handleAddDeposit = () => {
    setIsInsufficientDepositPopupOpen(false);
    router.push(INNER_LINKS.PROFILE.CHANNELS.DEPOSITS);
  };

  const handleCancelDeposit = () => {
    setIsInsufficientDepositPopupOpen(false);
  };

  const renderCreatePredictionButton = () => {
    if (!currentUserAddress) return null;
    if (!data?.userAddress) return null;
    if (currentUserAddress.toLowerCase() !== data.userAddress.toLowerCase()) return null;

    return (
      <BaseButton
        variant="info"
        size="lg"
        className="w-[256px] px-8"
        onClick={handleCreatePrediction}
      >
        <SvgIcon data-label="icon" name="MarketIcon" className="size-[24px] text-white" />
        Create Prediction
      </BaseButton>
    );
  };

  return (
    <div className="page p-4">
      <section className="mb-8">
        <div className="mb-6">
          <ChannelBanner imageUrl={data.channelBannerUrl} />
        </div>

        <div className="mb-6 flex items-start gap-6">
          <div className="shrink-0">
            <CommonAvatar
              imageUrl={data.channelAvatarUrl}
              size="lg"
              alt={data.channelName}
              className="h-20 w-20"
            />
          </div>

          <div className="flex-1">
            <h1 className="text-dark-deep mb-2 text-2xl font-bold">{data.channelName}</h1>

            <UserInfo
              userNickname={data.userNickname}
              isLeaderLive={data.channelIsLeaderLive}
              channelId={channelId}
            />

            <div className="gap-space-20 flex flex-wrap">
              <ChannelStats
                subscribers={data.channelSubscribers}
                totalVolume={data.channelTotalVolume}
                totalMarkets={data.channelTotalMarkets}
                isLoading={isLoading}
              />

              {renderChannelSubscription()}
            </div>

            {data.channelDescription && (
              <div className="text-size-sm text-gray-3 mt-space-20">{data.channelDescription}</div>
            )}
          </div>
          <SocialLinks channelSns={data.channelSns} />
        </div>
        <div className="flex justify-end">{renderCreatePredictionButton()}</div>
      </section>

      <TabNavigation channelId={channelId} getActiveTab={getActiveTab} />
      {children}

      <Popup
        isOpen={isInsufficientDepositPopupOpen}
        onClose={handleCancelDeposit}
        showCloseButton={false}
      >
        <InsufficientDepositBalancePopup
          onConfirm={handleAddDeposit}
          onCancel={handleCancelDeposit}
        />
      </Popup>
    </div>
  );
}
