'use client';
import { useParams, useRouter } from 'next/navigation';
import PostCreateView from '../../components/post-create-view';
import { INNER_LINKS } from '@/lib/constants';

export default function CreatePostPage() {
  const params = useParams();
  const router = useRouter();
  const channelId = params.channelId as string;

  const handleBack = () => {
    router.push(INNER_LINKS.MAIN.CHANNELS.POSTS.ROOT(channelId));
  };

  return <PostCreateView onBack={handleBack} channelId={channelId} />;
}
