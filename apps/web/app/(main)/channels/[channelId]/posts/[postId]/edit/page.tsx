'use client';
import { usePost } from '@/hooks/query/board';
import { useParams, useRouter } from 'next/navigation';
import PostEditView from '../../../components/post-edit-view';
import { INNER_LINKS } from '@/lib/constants';

export default function EditPostPage() {
  const params = useParams();
  const router = useRouter();
  const channelId = params.channelId as string;
  const postId = params.postId as string;

  const { data: post, isLoading, error } = usePost(postId);

  const handleBack = () => {
    router.push(INNER_LINKS.MAIN.CHANNELS.POSTS.ROOT(channelId));
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-gray-500">Loading post...</div>
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-red-500">Failed to load post. Please try again.</div>
      </div>
    );
  }

  return (
    <PostEditView
      onBack={handleBack}
      channelId={channelId}
      postId={postId}
      initialData={{
        title: post.title,
        content: post.content,
      }}
    />
  );
}
