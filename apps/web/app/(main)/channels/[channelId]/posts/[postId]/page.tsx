'use client';
import { useParams, useRouter } from 'next/navigation';
import { useMemo } from 'react';
import PostDetailView from '../../components/post-detail-view';
import { useChannelPosts } from '@/hooks/query/board';
import { adaptApiPostsToClientPosts } from '../../components/post-adapter';
import { INNER_LINKS } from '@/lib/constants';

export default function PostDetailPage() {
  const params = useParams();
  const router = useRouter();
  const channelId = params.channelId as string;
  const postId = params.postId as string;

  // Fetch channel posts to find the specific post
  const {
    data: apiPostsData,
    isLoading,
    error,
  } = useChannelPosts(channelId, { page: 0, limit: 50 });

  // Convert API data to client Post types
  const posts = useMemo(() => {
    if (!apiPostsData) return [];
    return adaptApiPostsToClientPosts(apiPostsData);
  }, [apiPostsData]);

  // Find the specific post
  const selectedPost = posts.find(post => post.id === postId);

  const handleBack = () => {
    router.push(INNER_LINKS.MAIN.CHANNELS.POSTS.ROOT(channelId));
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-gray-500">Loading post...</div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-red-500">Failed to load post. Please try again.</div>
      </div>
    );
  }

  // Post not found
  if (!selectedPost) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-gray-500">Post not found.</div>
      </div>
    );
  }

  return <PostDetailView post={selectedPost} onBack={handleBack} />;
}
