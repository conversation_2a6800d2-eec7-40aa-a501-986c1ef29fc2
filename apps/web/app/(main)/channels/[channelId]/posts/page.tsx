'use client';
import { useParams, useRouter } from 'next/navigation';
import { useState, useMemo } from 'react';
import ChannelPostListView from '../components/channel-post-list-view';
import { FilterOption } from '../components/post-types';
import { useChannelPosts, useTogglePostPin, useLikePost } from '@/hooks/query/board';
import { adaptApiPostsToClientPosts } from '../components/post-adapter';
import { useGlobalStore } from '@/store/global.store';

export default function ChannelPostsPage() {
  const params = useParams();
  const router = useRouter();
  const channelId = params.channelId as string;
  const [filter, setFilter] = useState<FilterOption>('newest');

  // Check if current user is channel owner
  const { safeSmartAccount } = useGlobalStore();
  const isOwnChannel = safeSmartAccount?.address?.toLowerCase() === channelId.toLowerCase();

  // Fetch API data
  const {
    data: apiPostsData,
    isLoading,
    error,
    refetch: refetchChannelPosts,
  } = useChannelPosts(channelId, { page: 0, limit: 50 });
  const togglePinMutation = useTogglePostPin();
  const likePostMutation = useLikePost();

  // Convert API data to client Post types
  const posts = useMemo(() => {
    if (!apiPostsData) return [];
    return adaptApiPostsToClientPosts(apiPostsData);
  }, [apiPostsData]);

  // Filtered and sorted post list
  const filteredPosts = useMemo(() => {
    return [...posts].sort((a, b) => {
      // First, sort by pinned status (pinned posts at top)
      if (a.isPinned && !b.isPinned) return -1;
      if (!a.isPinned && b.isPinned) return 1;

      // Then sort by filter criteria
      if (filter === 'newest') {
        // Sort by newest (using createdAt ISO string)
        return new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime();
      } else {
        // Sort by popularity (likes)
        return b.likes - a.likes;
      }
    });
  }, [posts, filter]);

  // Filter change handler
  const handleFilterChange = (value: FilterOption) => {
    setFilter(value);
  };

  // Pin/unpin post handler
  const handleTogglePin = async (postId: string) => {
    try {
      const post = posts.find(p => p.id === postId);
      if (post) {
        await togglePinMutation.mutateAsync({
          postId,
          isPinned: post.isPinned || false,
        });
        // Refetch channel posts to update the list
        refetchChannelPosts();
      }
    } catch (error) {
      console.error('Failed to toggle pin:', error);
    }
  };

  // Like/unlike post handler
  const handleToggleHeart = async (postId: string) => {
    try {
      await likePostMutation.mutateAsync(postId);
      // Refetch channel posts to update the list with new like count
      refetchChannelPosts();
    } catch (error) {
      console.error('Failed to toggle like:', error);
    }
  };

  // Loading state
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-gray-500">Loading posts...</div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-red-500">Failed to load posts. Please try again.</div>
      </div>
    );
  }

  return (
    <ChannelPostListView
      posts={filteredPosts}
      channelId={channelId}
      filter={filter}
      onFilterChange={handleFilterChange}
      onTogglePin={handleTogglePin}
      onToggleHeart={handleToggleHeart}
      isOwnChannel={isOwnChannel}
    />
  );
}
