'use client';

import { PredictionMarketCard } from '@/components/common/prediction-market-card';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/ui/base.select';
import { useChannelById, useChannelMarkets } from '@/hooks/query/channel';
import { MarketStatus } from '@/lib/api/channel/channel.schema.server';
import { Channel } from '@/lib/types';
import { useState } from 'react';

// 필터 옵션 타입
type FilterOption = MarketStatus;

// 필터 옵션 목록
const filterOptions = [
  { value: 'live', label: 'Live' },
  { value: 'ended', label: 'Resolved' },
];

interface ChannelMarketTabProps {
  channelId: string;
}

export default function ChannelMarketTab({ channelId }: ChannelMarketTabProps) {
  const [filter, setFilter] = useState<FilterOption>('live');

  const { data, isLoading, error } = useChannelMarkets(channelId, {
    status: filter,
    order: 'createdAt',
  });

  // 채널 정보도 가져오기
  const { data: channelData } = useChannelById(channelId);

  // 로딩 상태 처리
  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-mid-dark">로딩 중...</div>
      </div>
    );
  }

  // 에러 상태 처리
  if (error) {
    return (
      <div className="flex items-center justify-center py-8">
        <div className="text-red-500">마켓 데이터를 불러오는 중 오류가 발생했습니다.</div>
      </div>
    );
  }

  const markets = data?.markets || [];

  // 채널 정보 설정 (기본값 포함)
  const channel: Channel = channelData
    ? {
        id: channelData.id,
        name: channelData.name,
        imageUrl: channelData.imageUrl || '',
      }
    : {
        id: channelId,
        name: 'Unknown Channel',
        imageUrl: '',
      };

  // 상태별 카운트 - 실제 데이터 기반으로 계산
  const totalCount = markets.length;
  // const disputeCount = markets.filter(market => market.marketStatus === 'DISPUTED').length;
  // const noDisputeCount = totalCount - disputeCount;

  return (
    <div className="flex flex-col">
      <div className="mb-space-20 flex items-center justify-between">
        {/* 필터 */}
        <BaseSelect value={filter} onValueChange={value => setFilter(value as FilterOption)}>
          <BaseSelectTrigger className="w-[150px]">
            <BaseSelectValue placeholder="필터" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {filterOptions.map(option => (
              <BaseSelectItem key={option.value} value={option.value}>
                {option.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>

        {/* 상태 카운트 */}
        <div className="gap-space-15 text-size-sm flex items-center">
          <span className="text-mid-dark">
            <span className="font-semibold">Total:</span> {totalCount}
          </span>
          <span className="text-mid-dark">
            {/* <span className="font-semibold">Dispute:</span> {disputeCount} */}
          </span>
          <span className="text-mid-dark">
            {/* <span className="font-semibold">No dispute:</span> {noDisputeCount} */}
          </span>
        </div>
      </div>

      {/* 마켓 카드 그리드 */}
      {markets.length === 0 ? (
        <div className="flex items-center justify-center py-8">
          <div className="text-mid-dark">마켓이 없습니다.</div>
        </div>
      ) : (
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {markets.map(market => (
            <PredictionMarketCard
              key={market.marketId}
              item={{
                ...market,
                marketMaxOutcomeVolume: market.marketTotalVolume, // Add missing required prop
              }}
            />
          ))}
        </div>
      )}
    </div>
  );
}
