// Filter option types
export type FilterOption = 'newest' | 'most_liked';

// Filter option list
export const filterOptions = [
  { value: 'newest', label: 'Newest' },
  { value: 'most_liked', label: 'Most Liked' },
];

// Post data type
export interface Post {
  id: string;
  username: string;
  avatarUrl: string;
  timestamp: string;
  content: string;
  likes: number;
  comments: number;
  shares: number;
  viewCount: number;
  isAuthor: boolean;
  title?: string; // Optional title field
  isPinned?: boolean; // Pin status field
}

// Generate mock post data
export const generateMockPosts = (count: number): Post[] => {
  const posts: Post[] = [];

  for (let i = 1; i <= count; i++) {
    posts.push({
      id: `post-${i}`,
      username: `user${i}`,
      avatarUrl: `https://i.pravatar.cc/150?img=${i}`,
      timestamp: `${Math.floor(Math.random() * 24)} hours ago`,
      title: `Sample Post Title ${i}`, // Add title
      content:
        i % 3 === 0
          ? 'This is a long post content. This post can be displayed in multiple lines. You can create and share posts on the channel page. This is mock data, and in actual implementation, it will display data fetched from the API.\n\nAdditional paragraphs can also be included. This makes the post content look richer. In actual implementation, the content entered by users will be displayed as is.'
          : 'This is a short post content. You can create and share posts on the channel page.',
      likes: Math.floor(Math.random() * 100),
      comments: Math.floor(Math.random() * 20),
      shares: Math.floor(Math.random() * 10),
      viewCount: Math.floor(Math.random() * 1000),
      isAuthor: i === 2, // Set the second post as the author's own post
      isPinned: i === 1, // Pin the first post
    });
  }

  return posts;
};
