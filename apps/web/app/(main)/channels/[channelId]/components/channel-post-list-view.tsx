import SvgIcon from '@/components/icons/svg-icon';
import CommonAvatar from '@/components/ui/avatar-image';
import { DarkButton } from '@/components/ui/base.button';
import {
  BaseDropdownMenu,
  BaseDropdownMenuContent,
  BaseDropdownMenuItem,
  BaseDropdownMenuTrigger,
} from '@/components/ui/base.dropdown-menu';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectItem,
  BaseSelectTrigger,
  BaseSelectValue,
} from '@/components/ui/base.select';
import { Popup } from '@/components/ui/popup';
import { toast } from '@/components/ui/base.toast';
import DeleteConfirmationPopup from '@/components/ui/popup/delete-confirmation-popup';
import { useDeletePost } from '@/hooks/query/board';
import { cn, pxToRem } from '@repo/ui/lib/utils';
import Link from 'next/link';
import { useState } from 'react';
import { PostType } from './post-adapter';
import { FilterOption, filterOptions } from './post-types';
import { INNER_LINKS } from '@/lib/constants';

interface PostListViewProps {
  posts: PostType[];
  filter: FilterOption;
  onFilterChange: (value: FilterOption) => void;
  channelId: string;
  onTogglePin: (postId: string) => void;
  onToggleHeart: (postId: string) => void;
  isOwnChannel: boolean;
}

export default function ChannelPostListView({
  posts,
  filter,
  onFilterChange,
  channelId,
  onTogglePin,
  onToggleHeart,
  isOwnChannel,
}: PostListViewProps) {
  const [deletePostId, setDeletePostId] = useState<string | null>(null);
  const deletePostMutation = useDeletePost();

  const handleDeleteClick = (postId: string) => {
    setDeletePostId(postId);
  };

  const handleDeleteConfirm = async () => {
    if (!deletePostId) return;

    try {
      await deletePostMutation.mutateAsync(deletePostId);
      toast.success('Deleted successfully.');
      setDeletePostId(null);
    } catch (error) {
      console.error('Failed to delete post:', error);
      toast.error('Failed to delete post. Please try again.');
    }
  };

  const handleDeleteCancel = () => {
    setDeletePostId(null);
  };
  return (
    <div className="flex flex-col">
      <div className="mb-space-20 flex items-center justify-between">
        {/* Filter */}
        <BaseSelect value={filter} onValueChange={value => onFilterChange(value as FilterOption)}>
          <BaseSelectTrigger className="w-[150px]">
            <BaseSelectValue placeholder="Sort" />
          </BaseSelectTrigger>
          <BaseSelectContent>
            {filterOptions.map(option => (
              <BaseSelectItem key={option.value} value={option.value}>
                {option.label}
              </BaseSelectItem>
            ))}
          </BaseSelectContent>
        </BaseSelect>

        {/* Create post button */}
        <DarkButton asChild width={pxToRem(135)} fontSize="xs" size="sm">
          <Link href={INNER_LINKS.MAIN.CHANNELS.POSTS.NEW(channelId)}>
            <SvgIcon data-label="icon" name="CrownIcon" className="text-white" />
            <span>Create Post</span>
          </Link>
        </DarkButton>
      </div>

      {/* Post list */}
      <div className="flex flex-col">
        {posts.length === 0 ? (
          <div className="flex flex-col items-center justify-center py-12 text-center">
            <h3 className="text-gray-4 mb-2 text-lg font-medium">No posts available</h3>
          </div>
        ) : (
          posts.map(post => (
            <div
              key={post.id}
              className={cn(
                'border-b-line p-space-20 border-b last:border-b-0',
                post.isPinned && 'bg-gray-2'
              )}
            >
              <div className="gap-space-15 mb-space-15 flex items-center">
                <div className="relative">
                  <CommonAvatar imageUrl={post.avatarUrl} size="md" alt={post.username} />
                  {post.isPinned && (
                    <div className="absolute -top-2 -left-2">
                      <SvgIcon name="RedPinRotatedIcon" />
                    </div>
                  )}
                </div>
                <div className="flex flex-1 flex-col">
                  <Link
                    href={INNER_LINKS.MAIN.ADDRESS_POSITIONS(post.username)}
                    className="text-gray-3 text-xs font-semibold hover:underline"
                  >
                    {post.username}
                  </Link>
                  <Link
                    href={INNER_LINKS.MAIN.ADDRESS_POSITIONS(post.username)}
                    className="text-mid-dark text-size-sm13 font-semibold hover:underline"
                  >
                    {post.title}
                  </Link>
                </div>
                {isOwnChannel ? (
                  <div className="flex gap-2">
                    <BaseDropdownMenu>
                      <BaseDropdownMenuTrigger asChild>
                        <button className="text-gray-3 hover:text-mid-dark">
                          <SvgIcon name="ThreeDotsIcon" className="h-5 w-5" />
                        </button>
                      </BaseDropdownMenuTrigger>
                      <BaseDropdownMenuContent>
                        <BaseDropdownMenuItem
                          className="gap-space-6 cursor-pointer py-2 text-sm"
                          onClick={e => e.stopPropagation()}
                        >
                          <SvgIcon name="ShareBonusIcon" className="h-4 w-4" />
                          <span>Share</span>
                        </BaseDropdownMenuItem>
                        <BaseDropdownMenuItem
                          className="gap-space-6 cursor-pointer py-2 text-sm"
                          onClick={e => e.stopPropagation()}
                          asChild
                        >
                          <Link href={INNER_LINKS.MAIN.CHANNELS.POSTS.EDIT(channelId, post.id)}>
                            <SvgIcon name="EditIcon" className="h-4 w-4" />
                            <span>Edit</span>
                          </Link>
                        </BaseDropdownMenuItem>
                        <BaseDropdownMenuItem
                          className="gap-space-6 cursor-pointer py-2 text-sm"
                          onClick={e => e.stopPropagation()}
                        >
                          <SvgIcon name="ReportIcon" className="h-4 w-4" />
                          <span>Report</span>
                        </BaseDropdownMenuItem>
                        <BaseDropdownMenuItem
                          className="gap-space-6 cursor-pointer py-2 text-sm"
                          onClick={e => {
                            e.stopPropagation();
                            handleDeleteClick(post.id);
                          }}
                        >
                          <SvgIcon name="DeleteIcon" className="h-4 w-4" />
                          <span>Delete</span>
                        </BaseDropdownMenuItem>
                      </BaseDropdownMenuContent>
                    </BaseDropdownMenu>
                  </div>
                ) : (
                  <div className="flex gap-2">
                    <BaseDropdownMenu>
                      <BaseDropdownMenuTrigger asChild>
                        <button className="text-gray-3 hover:text-mid-dark">
                          <SvgIcon name="ThreeDotsIcon" className="h-5 w-5" />
                        </button>
                      </BaseDropdownMenuTrigger>
                      <BaseDropdownMenuContent>
                        <BaseDropdownMenuItem className="gap-space-6 cursor-pointer py-2 text-sm">
                          <SvgIcon name="ShareBonusIcon" className="h-4 w-4" />
                          <span>Share</span>
                        </BaseDropdownMenuItem>
                        <BaseDropdownMenuItem className="gap-space-6 cursor-pointer py-2 text-sm">
                          <SvgIcon name="ReportIcon" className="h-4 w-4" />
                          <span>Report</span>
                        </BaseDropdownMenuItem>
                      </BaseDropdownMenuContent>
                    </BaseDropdownMenu>
                  </div>
                )}
              </div>

              {/* Post content */}
              <Link href={INNER_LINKS.MAIN.CHANNELS.POSTS.DETAIL(channelId, post.id)}>
                <div className="mb-space-15">
                  <p className="text-mid-dark line-clamp-3">{post.content}</p>
                </div>
              </Link>

              <div className="text-size-sm text-gray-3 flex items-center justify-between">
                <div className="gap-space-15 flex">
                  <button
                    className="gap-space-6 hover:text-mid-dark flex items-center"
                    onClick={e => {
                      e.stopPropagation();
                      onToggleHeart(post.id);
                    }}
                  >
                    <SvgIcon name="HeartIcon" />
                    <span>{post.likes}</span>
                  </button>

                  {isOwnChannel && (
                    <button
                      className={`gap-space-6 hover:text-mid-dark flex items-center ${
                        post.isPinned ? 'text-blue-500' : ''
                      }`}
                      onClick={e => {
                        onTogglePin(post.id);
                      }}
                      title={post.isPinned ? 'Unpin post' : 'Pin post'}
                    >
                      <SvgIcon name="RedPinIcon" />
                    </button>
                  )}

                  <button
                    className="gap-space-6 hover:text-mid-dark flex items-center"
                    onClick={e => e.stopPropagation()} // Prevent post selection event when clicking button
                  >
                    <SvgIcon name="ViewsIcon" />
                    <span>{post.viewCount.toLocaleString()}</span>
                  </button>
                  <div className="flex items-center">
                    <SvgIcon name="ClockOutlineIcon" className="mr-1" />
                    <div className="text-size-xs text-gray-3">{post.timestamp}</div>
                  </div>
                </div>
                <div className="border-line p-space-10 flex items-center border bg-white">
                  <SvgIcon name="CommentsIcon" className="mr-1" />
                  <span>{post.comments}</span>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Delete Confirmation Popup */}
      <Popup isOpen={deletePostId !== null} onClose={handleDeleteCancel} showCloseButton={true}>
        <DeleteConfirmationPopup onConfirm={handleDeleteConfirm} onCancel={handleDeleteCancel} />
      </Popup>
    </div>
  );
}
