import type { Meta, StoryObj } from '@storybook/react';
import ChannelBanner from './channel-banner';

const meta = {
  title: 'Channel/ChannelBanner',
  component: ChannelBanner,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
} satisfies Meta<typeof ChannelBanner>;

export default meta;
type Story = StoryObj<typeof ChannelBanner>;

export const WithImage: Story = {
  args: {
    imageUrl: 'https://images.unsplash.com/photo-1639762681057-408e52192e55?q=80&w=2832&auto=format&fit=crop',
  },
};

export const WithoutImage: Story = {
  args: {
    imageUrl: undefined,
  },
};
