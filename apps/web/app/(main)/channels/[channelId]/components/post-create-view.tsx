import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { BaseButton } from '@/components/ui/base.button';
import { BaseInput } from '@/components/ui/base.input';
import { BaseTextarea } from '@/components/ui/base.textarea';
import SvgIcon from '@/components/icons/svg-icon';
import { pxToRem } from '@repo/ui/lib/utils';
import { useCreateChannelPost } from '@/hooks/query/board';
import { CreateChannelPostRequestSchema } from '@/lib/api/board/board.schema.server';
import { toast } from '@/components/ui/base.toast';

// Form schema based on CreateChannelPostRequestSchema
const postFormSchema = CreateChannelPostRequestSchema;

type PostFormValues = z.infer<typeof postFormSchema>;

const PLACEHOLDER_TEXT = {
  title: 'Please enter the title. (max. 100 characters)',
  content: 'Please write the content to announce.. (max. 2,000 characters)',
};

interface PostCreateViewProps {
  onBack: () => void;
  channelId: string;
}

export default function PostCreateView({ onBack, channelId }: PostCreateViewProps) {
  const createChannelPostMutation = useCreateChannelPost();

  const form = useForm<PostFormValues>({
    resolver: zodResolver(postFormSchema),
    defaultValues: {
      title: '',
      content: '',
    },
  });

  const onSubmit = async (values: PostFormValues) => {
    try {
      await createChannelPostMutation.mutateAsync({
        title: values.title,
        content: values.content,
        channelId: channelId,
      });
      toast.success('Channel post created successfully!');
      form.reset();
      onBack();
    } catch (error) {
      console.error('Failed to create channel post:', error);
      toast.error('Failed to create channel post');
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col">
        {/* Error Display */}
        {createChannelPostMutation.error && (
          <div className="mb-space-20 rounded-md border border-red-200 bg-red-50 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Channel post creation failed</h3>
                <div className="mt-2 text-sm text-red-700">
                  {createChannelPostMutation.error.message}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Back button */}
        <div className="mb-space-20">
          <BaseButton
            variant="ghost"
            size="sm"
            className="gap-space-6 text-gray-3 hover:text-mid-dark flex items-center"
            onClick={onBack}
            type="button"
          >
            <SvgIcon name="ChevronNextIcon" className="h-4 w-4 rotate-180" />
            <span>Back to list</span>
          </BaseButton>
        </div>

        {/* Post creation form */}
        <div className="gap-space-20 flex flex-col">
          {/* Title input */}
          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <FormLabel className="text-mid-dark block text-sm font-semibold">Title</FormLabel>
                <FormControl>
                  <BaseInput placeholder={PLACEHOLDER_TEXT.title} className="w-full" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Content input */}
          <FormField
            control={form.control}
            name="content"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <FormLabel className="text-mid-dark block text-sm font-semibold">Message</FormLabel>
                <FormControl>
                  <BaseTextarea
                    placeholder={PLACEHOLDER_TEXT.content}
                    className="min-h-[150px] w-full"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Button area */}
          <div className="gap-space-5 flex items-center justify-start">
            <BaseButton
              type="submit"
              width={pxToRem(135)}
              fontSize="xs"
              variant="dark"
              size="sm"
              disabled={createChannelPostMutation.isPending}
            >
              <SvgIcon data-label="icon" name="CrownIcon" className="text-white" />
              {createChannelPostMutation.isPending ? 'Creating...' : 'Channel Post'}
            </BaseButton>
          </div>
        </div>
      </form>
    </Form>
  );
}
