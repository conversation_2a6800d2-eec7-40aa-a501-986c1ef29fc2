import SvgIcon from '@/components/icons/svg-icon';
import { Skeleton } from '@repo/ui/components/skeleton';
import * as Icons from '@/components/icons/assets';

type IconName = keyof typeof Icons;

interface StatItemProps {
  icon: IconName;
  label: string;
  value?: string | number;
  isLoading?: boolean;
  skeletonWidth?: string;
}

function StatItem({ icon, label, value, isLoading, skeletonWidth = 'w-12' }: StatItemProps) {
  return (
    <div className="gap-space-5 flex items-center">
      <SvgIcon name={icon} />
      <span className="text-gray-3">{label}</span>
      {isLoading || value === undefined ? (
        <Skeleton className={`h-4 ${skeletonWidth}`} />
      ) : (
        <span className="text-icon-dark">{value}</span>
      )}
    </div>
  );
}

interface ChannelStatsProps {
  subscribers?: number | string;
  totalVolume?: number | string;
  totalMarkets?: number | string;
  isLoading?: boolean;
}

export default function ChannelStats({
  subscribers,
  totalVolume,
  totalMarkets,
  isLoading,
}: ChannelStatsProps) {
  return (
    <div className="gap-space-20 text-size-xs flex">
      <StatItem
        icon="SubscriptionInversedIcon"
        label="Subscribers"
        value={typeof subscribers === 'number' ? subscribers.toLocaleString() : subscribers}
        isLoading={isLoading}
      />
      <StatItem
        icon="DollarIcon"
        label="Total Volume:"
        value={totalVolume ? `$${Number(totalVolume).toLocaleString()}` : undefined}
        isLoading={isLoading}
        skeletonWidth="w-16"
      />
      <StatItem
        icon="MarketInversedIcon"
        label="Total Markets:"
        value={totalMarkets}
        isLoading={isLoading}
      />
    </div>
  );
}
