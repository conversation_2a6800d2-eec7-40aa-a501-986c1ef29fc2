import SvgIcon from '@/components/icons/svg-icon';
import { BaseButton } from '@/components/ui/base.button';
import { BaseInput } from '@/components/ui/base.input';
import { BaseTextarea } from '@/components/ui/base.textarea';
import { toast } from '@/components/ui/base.toast';
import { useChannelPosts, useEditPost } from '@/hooks/query/board';
import { EditChannelPostRequestSchema } from '@/lib/api/board/board.schema.server';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { pxToRem } from '@repo/ui/lib/utils';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

// Form schema based on EditChannelPostRequestSchema
const postFormSchema = EditChannelPostRequestSchema;

type PostFormValues = z.infer<typeof postFormSchema>;

const PLACEHOLDER_TEXT = {
  title: 'Please enter the title. (max. 100 characters)',
  content: 'Please write the content to announce.. (max. 2,000 characters)',
};

interface PostEditViewProps {
  onBack: () => void;
  channelId: string;
  postId: string;
  initialData: {
    title: string;
    content: string;
  };
}

export default function PostEditView({
  onBack,
  channelId,
  postId,
  initialData,
}: PostEditViewProps) {
  const editPostMutation = useEditPost();
  const { refetch: refetchChannelPosts } = useChannelPosts(channelId, { page: 0, limit: 50 });
  const form = useForm<PostFormValues>({
    resolver: zodResolver(postFormSchema),
    defaultValues: {
      title: initialData.title,
      content: initialData.content,
    },
  });

  const onSubmit = async (values: PostFormValues) => {
    try {
      await editPostMutation.mutateAsync({
        postId: postId,
        data: {
          title: values.title,
          content: values.content,
        },
      });
      toast.success('Post updated successfully!');
      refetchChannelPosts();
      onBack();
    } catch (error) {
      console.error('Failed to update post:', error);
      toast.error('Failed to update post');
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="flex flex-col">
        {/* Error Display */}
        {editPostMutation.error && (
          <div className="mb-space-20 rounded-md border border-red-200 bg-red-50 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Post update failed</h3>
                <div className="mt-2 text-sm text-red-700">{editPostMutation.error.message}</div>
              </div>
            </div>
          </div>
        )}

        {/* Back button */}
        <div className="mb-space-20">
          <BaseButton
            variant="ghost"
            size="sm"
            className="gap-space-6 text-gray-3 hover:text-mid-dark flex items-center"
            onClick={onBack}
            type="button"
          >
            <SvgIcon name="ChevronNextIcon" className="h-4 w-4 rotate-180" />
            <span>Back to list</span>
          </BaseButton>
        </div>

        {/* Post edit form */}
        <div className="gap-space-20 flex flex-col">
          {/* Title input */}
          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <FormLabel className="text-mid-dark block text-sm font-semibold">Title</FormLabel>
                <FormControl>
                  <BaseInput placeholder={PLACEHOLDER_TEXT.title} className="w-full" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Content input */}
          <FormField
            control={form.control}
            name="content"
            render={({ field }) => (
              <FormItem className="space-y-2">
                <FormLabel className="text-mid-dark block text-sm font-semibold">Message</FormLabel>
                <FormControl>
                  <BaseTextarea
                    placeholder={PLACEHOLDER_TEXT.content}
                    className="min-h-[150px] w-full"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          {/* Button area */}
          <div className="gap-space-5 flex items-center justify-start">
            <BaseButton
              type="submit"
              width={pxToRem(135)}
              fontSize="xs"
              variant="dark"
              size="sm"
              disabled={editPostMutation.isPending}
            >
              <SvgIcon data-label="icon" name="EditIcon" className="text-white" />
              {editPostMutation.isPending ? 'Updating...' : 'Update Post'}
            </BaseButton>
          </div>
        </div>
      </form>
    </Form>
  );
}
