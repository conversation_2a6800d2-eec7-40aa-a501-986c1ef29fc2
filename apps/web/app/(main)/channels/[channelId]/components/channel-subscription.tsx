import { Switch } from '@repo/ui/components/switch';
import { usePopupStore } from '@/components/ui/popup/popup.state';
import ChannelUnsubscribePopup from '@/components/ui/popup/channel-unsubscribe-popup';

interface ChannelSubscriptionProps {
  channelId: string;
  channelName: string;
  isSubscribed: boolean;
  onToggle: () => void;
  isLoading?: boolean;
}

export default function ChannelSubscription({
  channelId,
  channelName,
  isSubscribed,
  onToggle,
  isLoading = false,
}: ChannelSubscriptionProps) {
  const { openPopup, closePopup } = usePopupStore();

  const handleToggle = () => {
    if (isSubscribed) {
      // 구독 해제 시 팝업 표시
      openPopup(
        <ChannelUnsubscribePopup
          channelName={channelName}
          onConfirm={() => {
            onToggle();
            closePopup();
          }}
          onCancel={() => {
            closePopup();
          }}
        />
      );
    } else {
      // 구독 시 바로 실행
      onToggle();
    }
  };

  return (
    <div className="gap-space-5 flex items-center">
      <Switch
        id={`subscribe-${channelId}`}
        checked={isSubscribed}
        onCheckedChange={handleToggle}
        disabled={isLoading}
        className="data-[state=checked]:bg-sky data-[state=unchecked]:bg-gray-3"
      />
      <label htmlFor={`subscribe-${channelId}`} className="text-size-xs">
        {isSubscribed ? 'Subscribed' : 'Subscribe'}
      </label>
    </div>
  );
}
