import { cn } from '@repo/ui/lib/utils';

interface ChannelBannerProps {
  imageUrl?: string;
}

export default function ChannelBanner({ imageUrl }: ChannelBannerProps) {
  return (
    <div className={cn('bg-gray-2 relative h-(--banner-image-height) w-full overflow-hidden')}>
      {imageUrl ? (
        <img
          src={imageUrl}
          alt="Channel banner"
          className="h-full w-full object-cover object-center"
        />
      ) : (
        <div className="text-gray-3 flex h-full w-full items-center justify-center">
          No banner image
        </div>
      )}
    </div>
  );
}
