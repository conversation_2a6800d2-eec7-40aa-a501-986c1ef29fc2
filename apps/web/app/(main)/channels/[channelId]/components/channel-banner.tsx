import { cn } from '@repo/ui/lib/utils';
import { Skeleton } from '@repo/ui/components/skeleton';

interface ChannelBannerProps {
  imageUrl?: string;
}

export default function ChannelBanner({ imageUrl }: ChannelBannerProps) {
  return (
    <div className={cn('bg-gray-2 relative h-(--banner-image-height) w-full overflow-hidden')}>
      {imageUrl ? (
        <img
          src={imageUrl}
          alt="Channel banner"
          className="h-full w-full object-cover object-center"
        />
      ) : (
        <Skeleton className="h-full w-full" />
      )}
    </div>
  );
}
