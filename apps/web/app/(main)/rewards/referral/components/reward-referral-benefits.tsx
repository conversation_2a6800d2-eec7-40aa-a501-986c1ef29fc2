import { useReferralBenefits } from '@/hooks/query/referral';

export function RewardReferralBenefits() {
  const { data: benefits, isLoading } = useReferralBenefits();

  if (isLoading || !benefits) {
    return <div>Loading benefits...</div>; // Or a skeleton loader
  }

  return (
    <div className="divide-line text-size-sm text-mid-dark px-space-30 divide-y font-semibold">
      {benefits.map((benefit, index: number) => (
        <div key={index} className="py-space-10 px-space-20 grid grid-cols-4 items-center">
          <div>LV.{benefit.level}</div>
          <div>{benefit.commissionRewardRatio}</div>
          <div>{benefit.feeRebateRatio}</div>
          <div>&gt; {benefit.accumulatedProfit}</div>
        </div>
      ))}
    </div>
  );
}
