'use client';

import { BaseButton } from '@/components/ui/base.button';
import { RewardReferralBenefits } from './components/reward-referral-benefits';
import { RewardReferralDashboard } from './components/reward-referral-dashboard';
import RewardReferralLeaderboard from './components/reward-referral-learboard';

export default function ReferralPage() {
  return (
    <div className="page">
      <div className="flex flex-col">
        <section className="gap-space-60 border-b-line flex border-b pb-[70px]">
          {/* TODO: image section */}
          <div className="w-[500px]">
            <img
              alt="Referral Guidance"
              className="w-full"
              src="/assets/images/referral_object1.png"
            />
          </div>
          <div className="gap-space-30 flex flex-1 flex-col">
            <h2 className="text-size-xl text-dark font-bold">
              Participate & Receive 1% Referral Commission on you and your friends Winnings
            </h2>
            <div className="text-size-sm text-gray-3">
              Find out more about how PredictGo Referral works!{' '}
              <a className="text-dark underline" href="">
                View Rules
              </a>
            </div>
            <h3 className="dashboard-h2">Referral Benefits</h3>
            <div className="mt-space-20 mb-space-30">
              <div className="bg-gray-2">
                <div className="bg-gray-2 text-size-xs text-gray-3 py-space-15 px-space-20 border-b-line grid grid-cols-4 border-b font-semibold">
                  <div>Referral Level</div>
                  <div>Commission Reward</div>
                  <div>Fee Rebate</div>
                  <div>Accumulated Profit</div>
                </div>
                <RewardReferralBenefits />
              </div>
              <div className="gap-space-30 mt-space-20 flex items-center justify-end">
                <p className="text-size-sm">
                  Get up to 20% rebate when you invite friends to PredictGo!
                </p>
                <BaseButton className="px-space-20" size="sm" variant="info">
                  Check Referral Code
                </BaseButton>
              </div>
            </div>
          </div>
        </section>
        <RewardReferralDashboard />
        <RewardReferralLeaderboard />
      </div>
    </div>
  );
}
