import DashboardBox from '@/components/common/dashboard-box';
import { BaseButton } from '@/components/ui/base.button';
import { pxToRem } from '@repo/ui/lib/utils';

export default function ShareBonusPage() {
  return (
    <div className="page">
      <section className="gap-space-60 border-b-line flex border-b pb-[70px]">
        <div className="w-[500px]">
          <img
            alt="Referral Guidance"
            className="w-full"
            src="/assets/images/share_bonus_object1.png"
          />
        </div>
        <div className="gap-space-30 flex flex-1 flex-col">
          <h2 className="text-size-xl text-dark font-bold">
            Share Bonus RewShare System : Automated Expansion Protocolard
          </h2>
          <div className="text-size-sm text-gray-3">
            <p>
              Our "Share" feature is not merely a marketing tool. It serves as an automated user
              acquisition (UA) marketing tool and an Expansion Protocol that will propel PredictGo
              along an exponential growth trajectory. As more users engage with the platform, the
              pace of this growth will accelerate increasingly.{' '}
            </p>
            <p>
              Building upon this foundation, we aim to develop a dynamic platform capable of
              limitless expansion.
            </p>
          </div>
          <div>
            <p>User : Share</p>
            <img src="/assets/images/share_bonus_object2.svg" />
          </div>
        </div>
      </section>
      <section className="gap-space-30 pt-space-50 flex flex-col">
        <h2 className="dashboard-h2">Dashboard</h2>
        <div className="gap-space-60 flex">
          <DashboardBox className="bg-gray-2 flex-1">
            <h3 className="dashboard-h3">Total </h3>
            <strong className="text-size-xl text-mid-dark font-bold">$120.55K</strong>
          </DashboardBox>
          <DashboardBox className="flex-1">
            <h3 className="dashboard-h3">Total </h3>
            <strong className="text-size-xl text-mid-dark font-bold">$120.55K</strong>
          </DashboardBox>
        </div>
        <div className="gap-space-30 flex items-center justify-end">
          <p className="text-size-sm text-gray-3">
            Go to the Sharing page in the <span className="text-mid-dark">User Dashboard</span>{' '}
          </p>
          <BaseButton
            style={{
              width: pxToRem(180),
            }}
            size="sm2"
            variant="info"
          >
            Earn Extra Rewards
          </BaseButton>
        </div>
      </section>
    </div>
  );
}
