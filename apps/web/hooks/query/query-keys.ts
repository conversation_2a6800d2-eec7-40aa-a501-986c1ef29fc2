import type {
  GetMarketsRequestQuery,
  GetMarketsWithStatusRequestQuery,
  MarketStatusEnum,
} from '@/lib/api/market/market.schema.server';
import type { Address } from 'viem';
import type {
  LeaderboardPeriod as LeaderboardPeriodType,
  LeaderboardType,
  LeaderboardType as LeaderboardTypeType,
} from '@/lib/api/leaderboard/leaderboard.schema.server';

export const marketKeys = {
  markets: ['markets'] as const,
  marketsWithStatus: (status: MarketStatusEnum[] | undefined, options?: GetMarketsRequestQuery) =>
    ['markets', 'with-status', ...(status || []), options] as const,
  market: (marketId: string) => ['market', marketId] as const,
  activities: (marketId: string, outcome: string) => ['activities', marketId, outcome] as const,
  topPredictors: (marketId: string, outcome: string) =>
    ['topPredictors', marketId, outcome] as const,
  search: (query: string) => ['search', query] as const,
};

export const channelKeys = {
  channels: ['channels'] as const,
  channel: (channelId: string) => ['channel', channelId] as const,
  channelWithUser: (channelId: string) => ['channelWithUser', channelId] as const,
  channelLeaderboard: (channelId: string, type: LeaderboardType) =>
    ['channelLeaderboard', channelId, type] as const,
  channelMarkets: (channelId: string) => ['channelMarkets', channelId] as const,
  collateral: ['collateral'] as const,
  collateralHistory: ['collateralHistory'] as const,
  popularChannels: ['popularChannels'] as const,
  activePredictions: ['activePredictions'] as const,
  predictionHistory: ['predictionHistory'] as const,
  rewards: ['rewards'] as const,
  rewardsHistory: ['rewardsHistory'] as const,
  channelSearch: ['channelSearch'] as const,
  channelOverview: (channelId: string) => ['channelOverview', channelId] as const,
  userChannelDashboard: ['userChannelDashboard'] as const,
  myChannel: ['myChannel'] as const,
};

export const boardKeys = {
  board: ['board'] as const,
  comments: (parentId: string) => ['comments', parentId] as const,
  post: (postId: string) => ['post', postId] as const,
  channelPosts: (channelId: string) => ['channelPosts', channelId] as const,
};

export const categoryKeys = {
  categories: ['categories'] as const,
  category: (category: string) => ['category', category] as const,
  categoryTags: (category: string) => ['categoryTags', category] as const,
  categoryMarkets: (category: string, tag: string) => ['categoryMarkets', category, tag] as const,
  categoryWithMarkets: (category: string, tag: string) =>
    ['categoryWithMarkets', category, tag] as const,
  popularCategories: ['popularCategories'] as const,
};

export const userKeys = {
  all: ['users'] as const,
  user: (address: Address | undefined) => ['user', address] as const,
  userActivities: (address: string) => ['userActivities', address] as const,
  userPositions: (address: string) => ['userPositions', address] as const,
  userStats: (address: string) => ['userStats', address] as const,
  userSubscriptions: () => ['userSubscriptions'] as const,
  currentUser: () => ['currentUser'] as const,
};

export const predictKeys = {
  all: ['predict'] as const,
  market: (marketId: string) => ['market', marketId] as const,
  activities: (marketId: string, outcome: string) => ['activities', marketId, outcome] as const,
  collateral: () => ['collateral'] as const,
};

export const portfolioKeys = {
  all: ['portfolio'] as const,
  portfolio: () => [...portfolioKeys.all] as const,
  activities: (options?: any) => [...portfolioKeys.all, 'activities', options] as const,
  positions: (options?: any) => [...portfolioKeys.all, 'positions', options] as const,
  myPositions: (options?: any) => [...portfolioKeys.all, 'myPositions', options] as const,
  disputes: (options?: any) => [...portfolioKeys.all, 'disputes', options] as const,
  claimableDispute: () => [...portfolioKeys.all, 'claimableDispute'] as const,
  dashboard: () => [...portfolioKeys.all, 'dashboard'] as const,
  analysis: (options?: any) => [...portfolioKeys.all, 'analysis', options] as const,
};

export const bannerKeys = {
  all: ['banner'] as const,
  banner: () => [...bannerKeys.all] as const,
};

export const referralKeys = {
  all: ['referral'] as const,
  benefit: () => [...referralKeys.all, 'benefit'] as const,
  benefits: () => [...referralKeys.all, 'benefits'] as const,
  details: (type: string, options?: any) =>
    [...referralKeys.all, 'details', type, options] as const,
  leaderboard: () => [...referralKeys.all, 'leaderboard'] as const,
  dashboard: () => [...referralKeys.all, 'dashboard'] as const,
};

export const leaderboardKeys = {
  all: ['leaderboard'] as const,
  leaderboard: (type: LeaderboardTypeType, period: LeaderboardPeriodType) =>
    [...leaderboardKeys.all, type, period] as const,
  leaderboardByAddress: (
    type: LeaderboardTypeType,
    period: LeaderboardPeriodType,
    address: string
  ) => [...leaderboardKeys.all, type, period, address] as const,
};

export const activityKeys = {
  all: ['activity'] as const,
  predictActivities: (options?: { page?: number; limit?: number }) =>
    [...activityKeys.all, 'predict', options] as const,
};

export const sessionKeys = {
  all: ['session'] as const,
  session: () => [...sessionKeys.all, 'current'] as const,
};

export const safeSmartAccountKeys = {
  all: ['safeSmartAccount'] as const,
  account: (address: string | undefined) => [...safeSmartAccountKeys.all, address] as const,
};

export const shareKeys = {
  all: ['share'] as const,
  myShares: (options?: any) => [...shareKeys.all, 'myShares', options] as const,
  myDashboard: () => [...shareKeys.all, 'myDashboard'] as const,
  dashboard: () => [...shareKeys.all, 'dashboard'] as const,
};
