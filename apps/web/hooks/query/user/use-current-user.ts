import { useGlobalStore } from '@/store/global.store';
import { useAccount } from 'wagmi';
import { useUserWithAutoCreate } from './use-user-with-auto-create';

export const useCurrentUser = () => {
  const { safeSmartAccount, isSignedIn } = useGlobalStore();
  const { isConnected, isDisconnected } = useAccount();
  const { isLoading, ...rest } = useUserWithAutoCreate(safeSmartAccount?.address, isSignedIn);

  return {
    ...rest,
    isLoading: isLoading || !(isConnected || isDisconnected),
    isSignedIn,
  };
};
