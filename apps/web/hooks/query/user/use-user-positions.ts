import { userService } from '@/lib/api/user/user.service';
import { useQuery } from '@tanstack/react-query';
import { userKeys } from '../query-keys';
import { MARKET_STATUS_TEXT_MAP } from '@/lib/constants';

export type UserPostionItem = {
  marketTitle: string;
  marketStatus: string;
  marketImageUrl: string;
  marketVolume: string;
  outcome: string;
  outcomeOrder: number;
  estimatedOdds: string;
  estimatedWin: string;
};

export const useUserPositions = (address: string, options?: { page?: number; limit?: number }) => {
  return useQuery({
    enabled: !!address,
    queryKey: [...userKeys.userPositions(address), options],
    queryFn: () => userService.getUserPositions(address, options),
    select: data => {
      const positions = data.positions.map(val => {
        return {
          marketTitle: val.market.title,
          marketStatus: val.market.status ? MARKET_STATUS_TEXT_MAP[val.market.status] : '',
          marketImageUrl: val.market.imageUrl,
          marketVolume: val.value,
          outcome: val.outcome,
          outcomeOrder: val.outcomeOrder,
          estimatedOdds: val.estimatedOdds,
          estimatedWin: val.estimatedWin,
        };
      });
      return {
        positions,
        totalLength: data.totalLength,
      };
    },
  });
};
