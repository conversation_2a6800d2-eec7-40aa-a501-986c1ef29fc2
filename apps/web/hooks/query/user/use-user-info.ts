import { userService } from '@/lib/api/user/user.service';
import { useQuery } from '@tanstack/react-query';
import { Address } from 'viem';
import { userKeys } from '../query-keys';
import { ClientError } from '@/lib/error';

export const useUserInfo = (address: Address | undefined) => {
  return useQuery({
    queryKey: userKeys.user(address),
    enabled: !!address,
    queryFn: async () => {
      if (!address) {
        throw ClientError.fromQuery('Address is not found', {
          queryName: 'useUserInfo',
        });
      }
      const user = await userService.getUserInfo(address);
      return user;
    },
  });
};
