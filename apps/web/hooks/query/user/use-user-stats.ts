import { useQuery } from '@tanstack/react-query';
import { userKeys } from '../query-keys';
import { userService } from '@/lib/api/user/user.service';

export const useUserStats = (address: string) => {
  return useQuery({
    queryKey: userKeys.userStats(address),
    enabled: !!address,
    queryFn: async () => {
      return userService.getUserStats(address);
    },
    select: data => {
      return {
        ...data,
        stats: {
          positionsValue: data.stats.positionsValue,
          profitLoss: data.stats.profitLoss,
          volumeTraded: data.stats.volumeTraded,
          marketTraded: data.stats.marketTraded,
        },
      };
    },
  });
};
