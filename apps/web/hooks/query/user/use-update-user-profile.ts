import { userService } from '@/lib/api/user/user.service';
import { useMutation } from '@tanstack/react-query';

export const useUpdateUserProfile = (options?: {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}) => {
  return useMutation({
    mutationFn: (data: FormData) => userService.updateUserProfile(data),
    onSuccess: () => {
      options?.onSuccess?.();
    },
    onError: (error: any) => {
      options?.onError?.(error);
    },
  });
};
