import { useMutation, useQueryClient } from '@tanstack/react-query';
import { userKeys } from '../query-keys';
import { userService } from '@/lib/api/user/user.service';

export const useUpdateUserProfile = (options?: {
  onSuccess?: () => void;
  onError?: (error: Error) => void;
}) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: FormData) => userService.updateUserProfile(data),
    onSuccess: () => {
      // 현재 사용자 및 관련 데이터 무효화
      queryClient.invalidateQueries({
        queryKey: userKeys.currentUser(),
      });

      options?.onSuccess?.();
    },
    onError: (error: any) => {
      options?.onError?.(error);
    },
  });
};
