import { useQuery } from '@tanstack/react-query';
import { userKeys } from '../query-keys';
import { userService } from '@/lib/api/user/user.service';
import { DEFAULT_USER_AVATAR_URL } from '@/lib/constants';
import { toRelativeTime } from '@/lib/utils';

export type UserActivityItem = {
  marketTitle: string;
  marketImageUrl: string;
  marketId: string;
  outcome: string;
  type: string;
  value: string;
  timestamp: string;
  relativeTime: string;
};

export const useUserActivities = (address: string, options?: { page?: number; limit?: number }) => {
  return useQuery({
    enabled: !!address,
    queryKey: [...userKeys.userActivities(address), options],
    queryFn: () => userService.getUserActivities(address, options),
    select: data => {
      const activities = data.activities.map(val => {
        return {
          marketTitle: val.market?.title || 'Unknown Market',
          marketImageUrl: val.market?.imageUrl || DEFAULT_USER_AVATAR_URL,
          marketId: val.market?.id || '',
          outcome: val.outcome || '',
          type: val.type,
          value: val.value || '',
          timestamp: val.timestamp,
          relativeTime: toRelativeTime(val.timestamp) + ' ago',
        };
      });

      return {
        activities,
        totalLength: data.totalLength,
      };
    },
  });
};
