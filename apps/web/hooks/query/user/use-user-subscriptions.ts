import { useQuery } from '@tanstack/react-query';
import { userKeys } from '../query-keys';
import { userService } from '@/lib/api/user/user.service';

type GetUserSubscriptionsReturnType = Awaited<ReturnType<typeof userService.getUserSubscriptions>>;

export const mapUserSubscriptionsResponseToUserSubscriptionsProps = (
  response: GetUserSubscriptionsReturnType
) => {
  const channels = response.channels.map(channel => ({
    channelId: channel.id,
    channelName: channel.name,
    channelImageUrl: channel.imageUrl,
    channelLeaderImageUrl: channel.leader.imageUrl,
    channelLeaderNickname: channel.leader.nickname,
    channelLeaderAddress: channel.leader.address,
    channelSubscribers: channel.subscribers,
    isSubscribed: channel.isSubscribed,
  }));

  return {
    channels,
    totalLength: response.totalLength,
  };
};

export type UserSubscriptionsProps = ReturnType<
  typeof mapUserSubscriptionsResponseToUserSubscriptionsProps
>;

export type UserSubscriptionsChannelProps = UserSubscriptionsProps['channels'][number];

export const useUserSubscriptions = (options?: { page?: number; limit?: number }) => {
  return useQuery({
    queryKey: [...userKeys.userSubscriptions(), options],
    queryFn: () => userService.getUserSubscriptions(options),
    select: mapUserSubscriptionsResponseToUserSubscriptionsProps,
  });
};
