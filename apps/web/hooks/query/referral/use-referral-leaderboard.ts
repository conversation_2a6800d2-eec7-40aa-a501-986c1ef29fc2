import { useQuery } from '@tanstack/react-query';
import { referralKeys } from '../query-keys';
import { referralService } from '@/lib/api/referral/referral.service';

export type ReferralLeaderboardItem = {
  inviter: string;
  invitees: number;
  referralIncome: string;
  address: string;
};

export const useReferralLeaderboard = () => {
  return useQuery({
    queryKey: referralKeys.leaderboard(),
    queryFn: () => referralService.getReferralLeaderboard(),
    select: data => {
      const leaderboard = data.leaderboard.map(item => {
        return {
          inviter: item.nickname ? item.nickname : item.address,
          invitees: item.invitees,
          referralIncome: item.commissionReward,
          address: item.address,
        };
      });

      return leaderboard;
    },
  });
};
