import { useQuery } from '@tanstack/react-query';
import { categoryKeys } from '../query-keys';
import { categoryService } from '@/lib/api/category/category.service';

export const useCategoryTags = (category: string, options?: { page?: number; limit?: number }) => {
  return useQuery({
    queryKey: [...categoryKeys.categoryTags(category), options],
    queryFn: () => categoryService.getTagsInCategory(category, options),
    enabled: !!category,
  });
};
