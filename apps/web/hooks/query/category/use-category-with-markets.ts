import { useQuery } from '@tanstack/react-query';
import { categoryKeys } from '../query-keys';
import { categoryService } from '@/lib/api/category/category.service';
import { MarketOrder } from '@/lib/api/category/category.schema.server';

export const useCategoryWithMarkets = (
  category: string,
  tag: string = 'All',
  options?: {
    page?: number;
    limit?: number;
    order?: MarketOrder;
  }
) => {
  return useQuery({
    queryKey: [...categoryKeys.categoryWithMarkets(category, tag), options],
    queryFn: () => categoryService.getCategoryWithMarkets(category, tag, options),
    enabled: !!(category && tag),
  });
};
