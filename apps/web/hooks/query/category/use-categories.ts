import { useQuery } from '@tanstack/react-query';
import { categoryKeys } from '../query-keys';
import { categoryService } from '@/lib/api/category/category.service';

export type CategoryItem = {
  categoryName: string;
};

export const useCategories = (options?: { page?: number; limit?: number }) => {
  return useQuery({
    queryKey: [...categoryKeys.categories, options],
    queryFn: () => categoryService.getCategories(options),
    select: data => {
      const categories = data.categories.map(item => {
        return {
          categoryName: item.name,
        };
      });
      return categories;
    },
  });
};
