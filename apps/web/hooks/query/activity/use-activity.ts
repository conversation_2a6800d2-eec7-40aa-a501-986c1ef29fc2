import { useQuery } from '@tanstack/react-query';
import { activityService } from '@/lib/api/activity/activity.service';
import { activityKeys } from '../query-keys';
import { toRelativeTime } from '@/lib/utils';

export type PredictActivity = {
  marketId: string;
  marketTitle: string;
  marketImageUrl: string;
  userAddress: string;
  userNickname: string;
  userAvatarUrl: string;
  amount: string;
  outcome?: string;
  outcomeOrder?: number;
  timestamp: string;
  relativeTime: string;
};

export const usePredictActivities = (options?: { page?: number; limit?: number }) => {
  return useQuery({
    queryKey: activityKeys.predictActivities(options),
    queryFn: () => activityService.getPredictActivities(options),
    select: data => {
      const activities = data.activities.map(item => {
        return {
          marketId: item.market.id,
          marketTitle: item.market.title,
          marketImageUrl: item.market.imageUrl,
          userAddress: item.user.address,
          userNickname: item.user.nickname,
          userAvatarUrl: item.user.imageUrl,
          amount: item.formattedValue,
          outcome: item.outcome,
          outcomeOrder: item.outcomeOrder,
          timestamp: item.timestamp,
          relativeTime: toRelativeTime(item.timestamp),
        };
      });

      return {
        totalLength: data.totalLength,
        activities,
      };
    },
  });
};
