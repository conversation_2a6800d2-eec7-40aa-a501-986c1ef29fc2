import { useQuery } from '@tanstack/react-query';
import { shareService } from '@/lib/api/share/share.service';
import { shareKeys } from '../query-keys';
import { mapShareDashboardResponseToProps } from './share.mapper';

export const useShareDashboard = () => {
  return useQuery({
    queryKey: shareKeys.dashboard(),
    queryFn: async () => {
      const response = await shareService.getShareDashboard();
      return mapShareDashboardResponseToProps(response);
    },
  });
};
