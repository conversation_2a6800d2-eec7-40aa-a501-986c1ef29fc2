import { useMutation, useQueryClient } from '@tanstack/react-query';
import { shareService } from '@/lib/api/share/share.service';
import { shareKeys } from '../query-keys';
import type { ShareRequest } from '@/lib/api/share/share.schema.server';
import { toast } from '@repo/ui/components/sonner';

export const useShareMarket = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (shareData: ShareRequest) => shareService.shareMarket(shareData),
    onSuccess: () => {
      // Invalidate share-related queries to refresh data
      queryClient.invalidateQueries({
        queryKey: shareKeys.all,
      });
      toast.success('Market shared successfully!');
    },
    onError: (error) => {
      toast.error('Failed to share market');
      console.error('Share market error:', error);
    },
  });
};
