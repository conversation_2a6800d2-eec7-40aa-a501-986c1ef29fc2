import { shareService } from '@/lib/api/share/share.service';
import { toRelativeTime } from '@/lib/utils';

type GetMySharesReturnType = Awaited<ReturnType<typeof shareService.getMyShares>>;
type GetMyShareDashboardReturnType = Awaited<ReturnType<typeof shareService.getMyShareDashboard>>;
type GetShareDashboardReturnType = Awaited<ReturnType<typeof shareService.getShareDashboard>>;

export const mapMySharesResponseToShareRewardsProps = (response: GetMySharesReturnType) => {
  const shares = response.shares.map(share => {
    return {
      id: share.market.id,
      prediction: {
        id: share.market.id,
        title: share.market.title,
        avatar: share.market.imageUrl,
      },
      winnings: share.rawWinnings.toNumber(),
      shareBonus: share.rawShareableAmount.toNumber(),
      winningsFormatted: share.winningsFormatted,
      shareBonusFormatted: share.shareableAmountFormatted,
      status: share.isShared ? 'Completed' : 'Share',
      sharedAt: share.sharedAt,
      localSharedAt: share.localSharedAt,
      relativeSharedTime: share.sharedAt ? toRelativeTime(share.sharedAt) : null,
    };
  });

  return {
    shares,
    totalLength: response.totalLength,
  };
};

export type ShareRewardsProps = ReturnType<typeof mapMySharesResponseToShareRewardsProps>;
export type ShareRewardItem = ShareRewardsProps['shares'][number];

export const mapMyShareDashboardResponseToProps = (response: GetMyShareDashboardReturnType) => {
  return {
    claimableAmount: response.rawClaimableAmount.toNumber(),
    claimableAmountFormatted: response.claimableAmountFormatted,
    unLockableShareRewards: response.rawUnLockableShareRewards.toNumber(),
    unLockableShareRewardsFormatted: response.unLockableShareRewardsFormatted,
    totalClaimed: response.rawTotalClaimed.toNumber(),
    totalClaimedFormatted: response.totalClaimedFormatted,
  };
};

export type MyShareDashboardProps = ReturnType<typeof mapMyShareDashboardResponseToProps>;

export const mapShareDashboardResponseToProps = (response: GetShareDashboardReturnType) => {
  return {
    totalShareReward: response.rawTotalShareReward.toNumber(),
    totalShareRewardFormatted: response.totalShareRewardFormatted,
    totalShared: response.totalShared,
  };
};

export type ShareDashboardProps = ReturnType<typeof mapShareDashboardResponseToProps>;
