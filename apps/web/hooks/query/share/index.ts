// Query hooks
export { useMyShares } from './use-my-shares';
export { useMyShareDashboard } from './use-my-share-dashboard';
export { useShareDashboard } from './use-share-dashboard';

// Mutation hooks
export { useShareMarket } from './use-share-market';

// Mappers and types
export {
  mapMySharesResponseToShareRewardsProps,
  mapMyShareDashboardResponseToProps,
  mapShareDashboardResponseToProps,
  type ShareRewardsProps,
  type ShareRewardItem,
  type MyShareDashboardProps,
  type ShareDashboardProps,
} from './share.mapper';

// Re-export types for convenience
export type {
  ShareRequest,
  GetSharesRequest,
  ShareDashboardResponse,
  MyShareDashboardResponse,
  GetSharesResponse,
  ShareItem,
  ShareMarket,
} from '@/lib/api/share/share.schema.server';
