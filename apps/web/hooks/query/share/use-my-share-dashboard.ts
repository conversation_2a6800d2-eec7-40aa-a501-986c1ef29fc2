import { useQuery } from '@tanstack/react-query';
import { shareService } from '@/lib/api/share/share.service';
import { shareKeys } from '../query-keys';
import { mapMyShareDashboardResponseToProps } from './share.mapper';

export const useMyShareDashboard = () => {
  return useQuery({
    queryKey: shareKeys.myDashboard(),
    queryFn: async () => {
      const response = await shareService.getMyShareDashboard();
      return mapMyShareDashboardResponseToProps(response);
    },
  });
};
