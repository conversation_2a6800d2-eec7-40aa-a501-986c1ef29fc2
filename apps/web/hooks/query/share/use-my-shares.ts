import { useQuery } from '@tanstack/react-query';
import { shareService } from '@/lib/api/share/share.service';
import { shareKeys } from '../query-keys';
import type { GetSharesRequest } from '@/lib/api/share/share.schema.server';
import { mapMySharesResponseToShareRewardsProps } from './share.mapper';

export const useMyShares = (options: GetSharesRequest = {}) => {
  return useQuery({
    queryKey: shareKeys.myShares(options),
    queryFn: async () => {
      const response = await shareService.getMyShares(options);
      return mapMySharesResponseToShareRewardsProps(response);
    },
  });
};
