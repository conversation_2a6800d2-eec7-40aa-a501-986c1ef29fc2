import { useQuery } from '@tanstack/react-query';
import { portfolioService } from '@/lib/api/portfolio/portfolio.service';
import { PositionFilter, PositionOrder } from '@/lib/api/portfolio/portfolio.schema.server';
import { portfolioKeys } from '../query-keys';
import { mapPortfolioPositionsResponseToPositionsProps } from './portfolio.mapper';

export type { PortfolioPositionItem } from './portfolio.mapper';

export const usePortfolioPositions = (options?: {
  page?: number;
  limit?: number;
  filter?: PositionFilter;
  order?: PositionOrder;
}) => {
  return useQuery({
    queryKey: portfolioKeys.positions(options),
    queryFn: async () => {
      const response = await portfolioService.getPositions(options);
      return mapPortfolioPositionsResponseToPositionsProps(response);
    },
  });
};
