import { useQuery } from '@tanstack/react-query';
import { portfolioService } from '@/lib/api/portfolio/portfolio.service';
import { DisputeOrder } from '@/lib/api/portfolio/portfolio.schema.server';
import { portfolioKeys } from '../query-keys';
import { mapPortfolioDisputesToProps } from './portfolio.mapper';

export const usePortfolioDisputes = (options?: {
  page?: number;
  limit?: number;
  order?: DisputeOrder;
}) => {
  return useQuery({
    queryKey: portfolioKeys.disputes(options),
    queryFn: async () => {
      const response = await portfolioService.getDisputes(options);
      return mapPortfolioDisputesToProps(response);
    },
  });
};
