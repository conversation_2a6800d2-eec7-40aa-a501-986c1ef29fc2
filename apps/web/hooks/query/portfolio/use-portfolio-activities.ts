import { useQuery } from '@tanstack/react-query';
import { portfolioService } from '@/lib/api/portfolio/portfolio.service';
import { ActivityFilter, ActivityOrder } from '@/lib/api/portfolio/portfolio.schema.server';
import { portfolioKeys } from '../query-keys';
import { mapPortfolioActivitiesResponseToActivitiesProps } from './portfolio.mapper';

export type { PortfolioActivityItem } from './portfolio.mapper';

export const usePortfolioActivities = (options?: {
  page?: number;
  limit?: number;
  filter?: ActivityFilter;
  order?: ActivityOrder;
}) => {
  return useQuery({
    queryKey: portfolioKeys.activities(options),
    queryFn: async () => {
      const response = await portfolioService.getActivities(options);
      return mapPortfolioActivitiesResponseToActivitiesProps(response);
    },
  });
};
