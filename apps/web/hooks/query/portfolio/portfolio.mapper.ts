import { portfolioService } from '@/lib/api/portfolio/portfolio.service';
import { toRelativeTime, toScannerUrl } from '@/lib/utils';
import { formatUsdc } from '@/lib/format';
import { MARKET_STATUS_TEXT_MAP } from '@/lib/constants';

type GetPositionsReturnType = Awaited<ReturnType<typeof portfolioService.getPositions>>;

export const mapPortfolioPositionsResponseToPositionsProps = (response: GetPositionsReturnType) => {
  const positions = response.positions.map(position => {
    return {
      marketId: position.market.id,
      marketTitle: position.market.title,
      marketImageUrl: position.market.imageUrl,
      marketStatus: position.market.status ?? '',
      outcome: position.outcome,
      outcomeOrder: position.outcomeOrder,
      value: position.formattedValue,
      estimatedOddsFormartted: position.formattedEstimatedOdds,
      estimatedWinFormatted: position.formattedEstimatedWin,
    };
  });

  return {
    positions,
    totalLength: response.totalLength,
  };
};

export type PortfolioPositionsProps = ReturnType<
  typeof mapPortfolioPositionsResponseToPositionsProps
>;
export type PortfolioPositionItem = PortfolioPositionsProps['positions'][number];

type GetActivitiesReturnType = Awaited<ReturnType<typeof portfolioService.getActivities>>;

export const mapPortfolioActivitiesResponseToActivitiesProps = (
  response: GetActivitiesReturnType
) => {
  const activities = response.activities.map(activity => {
    return {
      marketId: activity.market.id,
      marketTitle: activity.market.title,
      marketImageUrl: activity.market.imageUrl,
      outcome: activity.outcomes[0]?.outcome || '',
      outcomeOrder: activity.outcomes[0]?.outcomeOrder.toString() || '',
      activityType: activity.type,
      activityAmount: activity.formattedAmount,
      activityDate: activity.timestamp,
      relativeTime: toRelativeTime(activity.timestamp),
      txHash: activity.transactionHash,
      txUrl: toScannerUrl(activity.transactionHash),
    };
  });

  return {
    totalLength: response.totalLength,
    activities,
  };
};

export type PortfolioActivitiesProps = ReturnType<
  typeof mapPortfolioActivitiesResponseToActivitiesProps
>;
export type PortfolioActivityItem = PortfolioActivitiesProps['activities'][number];

type GetPortfolioDisputesReturnType = Awaited<ReturnType<typeof portfolioService.getDisputes>>;

export const mapPortfolioDisputesToProps = (response: GetPortfolioDisputesReturnType) => {
  const disputes = response.disputes.map(dispute => {
    const statusText = MARKET_STATUS_TEXT_MAP[dispute.market.status] || dispute.market.status;

    // 리워드 계산 로직
    const getReward = () => {
      // 이의제기가 진행 중인 상태들
      const inProgressStatuses = ['DISPUTED', 'DISPUTABLE'];

      if (inProgressStatuses.includes(dispute.market.status)) {
        return '-';
      }

      // 이의제기가 종료된 경우 리워드 표시
      if (dispute.reward) {
        return formatUsdc(dispute.reward);
      }

      return '-';
    };

    return {
      id: dispute.market.id,
      marketId: dispute.market.id,
      marketTitle: dispute.market.title,
      marketImageUrl: dispute.market.imageUrl || '',
      amount: formatUsdc(dispute.amount),
      marketStatus: dispute.market.status,
      marketStatusText: statusText,
      reward: getReward(),
      timestamp: dispute.timestamp,
      claimedAt: dispute.claimedAt,
    };
  });

  return {
    disputes,
    totalLength: response.totalLength,
  };
};

export type PortfolioDisputesProps = ReturnType<typeof mapPortfolioDisputesToProps>;
export type PortfolioDisputeItem = PortfolioDisputesProps['disputes'][number];
