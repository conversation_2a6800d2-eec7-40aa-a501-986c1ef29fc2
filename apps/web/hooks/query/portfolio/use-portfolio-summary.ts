import { useUserPortfolio } from './use-user-portfolio';
import { useClaimableDispute } from './use-claimable-dispute';

export const usePortfolioSummary = () => {
  const portfolio = useUserPortfolio();
  const claimable = useClaimableDispute();

  return {
    data:
      portfolio.data && claimable.data
        ? {
            portfolio: portfolio.data,
            claimable: claimable.data,
          }
        : undefined,
    isLoading: portfolio.isLoading || claimable.isLoading,
    error: portfolio.error || claimable.error,
    refetch: () => {
      portfolio.refetch();
      claimable.refetch();
    },
  };
};
