import { useQuery } from '@tanstack/react-query';
import { portfolioService } from '@/lib/api/portfolio/portfolio.service';
import { portfolioKeys } from '../query-keys';
import { useGlobalStore } from '@/store/global.store';

export const useUserPortfolio = (enabled: boolean = true) => {
  return useQuery({
    queryKey: portfolioKeys.portfolio(),
    queryFn: () => portfolioService.getUserPortfolio(),
    enabled,
  });
};

export const useMyPortfolio = () => {
  const { safeSmartAccount } = useGlobalStore();
  return useUserPortfolio(!!safeSmartAccount);
};
