import { useMutation, useQueryClient } from '@tanstack/react-query';
import { predictService } from '@/lib/api/predict/predict.service';
import { FinalizeRequestBody } from '@/lib/api/predict/predict.schema';
import { ClientError } from '@/lib/error';
import { useGlobalStore } from '@/store/global.store';

export const useFinalizeAndGo = () => {
  const queryClient = useQueryClient();
  const { safeSmartAccount } = useGlobalStore();

  return useMutation({
    mutationFn: (finalizeData: FinalizeRequestBody) => {
      if (!safeSmartAccount) {
        throw ClientError.fromQuery(
          'SafeSmartAccount is not available. Please ensure you are connected to a wallet.'
        );
      }

      return predictService.finalizeAndGo(safeSmartAccount, finalizeData);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ['market', variables.marketId],
      });
    },
  });
};
