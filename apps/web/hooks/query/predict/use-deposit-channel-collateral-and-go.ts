import { usePopupStore } from '@/components/ui/popup/popup.state';
import { DepositChannelCollateralRequestBody } from '@/lib/api/predict/predict.schema';
import { predictService } from '@/lib/api/predict/predict.service';
import { ClientError } from '@/lib/error';
import { useGlobalStore } from '@/store/global.store';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { channelKeys } from '../query-keys';

export const useDepositChannelCollateralAndGo = () => {
  const queryClient = useQueryClient();
  const { safeSmartAccount } = useGlobalStore();
  const { closePopup } = usePopupStore();

  return useMutation({
    mutationFn: (channelCollateralData: DepositChannelCollateralRequestBody) => {
      if (!safeSmartAccount) {
        throw ClientError.fromQuery(
          'SafeSmartAccount is not available. Please ensure you are connected to a wallet.'
        );
      }

      return predictService.depositChannelCollateralAndGo(safeSmartAccount, channelCollateralData);
    },

    onSuccess: () => {
      closePopup();
      queryClient.invalidateQueries({ queryKey: channelKeys.collateral });
    },
  });
};
