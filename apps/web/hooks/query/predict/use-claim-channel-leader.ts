import { useMutation, useQueryClient } from '@tanstack/react-query';
import { predictService } from '@/lib/api/predict/predict.service';
import { ClientError } from '@/lib/error';
import { useGlobalStore } from '@/store/global.store';

export const useClaimChannelLeader = () => {
  const queryClient = useQueryClient();
  const { safeSmartAccount } = useGlobalStore();

  return useMutation({
    mutationFn: () => {
      if (!safeSmartAccount) {
        throw ClientError.fromQuery(
          'SafeSmartAccount is not available. Please ensure you are connected to a wallet.'
        );
      }

      return predictService.claimAndGo(safeSmartAccount, {
        distributionType: 2,
        category: 'ChannelLeader',
      });
    },
    onSuccess: () => {
      // Invalidate claimable dispute data to refresh the UI
      queryClient.invalidateQueries({
        queryKey: ['portfolio', 'claimableDispute'],
      });
    },
  });
};
