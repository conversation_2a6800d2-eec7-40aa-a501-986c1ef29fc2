import { useMutation, useQueryClient } from '@tanstack/react-query';
import { predictService } from '@/lib/api/predict/predict.service';
import { VoidRequestBody } from '@/lib/api/predict/predict.schema';
import { ClientError } from '@/lib/error';
import { useGlobalStore } from '@/store/global.store';

export const useVoidAndGo = () => {
  const queryClient = useQueryClient();
  const { safeSmartAccount } = useGlobalStore();

  return useMutation({
    mutationFn: (voidData: VoidRequestBody) => {
      if (!safeSmartAccount) {
        throw ClientError.fromQuery(
          'SafeSmartAccount is not available. Please ensure you are connected to a wallet.'
        );
      }

      return predictService.voidAndGo(safeSmartAccount, voidData);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ['market', variables.marketId],
      });
    },
  });
};
