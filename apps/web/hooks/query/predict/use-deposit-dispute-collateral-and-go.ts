import { useMutation, useQueryClient } from '@tanstack/react-query';
import { predictService } from '@/lib/api/predict/predict.service';
import { DepositDisputeCollateralRequestBody } from '@/lib/api/predict/predict.schema';
import { ClientError } from '@/lib/error';
import { useGlobalStore } from '@/store/global.store';
import { marketKeys } from '../query-keys';

export const useDepositDisputeCollateralAndGo = (marketId: string) => {
  const queryClient = useQueryClient();
  const { safeSmartAccount } = useGlobalStore();

  return useMutation({
    mutationFn: (disputeCollateralData: DepositDisputeCollateralRequestBody) => {
      if (!safeSmartAccount) {
        throw ClientError.fromQuery(
          'SafeSmartAccount is not available. Please ensure you are connected to a wallet.'
        );
      }

      return predictService.depositDisputeCollateralAndGo(safeSmartAccount, disputeCollateralData);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: marketKeys.market(marketId),
      });
    },
  });
};
