import { useMutation, useQueryClient } from '@tanstack/react-query';
import { predictService } from '@/lib/api/predict/predict.service';
import { WithdrawChannelCollateralRequestBody } from '@/lib/api/predict/predict.schema';
import { ClientError } from '@/lib/error';
import { useGlobalStore } from '@/store/global.store';
import { channelKeys } from '../query-keys';

export const useWithdrawChannelCollateralAndGo = () => {
  const queryClient = useQueryClient();
  const { safeSmartAccount } = useGlobalStore();

  return useMutation({
    mutationFn: (data: WithdrawChannelCollateralRequestBody) => {
      if (!safeSmartAccount) {
        throw ClientError.fromQuery(
          'SafeSmartAccount is not available. Please ensure you are connected to a wallet.'
        );
      }
      return predictService.withdrawChannelCollateralAndGo(safeSmartAccount, data);
    },

    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: channelKeys.collateral });
    },
  });
};
