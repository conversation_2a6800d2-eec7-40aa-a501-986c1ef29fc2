import { WithdrawRequestBody } from '@/lib/api/predict/predict.schema';
import { predictService } from '@/lib/api/predict/predict.service';
import { ClientError } from '@/lib/error';
import { useGlobalStore } from '@/store/global.store';
import { useMutation } from '@tanstack/react-query';

export const useWithdrawAndGo = () => {
  const { safeSmartAccount } = useGlobalStore();
  return useMutation({
    mutationFn: (withdrawData: WithdrawRequestBody) => {
      if (!safeSmartAccount) {
        throw ClientError.fromQuery(
          'SafeSmartAccount is not available. Please ensure you are connected to a wallet.'
        );
      }

      return predictService.withdrawAndGo(safeSmartAccount, withdrawData);
    },
  });
};
