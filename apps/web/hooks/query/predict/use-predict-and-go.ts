import { useMutation, useQueryClient } from '@tanstack/react-query';
import { predictService } from '@/lib/api/predict/predict.service';
import { PredictRequestBody } from '@/lib/api/predict/predict.schema';
import { ClientError } from '@/lib/error';
import { useGlobalStore } from '@/store/global.store';

export const usePredictAndGo = () => {
  const queryClient = useQueryClient();
  const { safeSmartAccount } = useGlobalStore();

  return useMutation({
    mutationFn: (data: PredictRequestBody) => {
      if (!safeSmartAccount) {
        throw ClientError.fromQuery(
          'SafeSmartAccount is not available. Please ensure you are connected to a wallet.'
        );
      }
      return predictService.predictAndGo(safeSmartAccount, data);
    },
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ['market', variables.marketId],
      });
      queryClient.invalidateQueries({
        queryKey: ['activities', variables.marketId, variables.outcome],
      });
    },
  });
};
