import { ClaimRequestBody } from '@/lib/api/predict/predict.schema';
import { predictService } from '@/lib/api/predict/predict.service';
import { ClientError } from '@/lib/error';
import { useGlobalStore } from '@/store/global.store';
import { useMutation } from '@tanstack/react-query';

export const useClaimAndGo = () => {
  // const queryClient = useQueryClient();
  const { safeSmartAccount } = useGlobalStore();

  return useMutation({
    mutationFn: (data: ClaimRequestBody) => {
      if (!safeSmartAccount) {
        throw ClientError.fromQuery(
          'SafeSmartAccount is not available. Please ensure you are connected to a wallet.'
        );
      }

      return predictService.claimAndGo(safeSmartAccount, data);
    },
  });
};

const DistributionType = {
  REVENUE: 0,
  COLLATERAL: 1,
  MARKET: 2,
} as const;

export const useMarketClaimAndGo = () => {
  const baseHook = useClaimAndGo();

  return useMutation({
    ...baseHook,
    mutationFn: () => {
      return baseHook.mutateAsync({
        distributionType: DistributionType.MARKET,
        category: 'ChannelLeader',
      });
    },
  });
};

export const useCommissionRewardClaimAndGo = () => {
  const baseHook = useClaimAndGo();

  return useMutation({
    ...baseHook,
    mutationFn: () => {
      return baseHook.mutateAsync({
        distributionType: DistributionType.REVENUE,
        category: 'CommissionReward',
      });
    },
  });
};

export const useFeeRebateClaimAndGo = () => {
  const baseHook = useClaimAndGo();

  return useMutation({
    ...baseHook,
    mutationFn: () => {
      return baseHook.mutateAsync({
        distributionType: DistributionType.REVENUE,
        category: 'FeeRebate',
      });
    },
  });
};

export const useShareClaimAndGo = () => {
  const baseHook = useClaimAndGo();

  return useMutation({
    ...baseHook,
    mutationFn: () => {
      return baseHook.mutateAsync({
        distributionType: DistributionType.REVENUE,
        category: 'Share',
      });
    },
  });
};

export const useDisputeClaimAndGo = () => {
  const baseHook = useClaimAndGo();

  return useMutation({
    ...baseHook,
    mutationFn: () => {
      return baseHook.mutateAsync({
        distributionType: DistributionType.COLLATERAL,
        category: 'Dispute',
      });
    },
  });
};
