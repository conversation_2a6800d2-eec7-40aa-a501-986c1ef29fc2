import { useQuery } from '@tanstack/react-query';
import { leaderboardKeys } from '../query-keys';
import { leaderboardService } from '@/lib/api/leaderboard/leaderboard.service';
import {
  LeaderboardPeriod,
  LeaderboardType,
} from '@/lib/api/leaderboard/leaderboard.schema.server';

export type LeaderboardItem = {
  userAvatarUrl: string | null;
  userNickname: string;
  userAddress: string;
  userRank: number;
  userValue: string;
};

const useLeaderboard = (type: LeaderboardType, period: LeaderboardPeriod) => {
  return useQuery({
    queryKey: leaderboardKeys.leaderboard(type, period),
    queryFn: () => {
      console.log('type, period', type, period);
      return leaderboardService.getLeaderboard(type, period);
    },
    enabled: !!(type && period),
    select: data => {
      const results = data.results.map(item => {
        return {
          userAvatarUrl: item.imageUrl,
          userNickname: item.nickname,
          userAddress: item.address,
          userRank: item.rank,
          userValue: item.formattedValue,
        };
      });
      return results;
    },
  });
};

export const useVolumeLeaderboard = (period: LeaderboardPeriod) => {
  return useLeaderboard('volume', period);
};

export const useProfitLeaderboard = (period: LeaderboardPeriod) => {
  return useLeaderboard('profit', period);
};

export default useLeaderboard;
