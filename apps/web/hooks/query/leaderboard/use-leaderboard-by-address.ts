import { useQuery } from '@tanstack/react-query';
import { leaderboardKeys } from '../query-keys';
import { leaderboardService } from '@/lib/api/leaderboard/leaderboard.service';
import {
  LeaderboardPeriod,
  LeaderboardType,
} from '@/lib/api/leaderboard/leaderboard.schema.server';

const useLeaderboardByAddress = (
  type: LeaderboardType,
  period: LeaderboardPeriod,
  address: string
) => {
  return useQuery({
    queryKey: leaderboardKeys.leaderboardByAddress(type, period, address),
    queryFn: () => leaderboardService.getLeaderboardByAddress(type, period, address),
    enabled: !!(type && period && address),
  });
};

export default useLeaderboardByAddress;
