import { leaderboardService } from '@/lib/api/leaderboard/leaderboard.service';
import { useQuery } from '@tanstack/react-query';
import { leaderboardKeys } from '../query-keys';
import type { LeaderboardPeriod } from '@/lib/api/leaderboard/leaderboard.schema.server';

export const useChannelLeaderboard = (period: LeaderboardPeriod) => {
  return useQuery({
    queryKey: leaderboardKeys.channelLeaderboard(period),
    queryFn: () => leaderboardService.getChannelLeaderboard(period),
    enabled: !!period,
  });
};
