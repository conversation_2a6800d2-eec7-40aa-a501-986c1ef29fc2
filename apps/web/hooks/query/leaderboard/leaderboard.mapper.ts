import { leaderboardService } from '@/lib/api/leaderboard/leaderboard.service';

type GetPredictLeaderboardReturnType = Awaited<
  ReturnType<typeof leaderboardService.getPredictLeaderboard>
>;
type GetPredictLeaderboardByAddressReturnType = Awaited<
  ReturnType<typeof leaderboardService.getPredictLeaderboardByAddress>
>;

export const mapPredictLeaderboardResponseToRankUserProps = (
  response: GetPredictLeaderboardReturnType,
  currentUserAddress?: string
) => {
  const results = response.results.map(user => ({
    userAddress: user.address,
    userAvatarUrl: user.imageUrl,
    userUsername: user.nickname,
    isCurrentUser: currentUserAddress
      ? user.address.toLowerCase() === currentUserAddress.toLowerCase()
      : false,
    rank: user.rank,
    amount: user.formattedValue,
  }));

  return results;
};

export const mapPredictLeaderboardByAddressResponseToRankUserProps = (
  response: GetPredictLeaderboardByAddressReturnType,
  currentUserAddress?: string
) => {
  return {
    userAvatarUrl: response.imageUrl || '',
    userUsername: response.nickname,
    isCurrentUser: currentUserAddress ? response.address === currentUserAddress : false,
    rank: response.rank,
    amount: response.formattedValue,
  };
};

export type PredictLeaderboardRankUserProps = ReturnType<
  typeof mapPredictLeaderboardResponseToRankUserProps
>;
export type PredictLeaderboardRankUserItem = PredictLeaderboardRankUserProps[number];
export type PredictLeaderboardByAddressRankUserProps = ReturnType<
  typeof mapPredictLeaderboardByAddressResponseToRankUserProps
>;
