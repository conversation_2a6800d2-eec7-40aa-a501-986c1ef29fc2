import { leaderboardService } from '@/lib/api/leaderboard/leaderboard.service';
import { useQuery } from '@tanstack/react-query';
import { leaderboardKeys } from '../query-keys';
import type { LeaderboardPeriod } from '@/lib/api/leaderboard/leaderboard.schema.server';

export const useChannelLeaderboardByChannelId = (period: LeaderboardPeriod, channelId: string) => {
  return useQuery({
    queryKey: leaderboardKeys.channelLeaderboardByChannelId(period, channelId),
    queryFn: () => leaderboardService.getChannelLeaderboardByChannelId(period, channelId),
    enabled: !!(period && channelId),
  });
};
