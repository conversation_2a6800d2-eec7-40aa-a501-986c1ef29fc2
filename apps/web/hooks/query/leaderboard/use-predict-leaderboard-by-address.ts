import { leaderboardService } from '@/lib/api/leaderboard/leaderboard.service';
import { useQuery } from '@tanstack/react-query';
import { leaderboardKeys } from '../query-keys';
import { mapPredictLeaderboardByAddressResponseToRankUserProps } from './leaderboard.mapper';
import type {
  LeaderboardPeriod,
  PredictLeaderboardType,
} from '@/lib/api/leaderboard/leaderboard.schema.server';

export const usePredictLeaderboardByAddress = (
  type: PredictLeaderboardType,
  period: LeaderboardPeriod,
  address: string,
  currentUserAddress?: string
) => {
  return useQuery({
    queryKey: leaderboardKeys.predictLeaderboardByAddress(type, period, address),
    queryFn: async () => {
      const response = await leaderboardService.getPredictLeaderboardByAddress(
        type,
        period,
        address
      );
      return mapPredictLeaderboardByAddressResponseToRankUserProps(response, currentUserAddress);
    },
    enabled: !!(type && period && address),
  });
};
