import { leaderboardService } from '@/lib/api/leaderboard/leaderboard.service';
import { useQuery } from '@tanstack/react-query';
import { leaderboardKeys } from '../query-keys';
import { mapPredictLeaderboardResponseToRankUserProps } from './leaderboard.mapper';
import type {
  LeaderboardPeriod,
  PredictLeaderboardType,
} from '@/lib/api/leaderboard/leaderboard.schema.server';
import { useGlobalStore } from '@/store/global.store';

const usePredictLeaderboard = (type: PredictLeaderboardType, period: LeaderboardPeriod) => {
  const currentUserAddress = useGlobalStore(v => v.safeSmartAccount?.address);

  return useQuery({
    enabled: !!(type && period),
    queryKey: leaderboardKeys.predictLeaderboard(type, period),
    queryFn: async () => {
      const response = await leaderboardService.getPredictLeaderboard(type, period);
      return mapPredictLeaderboardResponseToRankUserProps(response, currentUserAddress);
    },
  });
};

export default usePredictLeaderboard;
