import { useMutation, useQueryClient } from '@tanstack/react-query';
import { boardKeys } from '../query-keys';
import { boardService } from '@/lib/api/board/board.service';

export const useTogglePostPin = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ postId, isPinned }: { postId: string; isPinned: boolean }) => {
      return isPinned ? boardService.unpinPost(postId) : boardService.pinPost(postId);
    },
    onSuccess: (_, { postId }) => {
      queryClient.invalidateQueries({
        queryKey: boardKeys.post(postId),
      });
      queryClient.invalidateQueries({
        queryKey: boardKeys.board,
      });
    },
  });
};
