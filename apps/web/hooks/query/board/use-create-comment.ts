import { useMutation, useQueryClient } from '@tanstack/react-query';
import { boardKeys } from '../query-keys';
import { boardService } from '@/lib/api/board/board.service';
import { CreateCommentRequest } from '@/lib/api/board/board.schema.server';

export const useCreateComment = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ parentId, data }: { parentId: string; data: CreateCommentRequest }) =>
      boardService.createComment(parentId, data),
    onSuccess: (_, { parentId }) => {
      queryClient.invalidateQueries({
        queryKey: boardKeys.comments(parentId),
      });
    },
  });
};
