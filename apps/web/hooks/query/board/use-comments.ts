import { boardService } from '@/lib/api/board/board.service';
import { useQuery } from '@tanstack/react-query';
import { boardKeys } from '../query-keys';

export const useComments = (
  parentId: string,
  options?: {
    page?: number;
    limit?: number;
    order?: 'latest' | 'likes';
    withPositions?: 'true' | 'false';
    onlyPredictors?: 'true' | 'false';
  }
) => {
  return useQuery({
    // queryKey: [...boardKeys.comments(parentId), options],
    queryKey: [...boardKeys.comments(parentId), options],
    queryFn: () => boardService.getComments(parentId, options),
    enabled: !!parentId,
  });
};
