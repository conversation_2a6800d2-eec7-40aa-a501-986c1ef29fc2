import { useQuery } from '@tanstack/react-query';
import { boardKeys } from '../query-keys';
import { boardService } from '@/lib/api/board/board.service';

export const useChannelPosts = (channelId: string, options?: { page?: number; limit?: number }) => {
  return useQuery({
    queryKey: [...boardKeys.channelPosts(channelId), options],
    queryFn: () => boardService.getChannelPosts(channelId, options),
    enabled: !!channelId,
  });
};
