import { marketService } from '@/lib/api/market/market.service';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import useSignCreateMarket, { CreateMarketMessage } from '../../use-sign-create-market';
import { marketKeys } from '../query-keys';
import { ApiError } from '@/lib/api/base-api.error';
import { toast } from '@repo/ui/components/sonner';
import { useRouter } from 'next/navigation';
import { INNER_LINKS } from '@/lib/constants';

interface CreateMarketFlowOptions {
  onSigningStart?: () => void;
  onSigningComplete?: (signature: string) => void;
  onApiCallStart?: () => void;
  onSuccess?: (data: any) => void;
  onError?: (error: Error, message: string) => void;
}

export const useCreateMarketFlow = (options?: CreateMarketFlowOptions) => {
  const queryClient = useQueryClient();
  const { signCreateMarket } = useSignCreateMarket();
  const router = useRouter();
  return useMutation({
    mutationFn: async (data: CreateMarketMessage & { image?: File }) => {
      const signature = await signCreateMarket({
        ...data,
      });

      const apiData = {
        ...data,
        collateralAmount: data.collateralAmount.toString(),
        signature,
      };

      if (data.image) {
        apiData.image = data.image;
      }

      const result = await marketService.createMarket(apiData);
      return result;
    },
    onSuccess: data => {
      queryClient.invalidateQueries({
        queryKey: marketKeys.markets,
      });

      if (data?.id) {
        toast.success('Prediction generated!');
        router.push(INNER_LINKS.MAIN.MARKETS.DETAIL(data.id));
      }
    },
    onError: error => {
      if (ApiError.isApiError(error)) {
        toast.error('Failed to create market');
        return;
      }

      if (error instanceof Error && error.message.includes('signature')) {
        toast.error('Failed to sign the transaction');
        return;
      }

      throw error;
    },
  });
};
