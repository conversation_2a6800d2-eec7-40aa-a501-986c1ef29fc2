import { useQuery } from '@tanstack/react-query';
import { marketKeys } from '../query-keys';
import { marketService } from '@/lib/api/market/market.service';

export const useMarketActivities = (
  marketId: string,
  outcome: string,
  options?: { page?: number; limit?: number }
) => {
  const enabled = !!(marketId && outcome);

  return useQuery({
    queryKey: [...marketKeys.activities(marketId, outcome), options],
    queryFn: () => marketService.getMarketActivities(marketId, outcome, options),
    enabled,
  });
};
