import { useQuery } from '@tanstack/react-query';
import { marketKeys } from '../query-keys';
import { marketService } from '@/lib/api/market/market.service';
import { mapMarketsResponseToMarketsProps } from './market.mapper';

export const useMarketsByCategory = (
  category: string,
  tag?: string,
  options?: {
    page?: number;
    limit?: number;
    order?: 'VOLUME' | 'NEWEST' | 'ENDING_SOON' | 'COMPETITIVE';
  }
) => {
  return useQuery({
    queryKey: [...marketKeys.markets, category, options],
    queryFn: async () => {
      const markets = await marketService.getMarketByCategory(category, tag, options);
      return mapMarketsResponseToMarketsProps(markets);
    },
    enabled: !!category,
  });
};
