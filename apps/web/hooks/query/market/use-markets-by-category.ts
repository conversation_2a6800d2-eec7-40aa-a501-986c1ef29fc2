import { useQuery } from '@tanstack/react-query';
import { marketKeys } from '../query-keys';
import { marketService } from '@/lib/api/market/market.service';
import { mapMarketsResponseToMarketsProps } from './market.mapper';
import { GetMarketsByCategoryRequestOptions } from '@/lib/api/market/market.schema.server';

export const useMarketsByCategory = (
  category: string,
  options?: GetMarketsByCategoryRequestOptions
) => {
  return useQuery({
    enabled: !!category,
    queryKey: [...marketKeys.markets, category, options],
    queryFn: async () => {
      const markets = await marketService.getMarketByCategory(category, options);
      return mapMarketsResponseToMarketsProps(markets);
    },
  });
};
