import { marketService } from '@/lib/api/market/market.service';
import { useQuery } from '@tanstack/react-query';
import { marketKeys } from '../query-keys';
import { mapMarketsResponseToMarketsProps } from './market.mapper';
import type { GetMarketsRequestOptions } from '@/lib/api/market/market.schema.server';
import { normalizeOptions } from '@/lib/utils';

export const useMarkets = (options?: GetMarketsRequestOptions) => {
  return useQuery({
    queryKey: [...marketKeys.markets, options],
    queryFn: async () => {
      const markets = await marketService.getMarkets(options);
      return mapMarketsResponseToMarketsProps(markets);
    },
  });
};

export const useMarketsWithAll = (options?: GetMarketsRequestOptions) => {
  return useQuery({
    queryKey: ['useMarketsWithAll', normalizeOptions(options)],
    queryFn: async () => {
      const markets = await marketService.getMarketsWithAll(options);
      return mapMarketsResponseToMarketsProps(markets);
    },
  });
};
