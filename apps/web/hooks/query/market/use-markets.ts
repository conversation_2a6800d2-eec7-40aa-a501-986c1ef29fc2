import { marketService } from '@/lib/api/market/market.service';
import { useQuery } from '@tanstack/react-query';
import { marketKeys } from '../query-keys';
import { mapMarketsResponseToMarketsProps } from './market.mapper';

export const useMarkets = (options?: { page?: number; limit?: number }) => {
  return useQuery({
    queryKey: [...marketKeys.markets, options],
    queryFn: async () => {
      const markets = await marketService.getMarkets();
      return mapMarketsResponseToMarketsProps(markets);
    },
  });
};
