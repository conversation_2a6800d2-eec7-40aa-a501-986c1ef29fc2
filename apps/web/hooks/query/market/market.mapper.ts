import { marketService } from '@/lib/api/market/market.service';
import { MarketOutcome } from '@/lib/api/market/market.transform';

type GetMarketsReturnType = Awaited<ReturnType<typeof marketService.getMarkets>>;

export const mapMarketsResponseToMarketsProps = (response: GetMarketsReturnType) => {
  const markets = response.markets.map(market => {
    const getNextDeadline = () => {
      const status = market.status;
      switch (status) {
        case 'OPEN':
          return market.predictionDeadline;
        case 'REVIEWING':
          return market.resultConfirmDeadline;
        case 'DISPUTABLE':
          return market.disputeDeadline;
        case 'DISPUTED':
          return market.disputedAt ? market.disputedAt + 5 * 24 * 60 * 60 * 1000 : null; // 5일 후
        default:
          return null;
      }
    };
    return {
      marketId: market.id,
      marketTitle: market.title,
      marketAvatarImageUrl: market.imageUrl,
      marketPredictionDeadline: market.predictionDeadline,
      marketConfirmationDeadline: market.resultConfirmDeadline,
      marketTotalVolume: market.rawTotalVolume,
      marketStatus: market.status,
      marketTotalVolumeFormatted: market.formattedTotalVolume,
      marketParticipants: market.totalPredictor,
      marketMaxOutcomeVolume: market.rawMaxOutcomeVolume,
      marketMaxOutcomeVolumeFormatted: market.formattedMaxOutcomeVolume,
      marketStatusText: market.statusText,
      marketOutcomes: market.outcomes,
      marketNextDeadline: getNextDeadline(),
      marketProposedOutcome: market.proposedOutcome ?? null,
      channelId: market.channel.id,
      channelName: market.channel.name,
      channelAvatarImageUrl: market.channel.imageUrl,
    };
  });

  return {
    markets,
    totalLength: response.totalLength,
  };
};

export type MarketsProps = ReturnType<typeof mapMarketsResponseToMarketsProps>;
export type MarketListItem = MarketsProps['markets'][number];

type GetMarketReturnType = Awaited<ReturnType<typeof marketService.getMarketById>>;

export const mapMarketResponseToMarketProps = (market: GetMarketReturnType) => {
  const getNextDeadline = () => {
    const status = market.status;
    switch (status) {
      case 'OPEN':
        return market.predictionDeadline;
      case 'REVIEWING':
        return market.resultConfirmDeadline;
      case 'DISPUTABLE':
        return market.disputeDeadline;
      case 'DISPUTED':
        return market.disputedAt ? market.disputedAt + 5 * 24 * 60 * 60 * 1000 : null; // 5일 후
      default:
        return null;
    }
  };
  return {
    marketId: market.id,
    marketTitle: market.title,
    marketDescription: market.description,
    marketReferenceUrl: market.referenceURL,
    marketBroadcastUrl: market.broadcastURL,
    marketCompetitive: market.competitive,
    marketMinPredictCount: market.minPredictCount,
    marketMaxOutcomeVolume: market.rawMaxOutcomeVolume,
    marketMaxOutcomeVolumeFormatted: market.formattedMaxOutcomeVolume,
    marketCreatedAt: market.createdAt,
    marketAvatarImageUrl: market.imageUrl,
    marketPredictionDeadline: market.predictionDeadline,
    marketConfirmationDeadline: market.resultConfirmDeadline,
    marketTotalVolume: market.rawTotalVolume,
    marketTotalVolumeFormatted: market.formattedTotalVolume,
    marketParticipants: market.totalPredictor,
    marketPredictCount: market.predictCount,
    marketStatus: market.status,
    marketStatusText: market.statusText,
    marketOutcomes: market.outcomes,
    marketTopOutcomeVolume: market.rawMaxOutcomeVolume,
    marketTopOutcomeVolumeFormatted: market.formattedMaxOutcomeVolume,
    marketOutcomeProposedAt: market.outcomeProposedAt,
    marketProposedOutcome: market.proposedOutcome,
    marketDisputedAt: market.disputedAt,
    marketFinalizedAt: market.finalizedAt,
    marketNextDeadline: getNextDeadline(),
    channelId: market.channel.id,
    channelName: market.channel.name,
    channelAvatarImageUrl: market.channel.imageUrl,
  };
};

export type MarketProps = ReturnType<typeof mapMarketResponseToMarketProps>;

export const getMarketTopOutcomeVolume = (outcomes: MarketOutcome[]) => {
  let maxOutcomeIndex = 0;
  for (let i = 0; i < outcomes.length; i++) {
    const outcome = outcomes[i]!;
    if (outcome.rawVolume > outcomes[maxOutcomeIndex]!.rawVolume) {
      maxOutcomeIndex = i;
    }
  }
  return outcomes[maxOutcomeIndex]!;
};
