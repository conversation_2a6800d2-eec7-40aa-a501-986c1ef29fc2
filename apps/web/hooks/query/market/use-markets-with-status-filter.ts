import { GetMarketsRequestOptions, MarketStatusEnum } from '@/lib/api/market/market.schema.server';
import { marketService } from '@/lib/api/market/market.service';
import { useQuery } from '@tanstack/react-query';
import { marketKeys } from '../query-keys';
import { mapMarketsResponseToMarketsProps } from './market.mapper';

export const useMarketsWithStatusFilter = (
  status: MarketStatusEnum[] | undefined,
  options?: GetMarketsRequestOptions
) => {
  return useQuery({
    queryKey: marketKeys.marketsWithStatus(status, options),
    queryFn: async () => {
      const markets = await marketService.getMarketsWithStatusFilter(status, options);
      return mapMarketsResponseToMarketsProps(markets);
    },
  });
};

export const useOnlyOpenMarkets = (options?: GetMarketsRequestOptions) => {
  return useMarketsWithStatusFilter(['OPEN'], options);
};
