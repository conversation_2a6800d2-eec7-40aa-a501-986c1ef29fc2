import { useQuery } from '@tanstack/react-query';
import { marketKeys } from '../query-keys';
import { marketService } from '@/lib/api/market/market.service';

export const useMarketTopPredictors = (
  marketId: string,
  outcome: string,
  options?: { page?: number; limit?: number }
) => {
  return useQuery({
    queryKey: [...marketKeys.topPredictors(marketId, outcome), options],
    queryFn: () => marketService.getMarketTopPredictors(marketId, outcome, options),
    enabled: !!(marketId && outcome),
  });
};
