import { useQuery } from '@tanstack/react-query';
import { marketKeys } from '../query-keys';
import { marketService } from '@/lib/api/market/market.service';
import { useState, useEffect } from 'react';

const useDebounce = (value: string, delay: number) => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

export const useSearchMarkets = (query: string, options?: { page?: number; limit?: number }) => {
  const debouncedQuery = useDebounce(query, 300);

  return useQuery({
    queryKey: [...marketKeys.search(debouncedQuery), options],
    queryFn: () => marketService.searchMarkets(debouncedQuery, options),
    enabled: !!debouncedQuery && debouncedQuery.length >= 2,
  });
};
