import useSignPropose from '@/hooks/use-sign-propose';
import { ProposeRequestBodySchema } from '@/lib/api/market/market.schema.server';
import { marketService } from '@/lib/api/market/market.service';
import { ClientError } from '@/lib/error';
import { useGlobalStore } from '@/store/global.store';
import { useMutation } from '@tanstack/react-query';

export const useProposeMarketFlow = () => {
  const { signPropose } = useSignPropose();
  const safeSmartAccountAddress = useGlobalStore(v => v.safeSmartAccount?.address);

  return useMutation({
    mutationFn: async ({ marketId, outcome }: { marketId: string; outcome: string }) => {
      if (!safeSmartAccountAddress) {
        throw ClientError.fromQuery('Safe smart account address is not found', {
          queryName: 'useProposeFlow',
        });
      }

      const signature = await signPropose({
        proposer: safeSmartAccountAddress,
        marketId,
        outcome,
      });

      const parsed = ProposeRequestBodySchema.safeParse({
        marketId,
        outcome,
        signature,
      });

      if (!parsed.success) {
        throw ClientError.fromQuery('Invalid request::' + parsed.error.message, {
          queryName: 'useProposeFlow',
        });
      }

      await marketService.proposeMarket(parsed.data);
    },
  });
};
