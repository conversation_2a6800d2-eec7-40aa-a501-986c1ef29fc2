import { useQuery } from '@tanstack/react-query';
import { marketKeys } from '../query-keys';
import { marketService } from '@/lib/api/market/market.service';
import { mapMarketResponseToMarketProps } from './market.mapper';

export const useMarketById = (marketId: string) => {
  return useQuery({
    queryKey: marketKeys.market(marketId),
    enabled: !!marketId,
    queryFn: async () => {
      const market = await marketService.getMarketById(marketId);
      return mapMarketResponseToMarketProps(market);
    },
  });
};
