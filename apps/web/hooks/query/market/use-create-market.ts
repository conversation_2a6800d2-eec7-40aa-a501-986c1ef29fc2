import { marketService } from '@/lib/api/market/market.service';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { marketKeys } from '../query-keys';

export const useCreateMarket = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: marketService.createMarket,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: marketKeys.markets,
      });
    },
  });
};
