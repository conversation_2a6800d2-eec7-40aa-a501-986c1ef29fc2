'use client';

import { useQuery } from '@tanstack/react-query';
import { useEffect } from 'react';
import { SafeSmartAccount } from '@/lib/types';
import { safeSmartAccountKeys } from './query-keys';
import { getSmartAccount } from '@/lib/web3/wagmi';
import { useGlobalStore } from '@/store/global.store';
import { useAccount, useWalletClient } from 'wagmi';
import { WalletClient } from 'viem';

/**
 * 세션 기반 SafeSmartAccount 생성 훅
 * 세션이 존재할 때만 SafeSmartAccount를 생성
 */
export const useSafeSmartAccount = () => {
  const { session, setSafeSmartAccount } = useGlobalStore();
  const { isConnected } = useAccount();
  const { data: walletClient } = useWalletClient();

  const query = useQuery({
    queryKey: [...safeSmartAccountKeys.account(session?.address), walletClient?.account.address],
    queryFn: async ({ queryKey }): Promise<SafeSmartAccount> => {
      const [, sessionAddress, walletAddress] = queryKey as unknown as readonly [
        string,
        string | undefined,
        `0x${string}` | undefined,
      ];

      if (!sessionAddress || !walletAddress) {
        throw new Error('Session required for SafeSmartAccount creation');
      }

      const smartAccount = await getSmartAccount(walletClient as WalletClient);
      return smartAccount;
    },
    retry: (failureCount, error) => {
      // 최대 3번 재시도, 지수 백오프
      if (failureCount >= 3) return false;

      // 세션이 없는 경우는 재시도하지 않음
      if (error.message.includes('Session required')) return false;
      return true;
    },
    retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 30000),
    enabled: !!session?.address && !!walletClient?.account.address,
  });

  useEffect(() => {
    if (query.data && isConnected && walletClient?.account.address) {
      setSafeSmartAccount(query.data);
    }
  }, [query.data, setSafeSmartAccount, isConnected, walletClient?.account.address]);

  return query;
};
