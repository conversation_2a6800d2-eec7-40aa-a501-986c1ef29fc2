'use client';

import { useQuery } from '@tanstack/react-query';
import { useEffect } from 'react';
import { SafeSmartAccount } from '@/lib/types';
import { safeSmartAccountKeys } from './query-keys';
import { getSmartAccount, wagmiConfig } from '@/lib/web3/wagmi';
import { useGlobalStore } from '@/store/global.store';
import { getWalletClient } from '@wagmi/core';
import { useAppKitAccount } from '@reown/appkit/react';

/**
 * 세션 기반 SafeSmartAccount 생성 훅
 * 세션이 존재할 때만 SafeSmartAccount를 생성
 */
export const useSafeSmartAccount = () => {
  const { session, setSafeSmartAccount } = useGlobalStore();
  const { isConnected, address: appKitAddress } = useAppKitAccount({ namespace: 'eip155' });

  const query = useQuery({
    queryKey: [...safeSmartAccountKeys.account(session?.address), appKitAddress],
    queryFn: async ({ queryKey }): Promise<SafeSmartAccount> => {
      const [, sessionAddress] = queryKey as unknown as readonly [string, string | undefined];

      if (!sessionAddress) {
        throw new Error('Session required for SafeSmartAccount creation');
      }

      // 쿼리 실행 시점에 실제 walletClient 가져오기 (레이스 컨디션 방지)
      const currentWalletClient = await getWalletClient(wagmiConfig);

      if (!currentWalletClient) {
        throw new Error('WalletClient not available at query execution time');
      }

      if (!currentWalletClient.account?.address) {
        throw new Error('WalletClient account not available at query execution time');
      }

      // 주소 일치 검증 (쿼리 키와 실제 현재 주소)
      if (currentWalletClient.account.address.toLowerCase() !== sessionAddress.toLowerCase()) {
        throw new Error('WalletClient address mismatch during query execution');
      }

      const smartAccount = await getSmartAccount(currentWalletClient);
      return smartAccount;
    },

    enabled: !!session?.address && isConnected && !!appKitAddress,
  });

  useEffect(() => {
    if (query.data && isConnected) {
      setSafeSmartAccount(query.data);
    } else {
      setSafeSmartAccount(undefined);
    }
  }, [query.data, setSafeSmartAccount, isConnected]);

  return query;
};
