import { useMutation, useQueryClient } from '@tanstack/react-query';
import { channelKeys } from '../query-keys';
import { channelService } from '@/lib/api/channel/channel.service';
import { UpdateChannelRequestBody } from '@/lib/api/channel/channel.schema.server';

export const useUpdateChannel = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: UpdateChannelRequestBody) => channelService.updateChannel(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: channelKeys.myChannel });
    },
  });
};
