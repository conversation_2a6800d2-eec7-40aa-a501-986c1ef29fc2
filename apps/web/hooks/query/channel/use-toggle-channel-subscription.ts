import { channelService } from '@/lib/api/channel/channel.service';
import { useMutation } from '@tanstack/react-query';

export const useToggleChannelSubscription = () => {
  return useMutation({
    mutationFn: ({
      channelId,
      isCurrentlySubscribed,
    }: {
      channelId: string;
      isCurrentlySubscribed: boolean;
    }) => channelService.toggleChannelSubscription(channelId, isCurrentlySubscribed),
  });
};
