import { useQuery } from '@tanstack/react-query';
import { channelKeys } from '../query-keys';
import { channelService } from '@/lib/api/channel/channel.service';
import { ChannelCollateralHistoryResponseSchema } from '@/lib/api/channel/channel.schema.server';
import { z } from 'zod';
import { formatUsdc } from '@/lib/format';
import { toLocalDate } from '@/lib/utils';

const getHistoryTypeLabel = (type: string) => {
  switch (type) {
    case 'ChannelCollateralDeposit':
      return 'Add Deposit';
    case 'ChannelCollateralWithdraw':
      return 'Withdraw';
    case 'ChannelCollateralDisputeWin':
      return 'Win a dispute';
    case 'ChannelCollateralDisputeLose':
      return 'Lose a dispute';
    case 'ChannelCollateralDisputeRefund':
      return 'Refund a dispute';
    default:
      return type;
  }
};

export const mapCollateralHistoryResponseToCollateralHistoryProps = (
  response: z.infer<typeof ChannelCollateralHistoryResponseSchema>
) => {
  return {
    histories: response.histories.map(history => ({
      ...history,
      label: getHistoryTypeLabel(history.type),
      formattedAmount: formatUsdc(history.amount),
      timestamp: toLocalDate(history.timestamp),
    })),
    totalLength: response.totalLength,
  };
};

export const useChannelCollateralHistory = (options?: { page?: number; limit?: number }) => {
  return useQuery({
    queryKey: [...channelKeys.collateralHistory, options],
    queryFn: async () => {
      const response = await channelService.getCollateralHistory(options);
      return mapCollateralHistoryResponseToCollateralHistoryProps(response);
    },
  });
};
