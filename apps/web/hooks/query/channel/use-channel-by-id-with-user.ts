import { useQuery } from '@tanstack/react-query';
import { channelKeys } from '../query-keys';
import { channelService } from '@/lib/api/channel/channel.service';
import { ChannelInfoResponse } from '@/lib/api/channel/channel.schema.server';
import { useGlobalStore } from '@/store/global.store';

export type ChannelByIdWithUser = {
  channelBannerUrl: string;
  channelAvatarUrl: string;
  channelName: string;
  channelDescription: string;
  channelSubscribers: number;
  channelTotalVolume: string;
  channelTotalMarkets: number;
  channelIsLeaderLive: boolean;
  channelIsSubscribed: boolean;
  channelSns: ChannelInfoResponse['channelSns'];
  userNickname: string;
  userAddress: string;
};

type GetChannelByIdWithUserReturnType = Awaited<
  ReturnType<typeof channelService.getChannelByIdWithUser>
>;

export const mapChannelByIdWithUserResponseToChannelByIdWithUserProps = (
  response: GetChannelByIdWithUserReturnType,
  smartAccountAddress: string | undefined
) => {
  return {
    channelBannerUrl: response.channel.bannerUrl,
    channelAvatarUrl: response.channel.imageUrl,
    channelName: response.channel.name,
    channelDescription: response.channel.description,
    channelSubscribers: response.channel.subscribers,
    channelTotalVolume: response.channel.totalVolumeFormatted,
    channelTotalMarkets: response.channel.totalMarkets,
    channelIsLeaderLive: response.channel.isLeaderLive,
    channelIsSubscribed: response.channel.isSubscribed,
    channelSns: response.channel.channelSns,
    userNickname: response.user?.nickname,
    userAddress: response.user?.address,
    isMyChannel: smartAccountAddress
      ? smartAccountAddress.toLowerCase() === response.channel.id.toLowerCase()
      : false,
  };
};

export type ChannelByIdWithUserProps = ReturnType<
  typeof mapChannelByIdWithUserResponseToChannelByIdWithUserProps
>;

export const useChannelByIdWithUser = (channelId: string) => {
  const safeSmartAccountAddress = useGlobalStore(v => v.safeSmartAccount?.address);
  return useQuery({
    queryKey: channelKeys.channelWithUser(channelId),
    enabled: !!channelId,
    queryFn: async () => {
      const response = await channelService.getChannelByIdWithUser(channelId);
      return mapChannelByIdWithUserResponseToChannelByIdWithUserProps(
        response,
        safeSmartAccountAddress
      );
    },
  });
};
