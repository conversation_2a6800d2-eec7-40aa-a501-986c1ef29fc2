import { useQuery } from '@tanstack/react-query';
import { channelKeys } from '../query-keys';
import { channelService } from '@/lib/api/channel/channel.service';

export const useChannelSearch = (query: string, options?: { page?: number; limit?: number }) => {
  return useQuery({
    queryKey: [...channelKeys.channelSearch, query, options],
    queryFn: () => channelService.searchChannels(query, options),
    enabled: !!query && query.length >= 2,
  });
};
