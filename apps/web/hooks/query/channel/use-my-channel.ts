import { useQuery } from '@tanstack/react-query';
import { channelKeys } from '../query-keys';
import { channelService } from '@/lib/api/channel/channel.service';
import { useGlobalStore } from '@/store/global.store';

export const useMyChannel = () => {
  const { safeSmartAccount } = useGlobalStore();

  return useQuery({
    queryKey: channelKeys.myChannel,
    queryFn: () => channelService.getChannelById(safeSmartAccount!.address),
    enabled: !!safeSmartAccount?.address,
  });
};
