import { useQuery } from '@tanstack/react-query';
import { channelKeys } from '../query-keys';
import { channelService } from '@/lib/api/channel/channel.service';
import type { PredictLeaderboardType } from '@/lib/api/leaderboard/leaderboard.schema.server';

type GetChannelLeaderboardReturnType = Awaited<
  ReturnType<typeof channelService.getChannelLeaderboard>
>;

export const mapChannelLeaderboardResponseToChannelLeaderboardProps = (
  response: GetChannelLeaderboardReturnType
) => {
  const rankings = response.rankings.map(item => ({
    userRank: item.rank,
    userAddress: item.address,
    userNickname: item.nickname,
    userPnl: item.formattedPnl,
    userVolume: item.formattedVolume,
    userAvatarurl: '',
  }));
  return { rankings, myRank: response.myRank ?? null };
};

export type ChannelLeaderboardProps = ReturnType<
  typeof mapChannelLeaderboardResponseToChannelLeaderboardProps
>;

export const useChannelLeaderboard = (channelId: string, type: PredictLeaderboardType) => {
  return useQuery({
    enabled: !!channelId,
    queryKey: channelKeys.channelLeaderboard(channelId, type),
    queryFn: async () => {
      const response = await channelService.getChannelLeaderboard(channelId, type);
      return mapChannelLeaderboardResponseToChannelLeaderboardProps(response);
    },
  });
};

export const useChannelLeaderboardByProfit = (channelId: string) => {
  return useChannelLeaderboard(channelId, 'PROFIT');
};
