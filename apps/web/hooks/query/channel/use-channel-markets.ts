import type { MarketOrder, MarketStatus } from '@/lib/api/channel/channel.schema.server';
import { channelService } from '@/lib/api/channel/channel.service';
import { useQuery } from '@tanstack/react-query';
import { mapMarketsResponseToMarketsProps } from '../market/market.mapper';
import { channelKeys } from '../query-keys';

export const useChannelMarkets = (
  channelId: string,
  options?: {
    status?: MarketStatus;
    order?: MarketOrder;
    page?: number;
    limit?: number;
  }
) => {
  return useQuery({
    queryKey: [...channelKeys.channelMarkets(channelId), options],
    queryFn: () => channelService.getChannelMarkets(channelId, options),
    enabled: !!channelId,
    select: mapMarketsResponseToMarketsProps,
  });
};
