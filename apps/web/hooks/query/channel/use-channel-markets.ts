import { channelService } from '@/lib/api/channel/channel.service';
import type { GetMarketsRequestOptions } from '@/lib/api/market/market.schema.server';
import { useQuery } from '@tanstack/react-query';
import { mapMarketsResponseToMarketsProps } from '../market/market.mapper';
import { channelKeys } from '../query-keys';

export const useChannelMarkets = (channelId: string, options?: GetMarketsRequestOptions) => {
  return useQuery({
    queryKey: [...channelKeys.channelMarkets(channelId), options],
    enabled: !!channelId,
    queryFn: async () => {
      const markets = await channelService.getChannelMarkets(channelId, options);
      return mapMarketsResponseToMarketsProps(markets);
    },
  });
};
