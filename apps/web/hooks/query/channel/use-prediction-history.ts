import { useQuery } from '@tanstack/react-query';
import { channelKeys } from '../query-keys';
import { channelService } from '@/lib/api/channel/channel.service';

export const usePredictionHistory = (options?: { page?: number; limit?: number }) => {
  return useQuery({
    queryKey: [...channelKeys.predictionHistory, options],
    queryFn: () => channelService.getPredictionHistory(options),
  });
};
