import { useQuery } from '@tanstack/react-query';
import { channelKeys } from '../query-keys';
import { channelService } from '@/lib/api/channel/channel.service';

export const useActivePredictions = (options?: { page?: number; limit?: number }) => {
  return useQuery({
    queryKey: [...channelKeys.activePredictions, options],
    queryFn: () => channelService.getActivePredictions(options),
  });
};
