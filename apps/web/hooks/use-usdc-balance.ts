import { env } from '@/lib/env';
import { Address, formatUnits } from 'viem';
import { useReadContract } from 'wagmi';
import { useGlobalStore } from '@/store/global.store';
import { formatUsdc } from '@/lib/format';
import { shortenNumber } from '@/lib/utils';

const USDC_DECIMALS = 6;
const erc20ABI = [
  {
    name: 'balanceOf',
    type: 'function',
    stateMutability: 'view',
    inputs: [{ name: 'account', type: 'address' }],
    outputs: [{ name: 'balance', type: 'uint256' }],
  },
] as const;

export default function useUSDCBalance(address?: string) {
  const {
    data: balance,
    isError,
    isLoading,
    refetch,
  } = useReadContract({
    address: env.NEXT_PUBLIC_USDC_ADDRESS as Address,
    abi: erc20ABI,
    functionName: 'balanceOf',
    args: address ? [address as Address] : undefined,
    query: {
      enabled: !!address,
    },
  });

  const formattedBalance = balance ? formatUnits(balance, USDC_DECIMALS) : '0';
  console.log('formattedBalance', formattedBalance);

  return {
    balance: formattedBalance,
    rawBalance: balance,
    isLoading,
    isError,
    refetch,
  };
}

export function useMyUSDCBalance() {
  const { safeSmartAccount } = useGlobalStore();

  const {
    data: balance,
    isError,
    isLoading,
    refetch,
  } = useReadContract({
    address: env.NEXT_PUBLIC_USDC_ADDRESS as Address,
    abi: erc20ABI,
    functionName: 'balanceOf',
    args: safeSmartAccount?.address ? [safeSmartAccount.address as Address] : undefined,
    query: {
      enabled: !!safeSmartAccount?.address,
    },
  });

  const formattedBalance = balance ? formatUsdc(balance) : '0';

  return {
    balance: formattedBalance,
    balanceShortened: shortenNumber(formattedBalance),
    rawBalance: balance,
    isLoading,
    isError,
    refetch,
  };
}
