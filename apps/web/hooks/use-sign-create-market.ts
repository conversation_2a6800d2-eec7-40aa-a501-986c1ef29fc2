import { env } from '@/lib/env';
import { ClientError } from '@/lib/error';
import { useGlobalStore } from '@/store/global.store';
import { Address } from 'viem';
import { useChainId } from 'wagmi';
import { z } from 'zod';

const domain = (chainId: number) => ({
  name: 'PredictGo',
  version: '1',
  verifyingContract: env.NEXT_PUBLIC_PREDICTGO_ADDRESS as Address,
  chainId,
});

const CreateMarket = {
  types: {
    CreateMarket: [
      { name: 'maker', type: 'address' },
      { name: 'channelId', type: 'address' },
      { name: 'title', type: 'string' },
      { name: 'description', type: 'string' },
      { name: 'predictionDeadline', type: 'uint256' },
      { name: 'resultConfirmDeadline', type: 'uint256' },
      { name: 'disputedPeriod', type: 'string' },
      { name: 'category', type: 'string' },
      { name: 'collateralAmount', type: 'uint256' },
      { name: 'outcomes', type: 'string[]' },
      { name: 'tags', type: 'string[]' },
      { name: 'referenceURL', type: 'string' },
      { name: 'validationId', type: 'string' },
    ],
  },
  schema: z.object({
    maker: z.string(),
    channelId: z.string(),
    title: z.string(),
    description: z.string(),
    predictionDeadline: z.number(),
    resultConfirmDeadline: z.number(),
    disputedPeriod: z.string(),
    category: z.string(),
    collateralAmount: z.bigint(),
    outcomes: z.array(z.string()),
    tags: z.array(z.string()),
    validationId: z.string(),
    referenceURL: z.string(),
  }),
};

export type CreateMarketMessage = z.infer<typeof CreateMarket.schema>;

export default function useSignCreateMarket() {
  const chainId = useChainId();
  const { safeSmartAccount } = useGlobalStore();

  const signCreateMarket = async (message: CreateMarketMessage) => {
    if (!safeSmartAccount) {
      throw ClientError.fromQuery('Safe smart account not found', {
        queryName: 'useSignCreateMarket.signCreateMarket',
      });
    }
    const typedMessage = CreateMarket.schema.parse(message);
    const signature = await safeSmartAccount.signTypedData({
      domain: domain(chainId),
      types: CreateMarket.types,
      primaryType: 'CreateMarket',
      message: typedMessage,
    });

    return signature;
  };

  return { signCreateMarket };
}
