import { useState, useEffect, useCallback } from 'react';

export interface TrackingConsentState {
  googleAnalytics: boolean;
  microsoftClarity: boolean;
  timestamp: number;
}

const STORAGE_KEY = 'tracking-consent';
const ONE_DAY_MS = 24 * 60 * 60 * 1000;

export const useTrackingConsent = () => {
  const [consentState, setConsentState] = useState<TrackingConsentState | null>(null);
  const [shouldShowDialog, setShouldShowDialog] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  // 로컬 스토리지에서 동의 상태 불러오기
  const loadConsentState = useCallback(() => {
    try {
      const stored = localStorage.getItem(STORAGE_KEY);
      if (stored) {
        const parsed: TrackingConsentState = JSON.parse(stored);

        // 하루가 지났는지 확인
        const now = Date.now();
        const timeDiff = now - (parsed.timestamp || 0);

        if (timeDiff < ONE_DAY_MS && (parsed.googleAnalytics || parsed.microsoftClarity)) {
          // 하루가 지나지 않았고 하나 이상 동의한 경우
          setConsentState(parsed);
          setShouldShowDialog(false);
        } else if (timeDiff >= ONE_DAY_MS && !parsed.googleAnalytics && !parsed.microsoftClarity) {
          // 하루가 지났고 이전에 거부한 경우
          setShouldShowDialog(true);
        } else if (!parsed.googleAnalytics && !parsed.microsoftClarity) {
          // 거부한 경우이지만 하루가 지나지 않은 경우
          setShouldShowDialog(false);
        } else {
          // 이미 동의한 경우
          setConsentState(parsed);
          setShouldShowDialog(false);
        }
      } else {
        // 첫 방문자
        setShouldShowDialog(true);
      }
    } catch (error) {
      console.error('Error loading consent state:', error);
      setShouldShowDialog(true);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 동의 상태 저장
  const saveConsentState = useCallback((state: TrackingConsentState) => {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(state));
      setConsentState(state);
    } catch (error) {
      console.error('Error saving consent state:', error);
    }
  }, []);

  // 동의 처리
  const acceptConsent = useCallback(
    (analytics = true, clarity = true) => {
      const newState: TrackingConsentState = {
        googleAnalytics: analytics,
        microsoftClarity: clarity,
        timestamp: Date.now(),
      };

      saveConsentState(newState);
      setShouldShowDialog(false);
    },
    [saveConsentState]
  );

  // 거부 처리
  const rejectConsent = useCallback(() => {
    const newState: TrackingConsentState = {
      googleAnalytics: false,
      microsoftClarity: false,
      timestamp: Date.now(),
    };

    saveConsentState(newState);
    setShouldShowDialog(false);
  }, [saveConsentState]);

  // 컴포넌트 마운트 시 상태 로드
  useEffect(() => {
    loadConsentState();
  }, [loadConsentState]);

  return {
    consentState,
    shouldShowDialog,
    isLoading,
    acceptConsent,
    rejectConsent,
  };
};
