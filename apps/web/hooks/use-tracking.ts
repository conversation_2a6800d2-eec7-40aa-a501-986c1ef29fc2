import { useEffect, useCallback } from 'react';
import { useTrackingConsent } from './use-tracking-consent';
import { trackingService } from '@/lib/tracking/tracking.service';
import { AnalyticsEvent } from '@/lib/tracking/types';
import { env } from '@/lib/env';

export const useTracking = () => {
  const { consentState } = useTrackingConsent();

  // 트래킹 서비스 초기화
  useEffect(() => {
    if (!consentState) return;

    const shouldInitializeGA = consentState.googleAnalytics && env.NEXT_PUBLIC_GA_MEASUREMENT_ID;
    const shouldInitializeClarity =
      consentState.microsoftClarity && env.NEXT_PUBLIC_CLARITY_PROJECT_ID;

    if (shouldInitializeGA || shouldInitializeClarity) {
      trackingService.initialize({
        gaId: shouldInitializeGA ? env.NEXT_PUBLIC_GA_MEASUREMENT_ID : undefined,
        clarityProjectId: shouldInitializeClarity ? env.NEXT_PUBLIC_CLARITY_PROJECT_ID : undefined,
        enabled: true,
      });
    }
  }, [consentState]);

  // 공통 트래킹 함수
  const track = useCallback(
    (event: AnalyticsEvent) => {
      if (!consentState || (!consentState.googleAnalytics && !consentState.microsoftClarity)) {
        return;
      }
      trackingService.track(event);
    },
    [consentState]
  );

  // 사용자 식별
  const identify = useCallback(
    (userId: string, properties?: Record<string, any>) => {
      if (!consentState || (!consentState.googleAnalytics && !consentState.microsoftClarity)) {
        return;
      }
      trackingService.identify(userId, properties);
    },
    [consentState]
  );

  // 사용자 속성 설정
  const setUserProperties = useCallback(
    (properties: Record<string, any>) => {
      if (!consentState || (!consentState.googleAnalytics && !consentState.microsoftClarity)) {
        return;
      }
      trackingService.setUserProperties(properties);
    },
    [consentState]
  );

  // 편의 메서드들
  const trackPurchase = useCallback(
    (data: {
      transactionId: string;
      marketId: string;
      outcomeId: string;
      marketTitle?: string;
      category?: string;
      amount: number;
      currency: string;
      price: number;
      quantity?: number;
      userId?: string;
      affiliation?: string;
      coupon?: string;
    }) => {
      if (!consentState || (!consentState.googleAnalytics && !consentState.microsoftClarity)) {
        return;
      }
      trackingService.trackPurchase(data);
    },
    [consentState]
  );

  const trackMarketView = useCallback(
    (data: { marketId: string; marketTitle?: string; category?: string; userId?: string }) => {
      if (!consentState || (!consentState.googleAnalytics && !consentState.microsoftClarity)) {
        return;
      }
      trackingService.trackMarketView(data);
    },
    [consentState]
  );

  const trackWalletConnect = useCallback(
    (data: { walletType: string; userId?: string }) => {
      if (!consentState || (!consentState.googleAnalytics && !consentState.microsoftClarity)) {
        return;
      }
      trackingService.trackWalletConnect(data);
    },
    [consentState]
  );

  const trackUserLogin = useCallback(
    (data: { method: string; userId?: string }) => {
      if (!consentState || (!consentState.googleAnalytics && !consentState.microsoftClarity)) {
        return;
      }
      trackingService.trackUserLogin(data);
    },
    [consentState]
  );

  return {
    // 동의 상태
    consentState,
    isTrackingEnabled:
      !!consentState && (consentState.googleAnalytics || consentState.microsoftClarity),

    // 기본 트래킹 함수
    track,
    identify,
    setUserProperties,

    // 편의 메서드
    trackPurchase,
    trackMarketView,
    trackWalletConnect,
    trackUserLogin,
  };
};
