import { useEffect, useState } from 'react';

export interface BuildInfo {
  buildTime: string;
  buildTimestamp: number;
  buildDate: string;
  version: string;
  environment: string;
  gitCommit: string;
  gitBranch: string;
}

export function useBuildInfo() {
  const [buildInfo, setBuildInfo] = useState<BuildInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchBuildInfo = async () => {
      try {
        const response = await fetch('/build-info.json');
        if (!response.ok) {
          throw new Error('Failed to fetch build info');
        }
        const data = await response.json();
        setBuildInfo(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
        console.warn('Failed to load build info:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchBuildInfo();
  }, []);

  return { buildInfo, loading, error };
}
