import { env } from '@/lib/env';
import { ClientError } from '@/lib/error';
import { useGlobalStore } from '@/store/global.store';
import { Address } from 'viem';
import { useChainId } from 'wagmi';
import { z } from 'zod';

const domain = (chainId: number) => ({
  name: 'PredictGo',
  version: '1',
  verifyingContract: env.NEXT_PUBLIC_PREDICTGO_ADDRESS as Address,
  chainId,
});

const Propose = {
  types: {
    Propose: [
      { name: 'proposer', type: 'address' },
      { name: 'marketId', type: 'bytes32' },
      { name: 'outcome', type: 'string' },
    ],
  },
  schema: z.object({
    proposer: z.string(),
    marketId: z.string(),
    outcome: z.string(),
  }),
};

export type ProposeMessage = z.infer<typeof Propose.schema>;

export default function useSignPropose() {
  const chainId = useChainId();
  const { safeSmartAccount } = useGlobalStore();

  const signPropose = async (message: ProposeMessage) => {
    if (!safeSmartAccount) {
      throw ClientError.fromQuery('Safe smart account not found', {
        queryName: 'useSignPropose.signPropose',
      });
    }
    const typedMessage = Propose.schema.parse(message);
    const signature = await safeSmartAccount.signTypedData({
      domain: domain(chainId),
      types: Propose.types,
      primaryType: 'Propose',
      message: typedMessage,
    });

    return signature;
  };

  return { signPropose };
}
