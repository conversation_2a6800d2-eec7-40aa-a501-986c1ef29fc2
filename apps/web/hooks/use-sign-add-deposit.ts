import { env } from '@/lib/env';
import { useGlobalStore } from '@/store/global.store';
import { ClientError } from '@/lib/error';

import { Address } from 'viem';
import { z } from 'zod';
import { useChainId } from 'wagmi';

const domain = (chainId: number) => ({
  name: 'PredictGo',
  version: '1',
  verifyingContract: env.NEXT_PUBLIC_PREDICTGO_ADDRESS as Address,
  chainId,
});

const DepositMarketCollateral = {
  types: {
    DepositMarketCollateral: [
      { name: 'depositor', type: 'address' },
      { name: 'marketId', type: 'bytes32' },
      { name: 'amount', type: 'uint256' },
    ],
  },
  schema: z.object({
    depositor: z.string(),
    marketId: z.string(),
    amount: z.bigint(),
  }),
};

export type DepositMarketCollateralMessage = z.infer<typeof DepositMarketCollateral.schema>;

export default function useSignAddDeposit() {
  const chainId = useChainId();
  const { safeSmartAccount } = useGlobalStore();

  const signAddDeposit = async (message: DepositMarketCollateralMessage) => {
    if (!safeSmartAccount) {
      throw ClientError.fromQuery('Safe smart account not found', {
        queryName: 'useSignCreateMarket.signCreateMarket',
      });
    }
    const typedMessage = DepositMarketCollateral.schema.parse(message);
    const signature = await safeSmartAccount.signTypedData({
      domain: domain(chainId),
      types: DepositMarketCollateral.types,
      primaryType: 'DepositMarketCollateral',
      message: typedMessage,
    });
    return signature;
  };

  return { signAddDeposit };
}
