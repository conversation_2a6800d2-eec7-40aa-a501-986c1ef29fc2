enum ClientErrorCode {
  QUERY_ERROR = 'QUERY_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

export class ClientError extends Error {
  constructor(
    message: string,
    public code: ClientErrorCode,
    public details: Record<string, unknown> = {}
  ) {
    super(message);
    this.name = 'ClientError';
    this.code = code;
    this.details = details;
  }

  to<PERSON><PERSON>() {
    return {
      message: this.message,
      code: this.code,
      details: this.details,
    };
  }

  static from<PERSON>uery(message: string, details: Record<string, unknown> = {}) {
    return new ClientError(message, ClientErrorCode.QUERY_ERROR, details);
  }
}
