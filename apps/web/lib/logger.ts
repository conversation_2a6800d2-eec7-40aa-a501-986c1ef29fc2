import { env } from './env';

export type LogLevel = 'debug' | 'info' | 'warn' | 'error';

export interface LogContext {
  endpoint?: string;
  method?: string;
  statusCode?: number;
  requestData?: unknown;
  responseData?: unknown;
  duration?: number;
  userAgent?: string;
  userId?: string;
  sessionId?: string;
  error?: unknown;
}

export interface LogEntry {
  level: LogLevel;
  message: string;
  timestamp: string;
  context?: LogContext;
}

class Logger {
  private isDevelopment = env.NEXT_PUBLIC_ENV === 'local';
  private isProduction = env.NEXT_PUBLIC_ENV === 'prod';

  private formatTimestamp(): string {
    return new Date().toISOString();
  }

  private createLogEntry(level: LogLevel, message: string, context?: LogContext): LogEntry {
    return {
      level,
      message,
      timestamp: this.formatTimestamp(),
      context,
    };
  }

  private shouldLog(level: LogLevel): boolean {
    if (this.isDevelopment) return true;
    if (this.isProduction) {
      return level === 'warn' || level === 'error';
    }
    return true;
  }

  private formatForConsole(entry: LogEntry): void {
    const { level, message, timestamp, context } = entry;

    const styles = {
      debug: 'color: #888',
      info: 'color: #2196F3',
      warn: 'color: #FF9800',
      error: 'color: #F44336; font-weight: bold',
    };

    const prefix = `[${timestamp}] [${level.toUpperCase()}]`;

    if (context) {
      console.groupCollapsed(`%c${prefix} ${message}`, styles[level]);
      console.table(context);
      console.groupEnd();
    } else {
      console.log(`%c${prefix} ${message}`, styles[level]);
    }
  }

  private sendToExternalService(entry: LogEntry): void {
    if (!this.isProduction) return;

    // TODO: Send to external logging service (e.g., Sentry, LogRocket, etc.)
    // fetch('/api/logs', {
    //   method: 'POST',
    //   headers: { 'Content-Type': 'application/json' },
    //   body: JSON.stringify(entry),
    // }).catch(() => {
    //   // Silently fail to avoid logging loops
    // });
  }

  private log(level: LogLevel, message: string, context?: LogContext): void {
    if (!this.shouldLog(level)) return;

    const entry = this.createLogEntry(level, message, context);

    this.formatForConsole(entry);
    this.sendToExternalService(entry);
  }

  debug(message: string, context?: LogContext): void {
    this.log('debug', message, context);
  }

  info(message: string, context?: LogContext): void {
    this.log('info', message, context);
  }

  warn(message: string, context?: LogContext): void {
    this.log('warn', message, context);
  }

  error(message: string, context?: LogContext): void {
    this.log('error', message, context);
  }

  // Specific methods for common logging scenarios
  apiRequest(method: string, endpoint: string, requestData?: unknown): void {
    this.info(`API Request: ${method} ${endpoint}`, {
      method,
      endpoint,
      requestData,
      userAgent: typeof window !== 'undefined' ? window.navigator.userAgent : undefined,
    });
  }

  apiResponse(
    method: string,
    endpoint: string,
    statusCode: number,
    duration: number,
    responseData?: unknown
  ): void {
    const level = statusCode >= 400 ? 'error' : statusCode >= 300 ? 'warn' : 'info';
    this.log(level, `API Response: ${method} ${endpoint} - ${statusCode}`, {
      method,
      endpoint,
      statusCode,
      duration,
      responseData,
    });
  }

  apiError(method: string, endpoint: string, error: unknown, requestData?: unknown): void {
    this.error(`API Error: ${method} ${endpoint}`, {
      method,
      endpoint,
      requestData,
      error:
        error instanceof Error
          ? {
              name: error.name,
              message: error.message,
              stack: error.stack,
            }
          : error,
    });
  }

  zodError(schema: string, data: unknown, error: unknown): void {
    this.error(`Zod Validation Error: ${schema}`, {
      error:
        error instanceof Error
          ? {
              name: error.name,
              message: error.message,
            }
          : error,
      responseData: data,
    });
  }

  httpError(statusCode: number, method: string, endpoint: string, error: unknown): void {
    const message = `HTTP ${statusCode} Error: ${method} ${endpoint}`;
    this.error(message, {
      statusCode,
      method,
      endpoint,
      error:
        error instanceof Error
          ? {
              name: error.name,
              message: error.message,
            }
          : error,
    });
  }

  userAction(action: string, context?: Record<string, unknown>): void {
    this.info(`User Action: ${action}`, context);
  }

  performance(operation: string, duration: number, context?: Record<string, unknown>): void {
    const level = duration > 1000 ? 'warn' : 'info';
    this.log(level, `Performance: ${operation} took ${duration}ms`, {
      duration,
      ...context,
    });
  }
}

export const logger = new Logger();
