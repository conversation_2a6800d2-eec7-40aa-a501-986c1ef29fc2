/**
 * 스타일링 관련 유틸리티 함수들
 */

/**
 * order 값에 따른 그래프 색상 CSS 변수를 반환합니다.
 * @param order - 0부터 10까지의 순서 값
 * @param alpha - 투명도 값 (0-100). 제공되면 color-mix를 사용한 투명도 적용된 색상 반환
 * @returns CSS 변수 또는 color-mix 표현식 (예: 'var(--color-graph-1)' 또는 'color-mix(in srgb, var(--color-graph-1) 20%, transparent)')
 */
export function getGraphVar(order: number, alpha?: number): string {
  const graphColorVariables = [
    'var(--color-graph-1)', // 0: #f19595ff (분홍)
    'var(--color-graph-2)', // 1: #f1c695ff (주황)
    'var(--color-graph-3)', // 2: #f1eb8dff (노랑)
    'var(--color-graph-4)', // 3: #c9f18dff (연두)
    'var(--color-graph-5)', // 4: #a9e9a3ff (초록)
    'var(--color-graph-6)', // 5: #a3e9dfff (민트)
    'var(--color-graph-7)', // 6: #b5d0f1ff (파랑)
    'var(--color-graph-8)', // 7: #bcb5f1ff (보라)
    'var(--color-graph-9)', // 8: #ebc2efff (자주)
    'var(--color-graph-10)', // 9: #ffc3d7ff (핑크)
    'var(--color-graph-11)', // 10: #dad2d5ff (회색)
  ];

  const colorVariable = graphColorVariables[order] || 'var(--color-graph-1)';

  // alpha 값이 제공되면 color-mix를 사용한 투명도 적용
  if (alpha !== undefined) {
    return `color-mix(in srgb, ${colorVariable} ${alpha}%, transparent)`;
  }

  return colorVariable;
}
