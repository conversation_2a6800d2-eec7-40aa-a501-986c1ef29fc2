import { Variants, Transition } from 'motion/react';

// 카드 전환 시 사용할 공통 트랜지션
export const cardSwitchTransition: Transition = {
  duration: 0.2, // 사용자가 조정한 값
  ease: 'easeInOut',
};

// PredictionMarketCard와 PredictionCardQuickMode 간의 전환 애니메이션 Variants
export const cardSwitchVariants: Variants = {
  // Quick Mode 등장 애니메이션
  quickModeInitial: {
    opacity: 0,
    y: 10, // 사용자가 조정한 값
  },
  quickModeEnter: {
    opacity: 1,
    y: 0,
    transition: cardSwitchTransition,
  },
  quickModeExit: {
    opacity: 0,
    y: -10, // 사용자가 조정한 값
    transition: cardSwitchTransition,
  },

  // Market Card 등장 애니메이션
  marketCardInitial: {
    opacity: 0,
    y: -10, // 사용자가 조정한 값
  },
  marketCardEnter: {
    opacity: 1,
    y: 0,
    transition: cardSwitchTransition,
  },
  marketCardExit: {
    opacity: 0,
    y: 10, // 사용자가 조정한 값
    transition: cardSwitchTransition,
  },
};

// 기타 일반적인 애니메이션 (예시)
export const defaultFadeInTransition: Transition = {
  duration: 0.3,
  ease: 'easeInOut',
};

export const fadeInUpVariants: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0, transition: defaultFadeInTransition },
};
