import { z } from 'zod';

const loadedEnv = {
  NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
  NEXT_PUBLIC_APP_KIT_PROJECT_ID: process.env.NEXT_PUBLIC_APP_KIT_PROJECT_ID,
  NEXT_PUBLIC_ENV: process.env.NEXT_PUBLIC_ENV,
  NEXT_PUBLIC_USDC_ADDRESS: process.env.NEXT_PUBLIC_USDC_ADDRESS,
  NEXT_PUBLIC_PREDICTGO_ADDRESS: process.env.NEXT_PUBLIC_PREDICTGO_ADDRESS,
  NEXT_PUBLIC_GA_MEASUREMENT_ID: process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID,
  NEXT_PUBLIC_CLARITY_PROJECT_ID: process.env.NEXT_PUBLIC_CLARITY_PROJECT_ID,
};

const envSchema = z.object({
  NEXT_PUBLIC_API_URL: z.string().url(),
  NEXT_PUBLIC_APP_KIT_PROJECT_ID: z.string(),
  NEXT_PUBLIC_ENV: z.string(),
  // NEXT_PUBLIC_ENV: z.enum(['local', 'dq', 'prod', 'stg']),
  NEXT_PUBLIC_USDC_ADDRESS: z.string(),
  NEXT_PUBLIC_PREDICTGO_ADDRESS: z.string(),
  NEXT_PUBLIC_GA_MEASUREMENT_ID: z.string().optional(),
  NEXT_PUBLIC_CLARITY_PROJECT_ID: z.string().optional(),
});

export type Env = z.infer<typeof envSchema>;

function validateEnv(): Env {
  try {
    return envSchema.parse(loadedEnv);
  } catch (error) {
    if (error instanceof z.ZodError) {
      const missingVars = error.errors.map(err => `${err.path.join('.')}: ${err.message}`);
      throw new Error(`❌ Invalid environment variables:\n${missingVars.join('\n')}`);
    }
    throw error;
  }
}
export const env = validateEnv();

export const isLocal = env.NEXT_PUBLIC_ENV === 'local';
export const isDq = env.NEXT_PUBLIC_ENV === 'dq';
export const isProd = env.NEXT_PUBLIC_ENV === 'prod';
export const isStg = env.NEXT_PUBLIC_ENV === 'stg';
export const isServer = () => typeof window === 'undefined';
export const isClient = () => typeof window !== 'undefined';
