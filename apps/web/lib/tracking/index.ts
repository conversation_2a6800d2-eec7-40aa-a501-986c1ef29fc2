// 트래킹 서비스 메인 진입점
export { trackingService, TrackingService } from './tracking.service';

// 디버그 로거
export { trackingLogger, TrackingLogger } from './logger';

// 타입 정의
export type {
  TrackingEvent,
  EcommerceItem,
  PurchaseEvent,
  ViewItemEvent,
  AddToWishlistEvent,
  BeginCheckoutEvent,
  RefundEvent,
  ViewPromotionEvent,
  SelectPromotionEvent,
  MarketEvent,
  UserEvent,
  AnalyticsEvent,
  TrackingAdapter,
  TrackingConfig,
} from './types';

// 어댑터들
export { GoogleAnalyticsAdapter } from './adapters/google-analytics.adapter';
export { MicrosoftClarityAdapter } from './adapters/microsoft-clarity.adapter';
