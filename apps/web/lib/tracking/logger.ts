import { env } from '@/lib/env';

export class TrackingLogger {
  private static instance: TrackingLogger;
  private debugEnabled: boolean;

  private constructor() {
    // 환경 변수에서 디버그 플래그 확인
    this.debugEnabled =
      env.NEXT_PUBLIC_DEBUG === 'true' ||
      env.NEXT_PUBLIC_TRACKING_DEBUG === '1' ||
      env.NEXT_PUBLIC_ENV === 'local';
  }

  public static getInstance(): TrackingLogger {
    if (!TrackingLogger.instance) {
      TrackingLogger.instance = new TrackingLogger();
    }
    return TrackingLogger.instance;
  }

  public debug(message: string, data?: any): void {
    // 런타임 디버그 모드도 확인
    const runtimeDebug = typeof window !== 'undefined' && (window as any).TRACKING_DEBUG;
    if (this.debugEnabled || runtimeDebug) {
      if (data) {
        console.log(`[Tracking Debug] ${message}`, data);
      } else {
        console.log(`[Tracking Debug] ${message}`);
      }
    }
  }

  public info(message: string, data?: any): void {
    const runtimeDebug = typeof window !== 'undefined' && (window as any).TRACKING_DEBUG;
    if (this.debugEnabled || runtimeDebug) {
      if (data) {
        console.info(`[Tracking Info] ${message}`, data);
      } else {
        console.info(`[Tracking Info] ${message}`);
      }
    }
  }

  public warn(message: string, data?: any): void {
    const runtimeDebug = typeof window !== 'undefined' && (window as any).TRACKING_DEBUG;
    if (this.debugEnabled || runtimeDebug) {
      if (data) {
        console.warn(`[Tracking Warning] ${message}`, data);
      } else {
        console.warn(`[Tracking Warning] ${message}`);
      }
    }
  }

  public error(message: string, error?: any): void {
    // 에러는 항상 출력 (디버그 모드와 무관)
    if (error) {
      console.error(`[Tracking Error] ${message}`, error);
    } else {
      console.error(`[Tracking Error] ${message}`);
    }
  }

  public setDebugMode(enabled: boolean): void {
    this.debugEnabled = enabled;
  }

  public isDebugEnabled(): boolean {
    return this.debugEnabled;
  }
}

// 전역 인스턴스 내보내기
export const trackingLogger = TrackingLogger.getInstance();
