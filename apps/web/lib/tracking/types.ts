export interface TrackingEvent {
  event: string;
  properties?: Record<string, any>;
  userId?: string;
  sessionId?: string;
}

// GA4 전자상거래 표준 아이템 인터페이스
export interface EcommerceItem {
  item_id: string; // marketId + outcomeId 조합
  item_name: string; // 예측 결과명 (예: "Yes" 또는 "No")
  item_category?: string; // 마켓 카테고리
  item_category2?: string; // 서브카테고리
  item_brand?: string; // 브랜드 (예: "PredictGO")
  price: number; // 단가
  quantity: number; // 수량 (보통 1)
  currency: string; // 통화
}

// GA4 표준 구매 이벤트
export interface PurchaseEvent {
  event: 'purchase';
  properties: {
    transaction_id: string; // 거래 ID (필수)
    value: number; // 총 거래 금액 (필수)
    currency: string; // 통화 (필수)
    items: EcommerceItem[]; // 아이템 배열 (필수)

    // 추가 매개변수 (선택사항)
    affiliation?: string; // 제휴사 정보
    coupon?: string; // 쿠폰 코드
    shipping?: number; // 배송비
    tax?: number; // 세금

    // 커스텀 매개변수 (기존 호환성)
    marketId: string;
    outcomeId: string;
    timestamp: number;
  };
  userId?: string;
}

// 아이템 조회 이벤트 (마켓 조회)
export interface ViewItemEvent {
  event: 'view_item';
  properties: {
    currency: string;
    value: number;
    items: EcommerceItem[];

    // 커스텀 매개변수
    marketId: string;
    marketTitle?: string;
    category?: string;
    timestamp: number;
  };
  userId?: string;
}

// 관심 목록 추가 (장바구니 대신)
export interface AddToWishlistEvent {
  event: 'add_to_wishlist';
  properties: {
    currency: string;
    value: number;
    items: EcommerceItem[];

    // 커스텀 매개변수
    marketId: string;
    outcomeId: string;
    timestamp: number;
  };
  userId?: string;
}

// 구매 시작 이벤트
export interface BeginCheckoutEvent {
  event: 'begin_checkout';
  properties: {
    currency: string;
    value: number;
    items: EcommerceItem[];

    // 커스텀 매개변수
    marketId: string;
    outcomeId: string;
    timestamp: number;
  };
  userId?: string;
}

// 환불 이벤트
export interface RefundEvent {
  event: 'refund';
  properties: {
    transaction_id: string;
    value: number;
    currency: string;
    items: EcommerceItem[];

    // 커스텀 매개변수
    marketId: string;
    timestamp: number;
  };
  userId?: string;
}

// 프로모션 조회 이벤트
export interface ViewPromotionEvent {
  event: 'view_promotion';
  properties: {
    promotion_id: string;
    promotion_name: string;
    creative_name?: string;
    creative_slot?: string;
    location_id?: string;
    items?: EcommerceItem[];
    timestamp: number;
  };
  userId?: string;
}

// 프로모션 선택 이벤트
export interface SelectPromotionEvent {
  event: 'select_promotion';
  properties: {
    promotion_id: string;
    promotion_name: string;
    creative_name?: string;
    creative_slot?: string;
    location_id?: string;
    items?: EcommerceItem[];
    timestamp: number;
  };
  userId?: string;
}

export interface MarketEvent {
  event: 'market_view' | 'market_share' | 'market_favorite';
  properties: {
    marketId: string;
    marketTitle?: string;
    category?: string;
    timestamp: number;
  };
  userId?: string;
}

export interface UserEvent {
  event: 'user_login' | 'user_logout' | 'user_register' | 'wallet_connect' | 'wallet_disconnect';
  properties: {
    method?: string;
    walletType?: string;
    timestamp: number;
  };
  userId?: string;
}

export type AnalyticsEvent =
  | PurchaseEvent
  | ViewItemEvent
  | AddToWishlistEvent
  | BeginCheckoutEvent
  | RefundEvent
  | ViewPromotionEvent
  | SelectPromotionEvent
  | MarketEvent
  | UserEvent
  | TrackingEvent;

export interface TrackingAdapter {
  initialize(): void;
  track(event: AnalyticsEvent): void;
  identify(userId: string, properties?: Record<string, any>): void;
  setUserProperties(properties: Record<string, any>): void;
  isEnabled(): boolean;
}

export interface TrackingConfig {
  enabled: boolean;
  adapters: TrackingAdapter[];
}
