import { TrackingAdapter, AnalyticsEvent } from '../types';
import { trackingLogger } from '../logger';

declare global {
  interface Window {
    clarity: (...args: any[]) => void;
  }
}

export class MicrosoftClarityAdapter implements TrackingAdapter {
  private projectId: string;
  private isInitialized = false;

  constructor(projectId: string) {
    this.projectId = projectId;
  }

  initialize(): void {
    if (this.isInitialized || typeof window === 'undefined' || !this.projectId) {
      return;
    }

    // Microsoft Clarity가 이미 로드되어 있는지 확인
    if (typeof window.clarity === 'function') {
      this.isInitialized = true;
      return;
    }

    this.isInitialized = true;
  }

  track(event: AnalyticsEvent): void {
    if (
      !this.isEnabled() ||
      typeof window === 'undefined' ||
      typeof window.clarity !== 'function'
    ) {
      return;
    }

    const { event: eventName, properties, userId } = event;

    // Microsoft Clarity 커스텀 이벤트 전송
    try {
      window.clarity('event', eventName);

      // 속성이 있는 경우 태그로 설정
      if (properties) {
        Object.entries(properties).forEach(([key, value]) => {
          window.clarity('set', key, String(value));
        });
      }

      if (userId) {
        window.clarity('identify', userId);
      }

      trackingLogger.debug('Clarity event tracked', { eventName, properties });
    } catch (error) {
      trackingLogger.error('Clarity error tracking event', error);
    }
  }

  identify(userId: string, properties?: Record<string, any>): void {
    if (
      !this.isEnabled() ||
      typeof window === 'undefined' ||
      typeof window.clarity !== 'function'
    ) {
      return;
    }

    try {
      window.clarity('identify', userId);

      if (properties) {
        Object.entries(properties).forEach(([key, value]) => {
          window.clarity('set', key, String(value));
        });
      }

      trackingLogger.debug('Clarity user identified', { userId, properties });
    } catch (error) {
      trackingLogger.error('Clarity error identifying user', error);
    }
  }

  setUserProperties(properties: Record<string, any>): void {
    if (
      !this.isEnabled() ||
      typeof window === 'undefined' ||
      typeof window.clarity !== 'function'
    ) {
      return;
    }

    try {
      Object.entries(properties).forEach(([key, value]) => {
        window.clarity('set', key, String(value));
      });

      trackingLogger.debug('Clarity user properties set', { properties });
    } catch (error) {
      trackingLogger.error('Clarity error setting user properties', error);
    }
  }

  isEnabled(): boolean {
    return this.isInitialized && !!this.projectId;
  }
}
