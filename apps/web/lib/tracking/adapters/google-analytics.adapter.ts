import { TrackingAdapter, AnalyticsEvent } from '../types';
import { trackingLogger } from '../logger';

declare global {
  interface Window {
    gtag: (...args: any[]) => void;
  }
}

export class GoogleAnalyticsAdapter implements TrackingAdapter {
  private measurementId: string;
  private isInitialized = false;

  constructor(measurementId: string) {
    this.measurementId = measurementId;
  }

  initialize(): void {
    if (this.isInitialized || typeof window === 'undefined' || !this.measurementId) {
      return;
    }

    // Google Analytics가 이미 로드되어 있는지 확인
    if (typeof window.gtag === 'function') {
      this.isInitialized = true;
      return;
    }

    this.isInitialized = true;
  }

  track(event: AnalyticsEvent): void {
    if (!this.isEnabled()) {
      return;
    }

    if (typeof window === 'undefined') {
      return;
    }

    if (!window.gtag) {
      return;
    }

    const { event: eventName, properties, userId } = event;

    // GA4 전자상거래 이벤트 처리
    if (this.isEcommerceEvent(eventName)) {
      this.trackEcommerceEvent(eventName, properties, userId);
    } else {
      // 일반 이벤트 처리
      this.trackGeneralEvent(eventName, properties, userId);
    }

    trackingLogger.debug('GA event tracked', { eventName, properties });
  }

  private isEcommerceEvent(eventName: string): boolean {
    const ecommerceEvents = [
      'purchase',
      'refund',
      'view_item',
      'add_to_wishlist',
      'begin_checkout',
      'view_promotion',
      'select_promotion',
    ];
    return ecommerceEvents.includes(eventName);
  }

  private trackEcommerceEvent(eventName: string, properties: any, userId?: string): void {
    // GA4 전자상거래 이벤트는 특별한 구조를 요구함
    const gaEvent: Record<string, any> = {};

    // 공통 매개변수
    if (properties.currency) gaEvent.currency = properties.currency;
    if (properties.value !== undefined) gaEvent.value = properties.value;
    if (properties.items) gaEvent.items = properties.items;

    // 거래 관련 매개변수
    if (properties.transaction_id) gaEvent.transaction_id = properties.transaction_id;
    if (properties.affiliation) gaEvent.affiliation = properties.affiliation;
    if (properties.coupon) gaEvent.coupon = properties.coupon;
    if (properties.shipping) gaEvent.shipping = properties.shipping;
    if (properties.tax) gaEvent.tax = properties.tax;

    // 프로모션 관련 매개변수
    if (properties.promotion_id) gaEvent.promotion_id = properties.promotion_id;
    if (properties.promotion_name) gaEvent.promotion_name = properties.promotion_name;
    if (properties.creative_name) gaEvent.creative_name = properties.creative_name;
    if (properties.creative_slot) gaEvent.creative_slot = properties.creative_slot;
    if (properties.location_id) gaEvent.location_id = properties.location_id;

    // 사용자 ID 설정
    if (userId) {
      gaEvent.user_id = userId;
    }

    // GA4에 이벤트 전송
    window.gtag('event', eventName, gaEvent);
  }

  private trackGeneralEvent(eventName: string, properties: any, userId?: string): void {
    // 일반 이벤트 형식으로 변환
    const gaEvent: Record<string, any> = {
      event_category: this.getEventCategory(eventName),
      ...properties,
    };

    if (userId) {
      gaEvent.user_id = userId;
    }

    // GA4 이벤트 전송
    window.gtag('event', eventName, gaEvent);
  }

  identify(userId: string, properties?: Record<string, any>): void {
    if (!this.isEnabled() || typeof window === 'undefined' || !window.gtag) {
      return;
    }

    window.gtag('config', this.measurementId, {
      user_id: userId,
      custom_map: properties,
    });

    trackingLogger.debug('GA user identified', { userId, properties });
  }

  setUserProperties(properties: Record<string, any>): void {
    if (!this.isEnabled() || typeof window === 'undefined' || !window.gtag) {
      return;
    }

    window.gtag('set', properties);

    trackingLogger.debug('GA user properties set', { properties });
  }

  isEnabled(): boolean {
    return this.isInitialized && !!this.measurementId;
  }

  private getEventCategory(eventName: string): string {
    if (eventName === 'purchase' || eventName === 'refund') return 'ecommerce';
    if (
      eventName.startsWith('view_') ||
      eventName.startsWith('add_to_') ||
      eventName.startsWith('begin_')
    )
      return 'ecommerce';
    if (eventName.startsWith('market_')) return 'market';
    if (eventName.startsWith('user_')) return 'user';
    if (eventName.startsWith('wallet_')) return 'wallet';
    if (eventName.includes('promotion')) return 'promotion';
    return 'general';
  }
}
