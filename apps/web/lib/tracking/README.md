# 트래킹 시스템

어댑터 패턴을 사용한 공통 트래킹 시스템입니다. Google Analytics와 Microsoft Clarity를 지원합니다.

## 🧪 라이브 테스트 & 예제

실제 동작하는 모든 트래킹 이벤트 예제는 개발 서버에서 확인할 수 있습니다:

```bash
pnpm dev:web
# http://localhost:3100/tracking-test 접속
```

**트래킹 테스트 페이지 주요 기능:**

- ✅ **실시간 상태 확인**: 트래킹 활성화 상태, 어댑터 개수, 동의 상태
- ✅ **GA4 전자상거래 이벤트**: Purchase, View Item, Add to Wishlist 등 모든 이벤트 테스트
- ✅ **기존 이벤트**: Market View, Wallet Connect, User Login 등
- ✅ **사용자 관리**: 식별, 속성 설정, 커스텀 이벤트
- ✅ **직접 비교**: 트래킹 서비스 vs 직접 gtag 호출 비교
- ✅ **실시간 로그**: 이벤트 전송 로그 실시간 확인
- ✅ **디버그 모드**: 콘솔에서 상세한 트래킹 과정 확인

## 특징

- **어댑터 패턴**: 각 트래킹 서비스를 독립적으로 관리
- **싱글톤 서비스**: 전역에서 동일한 인스턴스 사용
- **동의 기반**: 사용자 동의에 따른 트래킹 활성화
- **타입 세이프**: TypeScript로 완전한 타입 지원
- **React 훅**: 컴포넌트에서 쉽게 사용 가능
- **GA4 전자상거래 표준**: Google Analytics 4 전자상거래 이벤트 완전 지원

## 구조

```
lib/tracking/
├── types.ts                     # 트래킹 타입 정의
├── tracking.service.ts          # 메인 트래킹 서비스
├── adapters/
│   ├── google-analytics.adapter.ts    # GA 어댑터
│   └── microsoft-clarity.adapter.ts   # Clarity 어댑터
└── index.ts                     # 진입점
```

## 🚀 빠른 시작

### 1. 기본 설정

환경 변수 설정:

```env
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
NEXT_PUBLIC_CLARITY_PROJECT_ID=your_project_id
```

### 2. 자동 초기화

트래킹 시스템은 앱 시작 시 자동으로 초기화됩니다. 사용자 동의 상태에 따라 자동으로 활성화/비활성화됩니다.

```tsx
// 이미 설정됨 - 별도 초기화 불필요
import { trackingService } from '@/lib/tracking';

// 즉시 사용 가능
trackingService.trackPurchase({...});
```

## 📈 실제 사용 시나리오

### 시나리오 1: 마켓 상세 페이지

```tsx
'use client';

import { useEffect } from 'react';
import { trackingService } from '@/lib/tracking';

export default function MarketDetailPage({ marketId, marketTitle }) {
  useEffect(() => {
    // 페이지 진입 시 마켓 조회 이벤트
    trackingService.trackViewItem({
      marketId,
      marketTitle,
      category: 'Prediction Market',
      price: 0.75,
      currency: 'USDC',
      userId: user?.id,
    });
  }, [marketId]);

  const handlePrediction = (outcomeId: string, amount: number) => {
    // 예측 시작 시
    trackingService.trackBeginCheckout({
      marketId,
      outcomeId,
      marketTitle,
      category: 'Prediction Market',
      price: 0.75,
      currency: 'USDC',
      userId: user?.id,
    });
  };

  const handlePurchaseSuccess = (transactionId: string) => {
    // 구매 완료 시
    trackingService.trackPurchase({
      transactionId,
      marketId,
      outcomeId: selectedOutcome,
      marketTitle,
      category: 'Prediction Market',
      amount: totalAmount,
      currency: 'USDC',
      price: outcomePrice,
      quantity: 1,
      userId: user?.id,
      affiliation: 'PredictGO',
    });
  };
}
```

### 시나리오 2: 사용자 인증

```tsx
import { trackingService } from '@/lib/tracking';

// 로그인 성공 시
export const handleLoginSuccess = (user: User, method: string) => {
  // 사용자 식별
  trackingService.identify(user.id, {
    email: user.email,
    plan: user.subscriptionPlan,
    signupDate: user.createdAt,
    isVerified: user.isEmailVerified,
  });

  // 로그인 이벤트
  trackingService.trackUserLogin({
    method, // 'email', 'google', 'metamask' 등
    userId: user.id,
  });

  // 사용자 속성 설정
  trackingService.setUserProperties({
    preferredLanguage: user.language,
    theme: user.theme,
    referralCode: user.referralCode,
  });
};
```

### 시나리오 3: 지갑 연결

```tsx
import { trackingService } from '@/lib/tracking';

// 지갑 연결 시
export const handleWalletConnect = (walletType: string, address: string) => {
  trackingService.trackWalletConnect({
    walletType, // 'MetaMask', 'WalletConnect', 'Coinbase' 등
    userId: user?.id,
  });

  // 지갑 주소를 사용자 속성으로 설정
  trackingService.setUserProperties({
    walletAddress: address,
    walletType,
    hasWallet: true,
  });
};
```

### 시나리오 4: 프로모션 추적

```tsx
import { trackingService } from '@/lib/tracking';

// 배너 노출 시
export const trackPromotionView = (promotionData: Promotion) => {
  trackingService.trackViewPromotion({
    promotionId: promotionData.id,
    promotionName: promotionData.name,
    creativeName: promotionData.bannerName,
    creativeSlot: promotionData.position, // 'header', 'sidebar', 'modal'
    locationId: window.location.pathname,
    userId: user?.id,
  });
};

// 배너 클릭 시
export const trackPromotionClick = (promotionData: Promotion) => {
  trackingService.trackSelectPromotion({
    promotionId: promotionData.id,
    promotionName: promotionData.name,
    creativeName: promotionData.bannerName,
    creativeSlot: promotionData.position,
    locationId: window.location.pathname,
    userId: user?.id,
  });
};
```

## GA4 전자상거래 이벤트 사용법

### 1. 구매 이벤트 (Purchase)

```tsx
import { trackingService } from '@/lib/tracking';

// GA4 표준 구매 이벤트
trackingService.trackPurchase({
  transactionId: 'TXN_123456789',
  marketId: 'market-123',
  outcomeId: 'outcome-456',
  marketTitle: 'Bitcoin Price Prediction',
  category: 'Crypto',
  amount: 100, // 총 거래 금액
  currency: 'USDC',
  price: 0.75, // 단가
  quantity: 1,
  userId: 'user-789',
  affiliation: 'PredictGO',
  coupon: 'DISCOUNT10',
});
```

### 2. 마켓 조회 이벤트 (View Item)

```tsx
// 마켓 페이지 조회 시
trackingService.trackViewItem({
  marketId: 'market-123',
  outcomeId: 'outcome-456',
  marketTitle: 'Bitcoin Price Prediction',
  category: 'Crypto',
  price: 0.75,
  currency: 'USDC',
  userId: 'user-789',
});
```

### 3. 관심 목록 추가 (Add to Wishlist)

```tsx
// 관심 목록에 마켓 추가
trackingService.trackAddToWishlist({
  marketId: 'market-123',
  outcomeId: 'outcome-456',
  marketTitle: 'Bitcoin Price Prediction',
  category: 'Crypto',
  price: 0.75,
  currency: 'USDC',
  userId: 'user-789',
});
```

### 4. 구매 시작 이벤트 (Begin Checkout)

```tsx
// 구매 프로세스 시작
trackingService.trackBeginCheckout({
  marketId: 'market-123',
  outcomeId: 'outcome-456',
  marketTitle: 'Bitcoin Price Prediction',
  category: 'Crypto',
  price: 0.75,
  currency: 'USDC',
  userId: 'user-789',
});
```

### 5. 환불 이벤트 (Refund)

```tsx
// 환불 처리
trackingService.trackRefund({
  transactionId: 'TXN_123456789',
  marketId: 'market-123',
  outcomeId: 'outcome-456',
  marketTitle: 'Bitcoin Price Prediction',
  category: 'Crypto',
  amount: 100,
  currency: 'USDC',
  price: 0.75,
  quantity: 1,
  userId: 'user-789',
});
```

### 6. 프로모션 이벤트

```tsx
// 프로모션 배너 조회
trackingService.trackViewPromotion({
  promotionId: 'promo-welcome-2024',
  promotionName: 'Welcome Bonus',
  creativeName: 'welcome-banner',
  creativeSlot: 'header',
  locationId: 'homepage',
  userId: 'user-789',
});

// 프로모션 클릭
trackingService.trackSelectPromotion({
  promotionId: 'promo-welcome-2024',
  promotionName: 'Welcome Bonus',
  creativeName: 'welcome-banner',
  creativeSlot: 'header',
  locationId: 'homepage',
  userId: 'user-789',
});
```

## 기본 사용법

### 1. React 훅 사용

```tsx
import { useTracking } from '@/hooks/use-tracking';

function MyComponent() {
  const { trackPurchase, trackViewItem, isTrackingEnabled } = useTracking();

  const handleMarketView = () => {
    trackViewItem({
      marketId: 'market-123',
      outcomeId: 'outcome-456',
      marketTitle: 'Bitcoin Price Prediction',
      category: 'Crypto',
      price: 0.75,
      currency: 'USDC',
      userId: 'user-789',
    });
  };

  const handlePurchase = () => {
    trackPurchase({
      transactionId: 'TXN_' + Date.now(),
      marketId: 'market-123',
      outcomeId: 'outcome-456',
      marketTitle: 'Bitcoin Price Prediction',
      category: 'Crypto',
      amount: 100,
      currency: 'USDC',
      price: 0.75,
      userId: 'user-789',
    });
  };

  return (
    <div>
      {isTrackingEnabled && <p>트래킹이 활성화되었습니다</p>}
      <button onClick={handleMarketView}>마켓 보기</button>
      <button onClick={handlePurchase}>구매하기</button>
    </div>
  );
}
```

### 2. 직접 서비스 사용

```tsx
import { trackingService } from '@/lib/tracking';

// 사용자 식별
trackingService.identify('user-789', {
  email: '<EMAIL>',
  plan: 'premium',
});

// 커스텀 이벤트
trackingService.track({
  event: 'custom_event',
  properties: {
    customProperty: 'value',
    timestamp: Date.now(),
  },
  userId: 'user-789',
});
```

### 3. 컴포넌트를 통한 트래킹

```tsx
import { PurchaseTracking, usePurchaseTracking } from '@/components/tracking/purchase-tracking';

function MarketPage({ marketId, userId }) {
  const { trackPurchaseEvent } = usePurchaseTracking();

  const handlePurchaseComplete = purchaseData => {
    trackPurchaseEvent({
      transactionId: purchaseData.transactionId,
      marketId: purchaseData.marketId,
      outcomeId: purchaseData.outcomeId,
      amount: purchaseData.amount,
      currency: 'USDC',
      price: purchaseData.price,
      userId,
    });
  };

  return (
    <div>
      {/* 마켓 조회 자동 트래킹 */}
      <PurchaseTracking
        marketId={marketId}
        outcomeId="placeholder"
        amount="0"
        currency="USDC"
        price="0"
        userId={userId}
        marketTitle="Bitcoin Price Prediction"
        category="Crypto"
      />

      {/* 마켓 컨텐츠 */}
      <MarketContent onPurchaseComplete={handlePurchaseComplete} />
    </div>
  );
}
```

## 지원하는 이벤트 타입

### GA4 전자상거래 이벤트

#### 구매 이벤트 (PurchaseEvent)

```typescript
{
  event: 'purchase',
  properties: {
    transaction_id: string;   // 거래 ID (필수)
    value: number;           // 총 거래 금액 (필수)
    currency: string;        // 통화 (필수)
    items: EcommerceItem[];  // 아이템 배열 (필수)

    // 추가 매개변수 (선택사항)
    affiliation?: string;    // 제휴사 정보
    coupon?: string;         // 쿠폰 코드
    shipping?: number;       // 배송비
    tax?: number;           // 세금

    // 커스텀 매개변수 (기존 호환성)
    marketId: string;
    outcomeId: string;
    timestamp: number;
  },
  userId?: string;
}
```

#### 아이템 조회 이벤트 (ViewItemEvent)

```typescript
{
  event: 'view_item',
  properties: {
    currency: string;
    value: number;
    items: EcommerceItem[];

    // 커스텀 매개변수
    marketId: string;
    marketTitle?: string;
    category?: string;
    timestamp: number;
  },
  userId?: string;
}
```

#### 전자상거래 아이템 구조 (EcommerceItem)

```typescript
{
  item_id: string;          // marketId + outcomeId 조합
  item_name: string;        // 예측 결과명 (예: "Yes" 또는 "No")
  item_category?: string;   // 마켓 카테고리
  item_category2?: string;  // 서브카테고리
  item_brand?: string;      // 브랜드 (예: "PredictGO")
  price: number;           // 단가
  quantity: number;        // 수량 (보통 1)
  currency: string;        // 통화
}
```

### 기존 이벤트 (하위 호환성)

#### 마켓 이벤트 (MarketEvent)

```typescript
{
  event: 'market_view' | 'market_share' | 'market_favorite',
  properties: {
    marketId: string;
    marketTitle?: string;
    category?: string;
    timestamp: number;
  },
  userId?: string;
}
```

#### 사용자 이벤트 (UserEvent)

```typescript
{
  event: 'user_login' | 'user_logout' | 'user_register' | 'wallet_connect' | 'wallet_disconnect',
  properties: {
    method?: string;
    walletType?: string;
    timestamp: number;
  },
  userId?: string;
}
```

## 환경 변수

```env
# Google Analytics 측정 ID
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Microsoft Clarity 프로젝트 ID
NEXT_PUBLIC_CLARITY_PROJECT_ID=your-clarity-project-id

# 트래킹 디버그 모드 (true/false 또는 1/0)
NEXT_PUBLIC_TRACKING_DEBUG=true
```

## 마이그레이션 가이드

### 기존 trackPurchase에서 새로운 표준으로

**기존 (Deprecated):**

```tsx
trackingService.trackPurchase({
  marketId: 'market-123',
  outcomeId: 'outcome-456',
  amount: '100', // 문자열
  currency: 'USDC',
  price: '0.75', // 문자열
  userId: 'user-789',
});
```

**새로운 GA4 표준 (권장):**

```tsx
trackingService.trackPurchase({
  transactionId: 'TXN_' + Date.now(), // 필수 추가
  marketId: 'market-123',
  outcomeId: 'outcome-456',
  marketTitle: 'Bitcoin Price Prediction', // 권장 추가
  category: 'Crypto', // 권장 추가
  amount: 100, // 숫자
  currency: 'USDC',
  price: 0.75, // 숫자
  userId: 'user-789',
});
```

## 디버그 모드

트래킹 시스템의 디버그 로그를 제어할 수 있습니다.

### 활성화 조건

- `NEXT_PUBLIC_TRACKING_DEBUG=true` 또는 `NEXT_PUBLIC_TRACKING_DEBUG=1`
- `NEXT_PUBLIC_ENV=local`

### 디버그 로그 종류

- **debug**: 일반적인 디버그 정보
- **info**: 초기화 및 중요 정보
- **warn**: 경고 메시지
- **error**: 에러 메시지 (항상 출력)

### 직접 로거 사용

```tsx
import { trackingLogger } from '@/lib/tracking';

trackingLogger.debug('Custom debug message', { data: 'example' });
trackingLogger.info('Custom info message');
trackingLogger.warn('Custom warning message');
trackingLogger.error('Custom error message', error);

// 디버그 모드 동적 변경
trackingLogger.setDebugMode(true);
console.log(trackingLogger.isDebugEnabled()); // true
```

## 확장하기

새로운 트래킹 서비스를 추가하려면:

1. `TrackingAdapter` 인터페이스를 구현
2. `TrackingService`에 어댑터 추가
3. 필요한 경우 환경 변수 설정

### 예시: 새로운 어댑터 추가

```typescript
import { TrackingAdapter, AnalyticsEvent } from '../types';

export class CustomTrackingAdapter implements TrackingAdapter {
  private apiKey: string;
  private isInitialized = false;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }

  initialize(): void {
    // 초기화 로직
    this.isInitialized = true;
  }

  track(event: AnalyticsEvent): void {
    // 이벤트 전송 로직
  }

  identify(userId: string, properties?: Record<string, any>): void {
    // 사용자 식별 로직
  }

  setUserProperties(properties: Record<string, any>): void {
    // 사용자 속성 설정 로직
  }

  isEnabled(): boolean {
    return this.isInitialized && !!this.apiKey;
  }
}
```

## 🔧 트러블슈팅

### 문제: 트래킹이 작동하지 않음

**해결 방법:**

1. **트래킹 테스트 페이지 확인**

   ```bash
   pnpm dev:web
   # http://localhost:3100/tracking-test 접속
   ```

2. **상태 확인**

   - 활성 어댑터: 2개 (GoogleAnalyticsAdapter, MicrosoftClarityAdapter)
   - 동의 상태: GA: ✅, Clarity: ✅
   - gtag 함수: 로드됨

3. **환경 변수 확인**

   ```env
   NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX
   NEXT_PUBLIC_CLARITY_PROJECT_ID=your_project_id
   ```

4. **디버그 모드 활성화**

   ```tsx
   // 브라우저 콘솔에서
   window.TRACKING_DEBUG = true;

   // 또는 환경 변수
   NEXT_PUBLIC_TRACKING_DEBUG = true;
   ```

### 문제: 이벤트가 GA에서 보이지 않음

**해결 방법:**

1. **브라우저 개발자 도구 확인**

   - Network 탭에서 `collect` 요청 확인
   - gtag 호출 여부 확인

2. **GA 실시간 보고서 확인**

   - Google Analytics > 보고서 > 실시간

3. **직접 gtag 테스트**
   ```tsx
   // 트래킹 테스트 페이지에서 "🔥 DIRECT gtag" 버튼 클릭
   ```

### 문제: TypeScript 타입 에러

**해결 방법:**

```tsx
// 타입 가져오기
import type { PurchaseEventData, ViewItemEventData } from '@/lib/tracking';

// 완전한 타입 정의 사용
const purchaseData: PurchaseEventData = {
  transactionId: 'TXN_123',
  marketId: 'market-123',
  outcomeId: 'outcome-456',
  marketTitle: 'Bitcoin Prediction',
  category: 'Crypto',
  amount: 100,
  currency: 'USDC',
  price: 0.75,
  quantity: 1,
  userId: 'user-789',
};
```

## 🎯 베스트 프랙티스

### 1. 이벤트 명명 규칙

```tsx
// ✅ 좋은 예
trackingService.trackPurchase({
  transactionId: 'TXN_1640995200000', // 고유 ID
  marketId: 'bitcoin-price-2024-01', // 의미있는 ID
  marketTitle: 'Bitcoin Price Prediction for 2024', // 읽기 쉬운 제목
});

// ❌ 나쁜 예
trackingService.trackPurchase({
  transactionId: '123', // 너무 단순
  marketId: 'market1', // 의미 없음
  marketTitle: 'market', // 정보 부족
});
```

### 2. 에러 처리

```tsx
// ✅ 트래킹 실패가 앱 로직에 영향을 주지 않도록
try {
  await processPayment(paymentData);

  // 결제 성공 후 트래킹 (실패해도 괜찮음)
  trackingService.trackPurchase(purchaseData);
} catch (error) {
  // 결제 실패 처리만 집중
  handlePaymentError(error);
}
```

### 3. 사용자 식별 타이밍

```tsx
// ✅ 로그인 직후에 식별
useEffect(() => {
  if (user) {
    trackingService.identify(user.id, {
      email: user.email,
      plan: user.plan,
      signupDate: user.createdAt,
    });
  }
}, [user]);
```

### 4. 동의 상태 확인

```tsx
import { useTrackingConsent } from '@/hooks/use-tracking-consent';

function MyComponent() {
  const { consentState } = useTrackingConsent();

  const handleAction = () => {
    // 선택적: 동의 상태 확인 후 추가 로직
    if (consentState?.googleAnalytics) {
      // GA 동의가 있을 때만 실행할 로직
    }

    // 트래킹은 자동으로 동의 상태를 확인함
    trackingService.trackCustomEvent({...});
  };
}
```

## 📊 성능 고려사항

### 1. 이벤트 배치 처리

현재 시스템은 이벤트를 즉시 전송합니다. 높은 빈도의 이벤트가 예상되면 배치 처리 고려:

```tsx
// 향후 개선 예정
trackingService.setBatchMode(true); // 배치 모드 활성화
trackingService.setBatchSize(10); // 10개씩 배치
trackingService.setBatchTimeout(5000); // 5초마다 전송
```

### 2. 중요하지 않은 이벤트 지연

```tsx
// 중요한 이벤트는 즉시
trackingService.trackPurchase(purchaseData);

// 덜 중요한 이벤트는 지연 가능
setTimeout(() => {
  trackingService.trackViewItem(viewData);
}, 100);
```

## 🔒 개인정보 보호

### 1. PII (개인식별정보) 제외

```tsx
// ✅ 좋은 예 - 해시된 식별자 사용
trackingService.identify(hashedUserId, {
  plan: 'premium',
  signupDate: '2024-01-01',
  isVerified: true,
});

// ❌ 나쁜 예 - 개인정보 포함
trackingService.identify(userId, {
  email: '<EMAIL>', // 피해야 함
  name: 'John Doe', // 피해야 함
  phone: '+1234567890', // 피해야 함
});
```

### 2. 동의 기반 트래킹

현재 시스템은 사용자 동의를 자동으로 확인합니다:

```tsx
// 동의 상태는 자동으로 관리됨
// 별도 확인 불필요
trackingService.trackPurchase(data);
```

## 주의사항

- ✅ **자동 초기화**: 사용자 동의 상태에 따라 자동으로 초기화됩니다
- ✅ **동의 기반**: 사용자 동의 없이는 트래킹이 실행되지 않습니다
- ✅ **타임스탬프**: 모든 이벤트에 자동으로 타임스탬프가 추가됩니다
- ✅ **에러 격리**: 한 어댑터의 에러가 다른 어댑터에 영향을 주지 않습니다
- ✅ **SSR 안전**: 브라우저 환경에서만 동작하며 서버사이드 렌더링을 고려합니다
- ✅ **하이드레이션 안전**: 클라이언트/서버 불일치 문제를 방지합니다
- ✅ **실시간 상태**: UI에서 트래킹 상태를 실시간으로 확인할 수 있습니다

---

## 📖 추가 리소스

- **라이브 테스트**: `http://localhost:3100/tracking-test`
- **Google Analytics 4 문서**: [GA4 Enhanced Ecommerce](https://developers.google.com/analytics/devguides/collection/ga4/ecommerce)
- **Microsoft Clarity 문서**: [Clarity Setup Guide](https://docs.microsoft.com/en-us/clarity/)

> **💡 팁**: 모든 기능과 실제 작동 예제는 트래킹 테스트 페이지에서 확인할 수 있습니다!
