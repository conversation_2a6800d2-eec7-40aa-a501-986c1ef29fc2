import { TrackingAdapter, AnalyticsEvent, TrackingConfig, EcommerceItem } from './types';
import { GoogleAnalyticsAdapter } from './adapters/google-analytics.adapter';
import { MicrosoftClarityAdapter } from './adapters/microsoft-clarity.adapter';
import { env } from '@/lib/env';
import { trackingLogger } from './logger';

export class TrackingService {
  private static instance: TrackingService;
  private adapters: TrackingAdapter[] = [];
  private config: TrackingConfig;
  private isInitialized = false;

  private constructor() {
    this.config = {
      enabled: true,
      adapters: [],
    };
  }

  public static getInstance(): TrackingService {
    if (!TrackingService.instance) {
      TrackingService.instance = new TrackingService();
    }
    return TrackingService.instance;
  }

  public initialize(options: {
    gaId?: string;
    clarityProjectId?: string;
    enabled?: boolean;
  }): void {
    // 이미 초기화되었고 설정이 같다면 건너뛰기
    if (this.isInitialized && this.config.enabled === (options.enabled ?? true)) {
      return;
    }

    // 설정 업데이트
    this.config.enabled = options.enabled ?? true;

    // 기존 어댑터 정리
    this.adapters = [];

    if (!this.config.enabled) {
      trackingLogger.info('Tracking is disabled');
      this.isInitialized = true;
      return;
    }

    // Google Analytics 어댑터 추가
    if (options.gaId) {
      const gaAdapter = new GoogleAnalyticsAdapter(options.gaId);
      gaAdapter.initialize();
      this.adapters.push(gaAdapter);
      trackingLogger.info('Google Analytics adapter initialized');
    }

    // Microsoft Clarity 어댑터 추가
    if (options.clarityProjectId) {
      const clarityAdapter = new MicrosoftClarityAdapter(options.clarityProjectId);
      clarityAdapter.initialize();
      this.adapters.push(clarityAdapter);
      trackingLogger.info('Microsoft Clarity adapter initialized');
    }

    this.config.adapters = this.adapters;
    this.isInitialized = true;

    trackingLogger.info(`Service initialized with ${this.adapters.length} adapters`);
  }

  public track(event: AnalyticsEvent): void {
    if (!this.config.enabled) {
      return;
    }

    if (this.adapters.length === 0) {
      return;
    }

    // 타임스탬프 추가
    if (event.properties && !event.properties.timestamp) {
      event.properties.timestamp = Date.now();
    }

    this.adapters.forEach(adapter => {
      if (adapter.isEnabled()) {
        try {
          adapter.track(event);
        } catch (error) {
          trackingLogger.error('Error in adapter', error);
        }
      }
    });
  }

  public identify(userId: string, properties?: Record<string, any>): void {
    if (!this.config.enabled || this.adapters.length === 0) {
      return;
    }

    this.adapters.forEach(adapter => {
      if (adapter.isEnabled()) {
        try {
          adapter.identify(userId, properties);
        } catch (error) {
          trackingLogger.error('Error in adapter identify', error);
        }
      }
    });
  }

  public setUserProperties(properties: Record<string, any>): void {
    if (!this.config.enabled || this.adapters.length === 0) {
      return;
    }

    this.adapters.forEach(adapter => {
      if (adapter.isEnabled()) {
        try {
          adapter.setUserProperties(properties);
        } catch (error) {
          trackingLogger.error('Error in adapter setUserProperties', error);
        }
      }
    });
  }

  // 헬퍼 함수: 아이템 생성
  private createEcommerceItem(data: {
    marketId: string;
    outcomeId: string;
    marketTitle?: string;
    category?: string;
    price: number;
    quantity?: number;
    currency: string;
  }): EcommerceItem {
    return {
      item_id: `${data.marketId}_${data.outcomeId}`,
      item_name: data.marketTitle || `Market ${data.marketId}`,
      item_category: data.category || 'Prediction Market',
      item_brand: 'PredictGO',
      price: data.price,
      quantity: data.quantity || 1,
      currency: data.currency,
    };
  }

  // GA4 표준 구매 이벤트
  public trackPurchase(data: {
    transactionId: string;
    marketId: string;
    outcomeId: string;
    marketTitle?: string;
    category?: string;
    amount: number; // 총 거래 금액
    currency: string;
    price: number; // 단가
    quantity?: number;
    userId?: string;
    affiliation?: string;
    coupon?: string;
  }): void {
    const item = this.createEcommerceItem({
      marketId: data.marketId,
      outcomeId: data.outcomeId,
      marketTitle: data.marketTitle,
      category: data.category,
      price: data.price,
      quantity: data.quantity,
      currency: data.currency,
    });

    this.track({
      event: 'purchase',
      properties: {
        transaction_id: data.transactionId,
        value: data.amount,
        currency: data.currency,
        items: [item],
        affiliation: data.affiliation,
        coupon: data.coupon,
        // 기존 호환성을 위한 커스텀 매개변수
        marketId: data.marketId,
        outcomeId: data.outcomeId,
        timestamp: Date.now(),
      },
      userId: data.userId,
    });
  }

  // 아이템 조회 이벤트 (마켓 조회)
  public trackViewItem(data: {
    marketId: string;
    outcomeId: string;
    marketTitle?: string;
    category?: string;
    price: number;
    currency: string;
    userId?: string;
  }): void {
    const item = this.createEcommerceItem({
      marketId: data.marketId,
      outcomeId: data.outcomeId,
      marketTitle: data.marketTitle,
      category: data.category,
      price: data.price,
      currency: data.currency,
    });

    this.track({
      event: 'view_item',
      properties: {
        currency: data.currency,
        value: data.price,
        items: [item],
        marketId: data.marketId,
        marketTitle: data.marketTitle,
        category: data.category,
        timestamp: Date.now(),
      },
      userId: data.userId,
    });
  }

  // 관심 목록 추가 (장바구니 대신)
  public trackAddToWishlist(data: {
    marketId: string;
    outcomeId: string;
    marketTitle?: string;
    category?: string;
    price: number;
    currency: string;
    userId?: string;
  }): void {
    const item = this.createEcommerceItem({
      marketId: data.marketId,
      outcomeId: data.outcomeId,
      marketTitle: data.marketTitle,
      category: data.category,
      price: data.price,
      currency: data.currency,
    });

    this.track({
      event: 'add_to_wishlist',
      properties: {
        currency: data.currency,
        value: data.price,
        items: [item],
        marketId: data.marketId,
        outcomeId: data.outcomeId,
        timestamp: Date.now(),
      },
      userId: data.userId,
    });
  }

  // 구매 시작 이벤트
  public trackBeginCheckout(data: {
    marketId: string;
    outcomeId: string;
    marketTitle?: string;
    category?: string;
    price: number;
    currency: string;
    userId?: string;
  }): void {
    const item = this.createEcommerceItem({
      marketId: data.marketId,
      outcomeId: data.outcomeId,
      marketTitle: data.marketTitle,
      category: data.category,
      price: data.price,
      currency: data.currency,
    });

    this.track({
      event: 'begin_checkout',
      properties: {
        currency: data.currency,
        value: data.price,
        items: [item],
        marketId: data.marketId,
        outcomeId: data.outcomeId,
        timestamp: Date.now(),
      },
      userId: data.userId,
    });
  }

  // 환불 이벤트
  public trackRefund(data: {
    transactionId: string;
    marketId: string;
    outcomeId: string;
    marketTitle?: string;
    category?: string;
    amount: number;
    currency: string;
    price: number;
    quantity?: number;
    userId?: string;
  }): void {
    const item = this.createEcommerceItem({
      marketId: data.marketId,
      outcomeId: data.outcomeId,
      marketTitle: data.marketTitle,
      category: data.category,
      price: data.price,
      quantity: data.quantity,
      currency: data.currency,
    });

    this.track({
      event: 'refund',
      properties: {
        transaction_id: data.transactionId,
        value: data.amount,
        currency: data.currency,
        items: [item],
        marketId: data.marketId,
        timestamp: Date.now(),
      },
      userId: data.userId,
    });
  }

  // 프로모션 조회 이벤트
  public trackViewPromotion(data: {
    promotionId: string;
    promotionName: string;
    creativeName?: string;
    creativeSlot?: string;
    locationId?: string;
    userId?: string;
  }): void {
    this.track({
      event: 'view_promotion',
      properties: {
        promotion_id: data.promotionId,
        promotion_name: data.promotionName,
        creative_name: data.creativeName,
        creative_slot: data.creativeSlot,
        location_id: data.locationId,
        timestamp: Date.now(),
      },
      userId: data.userId,
    });
  }

  // 프로모션 선택 이벤트
  public trackSelectPromotion(data: {
    promotionId: string;
    promotionName: string;
    creativeName?: string;
    creativeSlot?: string;
    locationId?: string;
    userId?: string;
  }): void {
    this.track({
      event: 'select_promotion',
      properties: {
        promotion_id: data.promotionId,
        promotion_name: data.promotionName,
        creative_name: data.creativeName,
        creative_slot: data.creativeSlot,
        location_id: data.locationId,
        timestamp: Date.now(),
      },
      userId: data.userId,
    });
  }

  // 기존 메서드들 (하위 호환성을 위해 유지하되, 새로운 표준 메서드 사용 권장)
  public trackMarketView(data: {
    marketId: string;
    marketTitle?: string;
    category?: string;
    userId?: string;
  }): void {
    this.track({
      event: 'market_view',
      properties: {
        marketId: data.marketId,
        marketTitle: data.marketTitle,
        category: data.category,
        timestamp: Date.now(),
      },
      userId: data.userId,
    });
  }

  public trackWalletConnect(data: { walletType: string; userId?: string }): void {
    this.track({
      event: 'wallet_connect',
      properties: {
        walletType: data.walletType,
        timestamp: Date.now(),
      },
      userId: data.userId,
    });
  }

  public trackUserLogin(data: { method: string; userId?: string }): void {
    this.track({
      event: 'user_login',
      properties: {
        method: data.method,
        timestamp: Date.now(),
      },
      userId: data.userId,
    });
  }

  public isEnabled(): boolean {
    return this.config.enabled && this.isInitialized;
  }

  public getEnabledAdapters(): TrackingAdapter[] {
    return this.adapters.filter(adapter => adapter.isEnabled());
  }

  public updateConsent(enabled: boolean): void {
    const wasDisabled = !this.config.enabled;
    this.config.enabled = enabled;

    // 비활성화에서 활성화로 변경되고, 어댑터가 없다면 다시 초기화
    if (wasDisabled && enabled && this.adapters.length === 0) {
      this.isInitialized = false; // 재초기화를 위해 플래그 리셋
      this.initialize({
        gaId: env.NEXT_PUBLIC_GA_MEASUREMENT_ID,
        clarityProjectId: env.NEXT_PUBLIC_CLARITY_PROJECT_ID,
        enabled: true,
      });
    }

    trackingLogger.info(`Tracking consent ${enabled ? 'granted' : 'withdrawn'}`);
  }
}

// 전역 인스턴스 내보내기
export const trackingService = TrackingService.getInstance();
