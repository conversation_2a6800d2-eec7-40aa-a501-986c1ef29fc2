import { concat, pad, toHex, type Hex } from 'viem';
import type { UserOperation } from 'viem/account-abstraction';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import { useRouter } from 'next/navigation';

dayjs.extend(relativeTime);

export function copyToClipboard(text: string) {
  navigator.clipboard.writeText(text);
}

export function isBrowser() {
  if (typeof window === 'undefined') {
    return false;
  }
  return true;
}

export function createPaymasterAndData(userOp: UserOperation): Hex {
  return concat([
    userOp.paymaster as Hex,
    pad(toHex(userOp.paymasterVerificationGasLimit || BigInt(0)), {
      size: 16,
    }),
    pad(toHex(userOp.paymasterPostOpGasLimit || BigInt(0)), {
      size: 16,
    }),
    (userOp.paymasterData as Hex) || ('0x' as Hex),
  ]);
}

export function convertUserOpToBigInt(userOp: any) {
  return {
    ...userOp,
    maxFeePerGas: BigInt(userOp.maxFeePerGas),
    maxPriorityFeePerGas: BigInt(userOp.maxPriorityFeePerGas),
    nonce: BigInt(userOp.nonce),
    paymasterPostOpGasLimit: BigInt(userOp.paymasterPostOpGasLimit),
    callGasLimit: BigInt(userOp.callGasLimit),
    preVerificationGas: BigInt(userOp.preVerificationGas),
    verificationGasLimit: BigInt(userOp.verificationGasLimit),
    paymasterVerificationGasLimit: BigInt(userOp.paymasterVerificationGasLimit),
  };
}
export function toLocalDate(date: string | Date | number) {
  return new Date(date).toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });
}

export function toRelativeTime(date: string | Date | number) {
  return dayjs(date).fromNow(true) + ' ago';
}

export const toPercentage = (value: number | string): string => {
  const numValue = typeof value === 'string' ? Number(value) : value;
  return (numValue * 100).toFixed(2) + '%';
};

export function toScannerUrl(txHash: string) {
  return `https://sepolia.basescan.org/tx/${txHash}`;
}

export function safeDivide(a: bigint | string | number, b: bigint | string | number) {
  return b === BigInt(0) ? 0 : Number(BigInt(a) / BigInt(b));
}

export function shortenNumber(num: number | string): string {
  const value = typeof num === 'string' ? Number(num) : num;

  const units = [
    { threshold: 1_000_000_000_000, suffix: 'T' },
    { threshold: 1_000_000_000, suffix: 'B' },
    { threshold: 1_000_000, suffix: 'M' },
    { threshold: 1_000, suffix: 'K' },
  ];

  const unit = units.find(u => value >= u.threshold);
  return unit ? (value / unit.threshold).toFixed(2) + unit.suffix : value.toFixed(2);
}

export type Router = ReturnType<typeof useRouter>;
export const updateSearchParams = (
  updates: Record<string, string | number | null>,
  router: Router,
  searchParams: URLSearchParams,
  pathname: string
) => {
  const newParams = new URLSearchParams(searchParams.toString());

  Object.entries(updates).forEach(([key, value]) => {
    if (value !== null && value !== undefined) {
      newParams.set(key, value.toString());
    } else {
      newParams.delete(key);
    }
  });

  router.push(`${pathname}?${newParams.toString()}`);
};

export function normalizeOptions(options: Record<string, any> | undefined) {
  if (!options) return '';

  return Object.entries(options)
    .map(([key, value]) => `${key}:${value}`)
    .join('::k');
}
