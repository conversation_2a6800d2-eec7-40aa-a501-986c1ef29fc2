import { isProd } from '@/constants';
import { WagmiAdapter } from '@reown/appkit-adapter-wagmi';
import { cookieStorage, createStorage } from '@wagmi/core';
import { base as baseNetwork, baseSepolia } from '@wagmi/core/chains';
import { toSafeSmartAccount } from 'permissionless/accounts';
import { createPublicClient, http, type WalletClient } from 'viem';
import { entryPoint07Address } from 'viem/account-abstraction';

export const projectId = process.env.NEXT_PUBLIC_APP_KIT_PROJECT_ID;

if (!projectId) {
  throw new Error('Project ID is not defined');
}

export const network = isProd ? baseSepolia : baseSepolia;
export const networks = [network];
export const networkIds = networks.map(network => network.id as number);

export const publicClient = createPublicClient({
  chain: network,
  transport: http(network.rpcUrls.default.http[0]),
});

export const wagmiAdapter = new WagmiAdapter({
  storage: createStorage({
    storage: cookieStorage,
  }),
  ssr: true,
  projectId,
  networks,
});

export const wagmiConfig = wagmiAdapter.wagmiConfig;

export const getSmartAccount = async (walletClient: WalletClient) => {
  const safeSmartAccount = await toSafeSmartAccount({
    client: publicClient,
    owners: [walletClient],
    version: '1.4.1',
    entryPoint: {
      address: entryPoint07Address,
      version: '0.7',
    },
  });

  return safeSmartAccount;
};
