# Test Utils

웹 애플리케이션을 위한 공통 테스트 유틸리티 모음입니다.

## MockWallet

Web3 인증 테스트를 위한 고정된 Private Key를 사용하는 Mock Ethereum Wallet 유틸리티입니다.

### 특징

- 🔑 **고정된 Private Key**: 예측 가능한 테스트 결과
- ✅ **실제 서명 기능**: viem을 사용한 실제 암호화 서명
- 🛡️ **SIWE 호환**: @reown/appkit-siwe와 완전 호환
- 🧪 **풍부한 헬퍼**: 다양한 테스트 시나리오 지원

### 기본 사용법

```typescript
import { MockWallet } from '@/lib/test-utils';

// 기본 정보 사용
const address = MockWallet.ADDRESS; // ******************************************
const chainId = MockWallet.CHAIN_ID; // polygonAmoy.id

// 메시지 서명
const signature = await MockWallet.signMessage('Hello, world!');
console.log(MockWallet.isValidSignature(signature)); // true
```

### SIWE 인증 테스트

```typescript
// SIWE 메시지 생성 및 서명
const { message, signature, args } = await MockWallet.signSIWEMessage({
  nonce: 'custom-nonce-123',
  domain: 'example.com',
});

// 커스텀 SIWE 인수 생성
const customArgs = MockWallet.createSIWEArgs({
  nonce: 'test-nonce',
  statement: 'Custom statement',
});

const siweMessage = MockWallet.createSIWEMessage(customArgs, MockWallet.ADDRESS);
```

### 검증 함수

```typescript
// 주소 검증
MockWallet.isValidAddress('******************************************'); // true
MockWallet.isValidAddress('0xinvalid'); // false

// 서명 형식 검증
MockWallet.isValidSignature('0x...130자리'); // true
MockWallet.isValidSignature('invalid'); // false
```

### 테스트 예시

```typescript
import { describe, it, expect, vi } from 'vitest';
import { MockWallet } from '@/lib/test-utils';
import { AuthService } from './auth.service';

describe('Auth Service', () => {
  it('should verify SIWE signature', async () => {
    // Arrange
    const { message, signature } = await MockWallet.signSIWEMessage({
      nonce: 'test-nonce',
    });

    // Mock API response
    vi.spyOn(authService.fetcher, 'post').mockResolvedValue({
      verified: true,
      address: MockWallet.ADDRESS,
    });

    // Act
    const result = await authService.verify({ message, signature });

    // Assert
    expect(result.verified).toBe(true);
    expect(result.address).toBe(MockWallet.ADDRESS);
  });
});
```

## API 참조

### MockWallet 상수

| 속성          | 타입      | 설명                      |
| ------------- | --------- | ------------------------- |
| `PRIVATE_KEY` | `Hex`     | 테스트용 고정 Private Key |
| `ADDRESS`     | `Address` | 고정 지갑 주소            |
| `CHAIN_ID`    | `number`  | 체인 ID (Polygon Amoy)    |

### MockWallet 객체

| 속성      | 타입           | 설명                   |
| --------- | -------------- | ---------------------- |
| `account` | `Account`      | viem Account 객체      |
| `client`  | `WalletClient` | viem WalletClient 객체 |

### MockWallet 메서드

| 메서드              | 시그니처                                              | 설명             |
| ------------------- | ----------------------------------------------------- | ---------------- |
| `signMessage`       | `(message: string) => Promise<string>`                | 일반 메시지 서명 |
| `createSIWEArgs`    | `(overrides?) => SIWECreateMessageArgs`               | SIWE 인수 생성   |
| `createSIWEMessage` | `(args, address) => string`                           | SIWE 메시지 생성 |
| `signSIWEMessage`   | `(overrides?) => Promise<{message, signature, args}>` | SIWE 메시지 서명 |
| `isValidAddress`    | `(address: string) => boolean`                        | 주소 검증        |
| `isValidSignature`  | `(signature: string) => boolean`                      | 서명 형식 검증   |

## 확장

새로운 테스트 유틸리티를 추가할 때는:

1. `lib/test-utils/` 디렉토리에 새 파일 생성
2. `lib/test-utils/index.ts`에 export 추가
3. 해당 유틸리티의 테스트 파일 작성
4. 이 README에 문서 추가

## 보안 주의사항

⚠️ **경고**: 이 Private Key는 테스트 전용입니다. 실제 자금을 보유하지 마세요.

- Private Key: `0x59c6995e998f97e48234cedc2a5cdcb2b8c5c7a14a5b16ef0b7d5ca5c5d4f5e8`
- Address: `******************************************`

이 키는 공개적으로 알려진 테스트용 키이므로 절대 실제 네트워크에서 사용하지 마세요.
