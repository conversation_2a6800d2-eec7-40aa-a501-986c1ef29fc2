import { privateKeyToAccount } from 'viem/accounts';
import { createWalletClient, http, type Hex, type WalletClient } from 'viem';
import { polygonAmoy } from 'viem/chains';
import type { SIWECreateMessageArgs } from '@reown/appkit-siwe';
import { formatMessage } from '@reown/appkit-siwe';

/**
 * 테스트용 고정된 Private Key
 * 실제 자금이 없는 테스트 전용 키입니다.
 */
export const TEST_PRIVATE_KEY: Hex =
  '0x59c6995e998f97e48234cedc2a5cdcb2b8c5c7a14a5b16ef0b7d5ca5c5d4f5e8';

/**
 * 테스트용 계정 (고정된 PK로 생성)
 */
export const mockAccount = privateKeyToAccount(TEST_PRIVATE_KEY);

/**
 * 테스트용 고정 주소
 * ******************************************
 */
export const TEST_ADDRESS = mockAccount.address;

/**
 * 테스트용 Wallet Client
 * 실제 서명 기능을 제공하는 mock wallet
 */
export const mockWalletClient: WalletClient = createWalletClient({
  account: mockAccount,
  chain: polygonAmoy,
  transport: http(),
});
/**
 * SIWE 메시지 생성 헬퍼 함수
 * @reown/appkit-siwe의 formatMessage를 사용
 */
export const createTestSIWEMessage = (args: SIWECreateMessageArgs, address: string): string => {
  return formatMessage(args, address);
};

/**
 * 테스트용 SIWE 메시지 인수 생성 헬퍼
 */
export const createTestSIWEArgs = (
  overrides: Partial<SIWECreateMessageArgs> = {}
): SIWECreateMessageArgs => {
  return {
    domain: 'localhost:3000',
    address: TEST_ADDRESS,
    uri: 'http://localhost:3000',
    version: '1',
    chainId: polygonAmoy.id,
    nonce: 'test-nonce-12345',
    statement: 'Please sign with your account',
    ...overrides,
  };
};

/**
 * 완전한 SIWE 메시지 생성 및 서명
 */
export const createSignedSIWEMessage = async (
  messageArgs?: Partial<SIWECreateMessageArgs>
): Promise<{ message: string; signature: string; args: SIWECreateMessageArgs }> => {
  const args = createTestSIWEArgs(messageArgs);
  const message = createTestSIWEMessage(args, TEST_ADDRESS);
  const signature = await mockWalletClient.signMessage({ account: mockAccount, message });

  return { message, signature, args };
};

/**
 * Mock Wallet 유틸리티 객체
 * 테스트에서 쉽게 사용할 수 있도록 모든 기능을 하나의 객체로 제공
 */
export const MockWallet = {
  // 상수
  PRIVATE_KEY: TEST_PRIVATE_KEY,
  ADDRESS: TEST_ADDRESS,
  CHAIN_ID: polygonAmoy.id,

  // 클라이언트
  account: mockAccount,
  client: mockWalletClient,

  // 헬퍼 함수
  createSIWEMessage: createTestSIWEMessage,
  createSIWEArgs: createTestSIWEArgs,
  signSIWEMessage: createSignedSIWEMessage,

  // 기본 서명 함수
  signMessage: async (message: string) => {
    return await mockWalletClient.signMessage({ account: mockAccount, message });
  },

  // 주소 검증
  isValidAddress: (address: string) => {
    return address === TEST_ADDRESS;
  },

  // 서명 형식 검증
  isValidSignature: (signature: string) => {
    return /^0x[a-fA-F0-9]{130}$/.test(signature);
  },
} as const;

export default MockWallet;
