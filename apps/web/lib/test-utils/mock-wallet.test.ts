import { describe, it, expect } from 'vitest';
import { MockWallet } from './mock-wallet';
import { polygonAmoy } from 'viem/chains';

describe('MockWallet Test Utility', () => {
  describe('Constants', () => {
    it('should have correct fixed private key', () => {
      expect(MockWallet.PRIVATE_KEY).toBe(
        '0x59c6995e998f97e48234cedc2a5cdcb2b8c5c7a14a5b16ef0b7d5ca5c5d4f5e8'
      );
    });

    it('should have correct derived address', () => {
      expect(MockWallet.ADDRESS).toBe('******************************************');
    });

    it('should have correct chain ID', () => {
      expect(MockWallet.CHAIN_ID).toBe(polygonAmoy.id);
    });
  });

  describe('Address Validation', () => {
    it('should validate correct address', () => {
      expect(MockWallet.isValidAddress(MockWallet.ADDRESS)).toBe(true);
    });

    it('should reject invalid address', () => {
      expect(MockWallet.isValidAddress('0xinvalidaddress')).toBe(false);
    });
  });

  describe('Signature Validation', () => {
    it('should validate correct signature format', async () => {
      const signature = await MockWallet.signMessage('test message');
      expect(MockWallet.isValidSignature(signature)).toBe(true);
    });

    it('should reject invalid signature format', () => {
      expect(MockWallet.isValidSignature('0xinvalid')).toBe(false);
      expect(MockWallet.isValidSignature('invalid')).toBe(false);
    });
  });

  describe('Message Signing', () => {
    it('should sign simple messages', async () => {
      const message = 'Hello, world!';
      const signature = await MockWallet.signMessage(message);

      expect(signature).toMatch(/^0x[a-fA-F0-9]{130}$/);
      expect(MockWallet.isValidSignature(signature)).toBe(true);
    });

    it('should produce consistent signatures for same message', async () => {
      const message = 'test message';
      const signature1 = await MockWallet.signMessage(message);
      const signature2 = await MockWallet.signMessage(message);

      expect(signature1).toBe(signature2);
    });
  });

  describe('SIWE Message Creation', () => {
    it('should create default SIWE arguments', () => {
      const args = MockWallet.createSIWEArgs();

      expect(args).toMatchObject({
        domain: 'localhost:3000',
        address: MockWallet.ADDRESS,
        uri: 'http://localhost:3000',
        version: '1',
        chainId: MockWallet.CHAIN_ID,
        nonce: 'test-nonce-12345',
        statement: 'Please sign with your account',
      });
    });

    it('should allow overriding SIWE arguments', () => {
      const customNonce = 'custom-nonce-456';
      const customDomain = 'example.com';

      const args = MockWallet.createSIWEArgs({
        nonce: customNonce,
        domain: customDomain,
      });

      expect(args.nonce).toBe(customNonce);
      expect(args.domain).toBe(customDomain);
      expect(args.address).toBe(MockWallet.ADDRESS); // Should keep default
    });

    it('should create valid SIWE message string', () => {
      const args = MockWallet.createSIWEArgs();
      const message = MockWallet.createSIWEMessage(args, MockWallet.ADDRESS);

      expect(message).toContain(MockWallet.ADDRESS);
      expect(message).toContain('localhost:3000');
      expect(message).toContain('Please sign with your account');
      expect(message).toContain('test-nonce-12345');
    });
  });

  describe('SIWE Message Signing', () => {
    it('should sign SIWE message with default arguments', async () => {
      const { message, signature, args } = await MockWallet.signSIWEMessage();

      expect(message).toContain(MockWallet.ADDRESS);
      expect(MockWallet.isValidSignature(signature)).toBe(true);
      expect(args.address).toBe(MockWallet.ADDRESS);
    });

    it('should sign SIWE message with custom arguments', async () => {
      const customNonce = 'custom-test-nonce';
      const { message, signature, args } = await MockWallet.signSIWEMessage({
        nonce: customNonce,
      });

      expect(message).toContain(customNonce);
      expect(MockWallet.isValidSignature(signature)).toBe(true);
      expect(args.nonce).toBe(customNonce);
    });

    it('should produce different signatures for different nonces', async () => {
      const { signature: sig1 } = await MockWallet.signSIWEMessage({
        nonce: 'nonce-1',
      });

      const { signature: sig2 } = await MockWallet.signSIWEMessage({
        nonce: 'nonce-2',
      });

      expect(sig1).not.toBe(sig2);
      expect(MockWallet.isValidSignature(sig1)).toBe(true);
      expect(MockWallet.isValidSignature(sig2)).toBe(true);
    });
  });

  describe('Wallet Client Integration', () => {
    it('should have functioning wallet client', () => {
      expect(MockWallet.client).toBeDefined();
      expect(MockWallet.account).toBeDefined();
      expect(MockWallet.account.address).toBe(MockWallet.ADDRESS);
    });

    it('should sign messages through client', async () => {
      const message = 'Direct client test';
      const signature = await MockWallet.client.signMessage({
        account: MockWallet.account,
        message,
      });

      expect(MockWallet.isValidSignature(signature)).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle empty message signing', async () => {
      const signature = await MockWallet.signMessage('');
      expect(MockWallet.isValidSignature(signature)).toBe(true);
    });

    it('should handle Unicode message signing', async () => {
      const unicodeMessage = '🚀 Hello, 世界! 🌍';
      const signature = await MockWallet.signMessage(unicodeMessage);
      expect(MockWallet.isValidSignature(signature)).toBe(true);
    });
  });
});
