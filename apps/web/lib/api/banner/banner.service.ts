import { BannerSchema, type BannerData } from './banner.schema';
import { BaseFetcher } from '@repo/shared/lib/base-fetcher';

export class BannerService extends BaseFetcher {
  static BASE_PATH = '';
  static ROUTES = {
    GET: {
      '/banner.json': 'banner.json',
    },
  };

  constructor() {
    super();
  }

  async getBannerData(): Promise<BannerData> {
    try {
      const endpoint = BannerService.ROUTES.GET['/banner.json'];
      const data = await this.kyInstance.get(`/${endpoint}`).json();

      // safeJsonParse를 사용하여 스키마 검증
      const validatedData = BannerSchema.safeParse(data);

      if (!validatedData.success) {
        throw new Error(`BannerService.getBannerData failed: ${validatedData.error.message}`);
      }

      return validatedData.data;
    } catch (error) {
      console.error(`BannerService.getBannerData failed:`, error);
      throw error;
    }
  }
}

// 싱글톤 인스턴스 생성
export const bannerService = new BannerService();
