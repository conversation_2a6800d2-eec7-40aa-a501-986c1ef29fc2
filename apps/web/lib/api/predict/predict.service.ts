import { ApiClient } from '@/lib/api/base-api';
import { SafeSmartAccount } from '@/lib/types';
import { convertUserOpToBigInt, createPaymasterAndData } from '@/lib/utils';
import { type Hex } from 'viem';
import { UserOperation } from 'viem/account-abstraction';
import { z } from 'zod';
import {
  ClaimRequestBody,
  DepositChannelCollateralRequestBody,
  DepositDisputeCollateralRequestBody,
  FinalizeRequestBody,
  GoResponseSchema,
  PredictRequestBody,
  PredictRequestBodySchema,
  RedeemRequestBody,
  UserOperationRequest,
  UserOperationSchema,
  VoidRequestBody,
  WithdrawChannelCollateralRequestBody,
  WithdrawRequestBody,
} from './predict.schema';
import { ApiError } from '../base-api.error';
import { toAmount } from '@/lib/format';

export class PredictService {
  static BASE_PATH = '/data-api/v1/predict';

  static ROUTES = {
    POST: {
      '/claim': 'claim',
      '/deposit-channel-collateral': 'deposit-channel-collateral',
      '/deposit-dispute-collateral': 'deposit-dispute-collateral',
      '/finalize': 'finalize',
      '/go': 'go',
      '/predict': 'predict',
      '/redeem': 'redeem',
      '/void': 'void',
      '/withdraw': 'withdraw',
      '/withdraw-channel-collateral': 'withdraw-channel-collateral',
    },
  };
  private api: ApiClient;

  constructor() {
    this.api = new ApiClient(PredictService.BASE_PATH);
  }

  /**
   * 예측 보상 청구
   */
  async claim(data: ClaimRequestBody) {
    const response = await this.api.post(PredictService.ROUTES.POST['/claim'], {
      json: data,
    });
    const result = z.any().safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        PredictService.ROUTES.POST['/claim'],
        response
      );
    }
    return result.data;
  }

  /**
   * 채널 담보 예치
   */
  async depositChannelCollateral(data: DepositChannelCollateralRequestBody) {
    const response = await this.api.post(
      PredictService.ROUTES.POST['/deposit-channel-collateral'],
      {
        json: data,
      }
    );
    const result = z.any().safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        PredictService.ROUTES.POST['/deposit-channel-collateral'],
        response
      );
    }
    return result.data;
  }

  /**
   * 분쟁 담보 예치
   */
  async depositDisputeCollateral(data: DepositDisputeCollateralRequestBody) {
    const formData = new FormData();

    formData.append('marketId', data.marketId);
    formData.append('amount', data.amount.toString());

    if (data.description) {
      formData.append('description', data.description);
    }

    if (data.referenceURL) {
      formData.append('referenceURL', data.referenceURL);
    }

    if (data.fileURLs && data.fileURLs.length > 0) {
      data.fileURLs.forEach(url => {
        formData.append('fileURLs', url);
      });
    }

    const response = await this.api.post(
      PredictService.ROUTES.POST['/deposit-dispute-collateral'],
      {
        body: formData,
      }
    );

    const result = z.any().safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        PredictService.ROUTES.POST['/deposit-dispute-collateral'],
        response
      );
    }
    return result.data;
  }

  /**
   * 마켓 확정
   */
  async finalize(data: FinalizeRequestBody) {
    const response = await this.api.post(PredictService.ROUTES.POST['/finalize'], {
      json: data,
    });

    const result = UserOperationSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        PredictService.ROUTES.POST['/finalize'],
        response
      );
    }
    return result.data;
  }

  /**
   * 사용자 작업 실행
   */
  async go(data: UserOperationRequest) {
    const response = await this.api.post(PredictService.ROUTES.POST['/go'], {
      json: data,
    });

    const result = GoResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(result.error, PredictService.ROUTES.POST['/go'], response);
    }
    return result.data;
  }

  /**
   * 예측 실행
   */
  async predict(data: unknown) {
    const parsed = PredictRequestBodySchema.safeParse(data);

    if (!parsed.success) {
      throw ApiError.fromValidationError(
        parsed.error,
        PredictService.ROUTES.POST['/predict'],
        data
      );
    }

    const response = await this.api.post(PredictService.ROUTES.POST['/predict'], {
      json: parsed.data,
    });

    const result = z.any().safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        PredictService.ROUTES.POST['/predict'],
        response
      );
    }
    return result.data;
  }

  /**
   * 토큰 상환
   */
  async redeem(data: RedeemRequestBody) {
    const response = await this.api.post(PredictService.ROUTES.POST['/redeem'], {
      json: data,
    });

    const result = UserOperationSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        PredictService.ROUTES.POST['/redeem'],
        response
      );
    }
    return result.data;
  }

  /**
   * 마켓 무효화
   */
  async void(data: VoidRequestBody) {
    const response = await this.api.post(PredictService.ROUTES.POST['/void'], {
      json: data,
    });

    const result = UserOperationSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        PredictService.ROUTES.POST['/void'],
        response
      );
    }
    return result.data;
  }

  /**
   * 토큰 인출
   */
  async withdraw(data: WithdrawRequestBody) {
    const response = await this.api.post(PredictService.ROUTES.POST['/withdraw'], {
      json: data,
    });

    const result = UserOperationSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        PredictService.ROUTES.POST['/withdraw'],
        response
      );
    }
    return result.data;
  }

  /**
   * 채널 담보 인출
   */
  async withdrawChannelCollateral(data: WithdrawChannelCollateralRequestBody) {
    const response = await this.api.post(
      PredictService.ROUTES.POST['/withdraw-channel-collateral'],
      {
        json: data,
      }
    );

    const result = UserOperationSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        PredictService.ROUTES.POST['/withdraw-channel-collateral'],
        response
      );
    }
    return result.data;
  }

  /**
   * 예측과 UserOperation 실행을 한 번에 처리하는 편의 메서드
   */
  async predictAndGo(safeSmartAccount: SafeSmartAccount, predictData: PredictRequestBody) {
    try {
      // 1. 예측 UserOperation 생성
      const userOp = await this.predict(predictData);

      // 2. BigInt로 변환
      const userOpForSigning = convertUserOpToBigInt(userOp);

      // 3. paymasterAndData 생성
      const paymasterAndData = createPaymasterAndData(userOpForSigning);

      // 4. 서명
      const signature = await safeSmartAccount.signUserOperation({
        ...userOpForSigning,
        paymasterAndData,
      } as unknown as UserOperation);

      // 5. 서명된 UserOperation으로 실행
      const signedUserOp = { ...userOp, signature: signature as Hex };
      return await this.go(signedUserOp);
    } catch (error) {
      console.error('Predict and go error:', error);
      throw error;
    }
  }

  /**
   * 청구와 UserOperation 실행을 한 번에 처리하는 편의 메서드
   */
  async claimAndGo(safeSmartAccount: SafeSmartAccount, claimData: ClaimRequestBody) {
    try {
      // 1. 청구 UserOperation 생성
      const userOp = await this.claim(claimData);

      // 2. BigInt로 변환
      const userOpForSigning = convertUserOpToBigInt(userOp);

      // 3. paymasterAndData 생성
      const paymasterAndData = createPaymasterAndData(userOpForSigning);

      // 4. 서명
      const signature = await safeSmartAccount.signUserOperation({
        ...userOpForSigning,
        paymasterAndData,
      } as unknown as UserOperation);

      // 5. 서명된 UserOperation으로 실행
      const signedUserOp = { ...userOp, signature: signature as Hex };
      return await this.go(signedUserOp);
    } catch (error) {
      console.error('Claim and go error:', error);
      throw error;
    }
  }

  /**
   * 채널 담보 예치와 UserOperation 실행을 한 번에 처리하는 편의 메서드
   */
  async depositChannelCollateralAndGo(
    safeSmartAccount: SafeSmartAccount,
    data: DepositChannelCollateralRequestBody
  ) {
    try {
      const req = {
        ...data,
        amount: toAmount(data.amount),
      };

      const userOp = await this.depositChannelCollateral(req);
      const userOpForSigning = convertUserOpToBigInt(userOp);
      const paymasterAndData = createPaymasterAndData(userOpForSigning);
      const signature = await safeSmartAccount.signUserOperation({
        ...userOpForSigning,
        paymasterAndData,
      } as unknown as UserOperation);

      const signedUserOp = { ...userOp, signature: signature as Hex };
      return await this.go(signedUserOp);
    } catch (error) {
      console.error('Deposit channel collateral and go error:', error);
      throw error;
    }
  }

  /**
   * 분쟁 담보 예치와 UserOperation 실행을 한 번에 처리하는 편의 메서드
   */
  async depositDisputeCollateralAndGo(
    safeSmartAccount: SafeSmartAccount,
    disputeCollateralData: DepositDisputeCollateralRequestBody
  ) {
    try {
      // 1. 분쟁 담보 예치 UserOperation 생성
      const userOp = await this.depositDisputeCollateral(disputeCollateralData);

      // 2. BigInt로 변환
      const userOpForSigning = convertUserOpToBigInt(userOp);

      // 3. paymasterAndData 생성
      const paymasterAndData = createPaymasterAndData(userOpForSigning);

      // 4. 서명
      const signature = await safeSmartAccount.signUserOperation({
        ...userOpForSigning,
        paymasterAndData,
      } as unknown as UserOperation);

      // 5. 서명된 UserOperation으로 실행
      const signedUserOp = { ...userOp, signature: signature as Hex };
      return await this.go(signedUserOp);
    } catch (error) {
      console.error('Deposit dispute collateral and go error:', error);
      throw error;
    }
  }

  /**
   * 마켓 확정과 UserOperation 실행을 한 번에 처리하는 편의 메서드
   */
  async finalizeAndGo(safeSmartAccount: SafeSmartAccount, finalizeData: FinalizeRequestBody) {
    try {
      // 1. 마켓 확정 UserOperation 생성
      const userOp = await this.finalize(finalizeData);

      // 2. BigInt로 변환
      const userOpForSigning = convertUserOpToBigInt(userOp);

      // 3. paymasterAndData 생성
      const paymasterAndData = createPaymasterAndData(userOpForSigning);

      // 4. 서명
      const signature = await safeSmartAccount.signUserOperation({
        ...userOpForSigning,
        paymasterAndData,
      } as unknown as UserOperation);

      // 5. 서명된 UserOperation으로 실행 (bigint를 number로 변환)
      const signedUserOp = {
        ...userOp,
        signature: signature as Hex,
        maxFeePerGas: Number(userOp.maxFeePerGas),
        maxPriorityFeePerGas: Number(userOp.maxPriorityFeePerGas),
        nonce: Number(userOp.nonce),
        callGasLimit: Number(userOp.callGasLimit),
        preVerificationGas: Number(userOp.preVerificationGas),
        verificationGasLimit: Number(userOp.verificationGasLimit),
        paymasterPostOpGasLimit: userOp.paymasterPostOpGasLimit
          ? Number(userOp.paymasterPostOpGasLimit)
          : undefined,
        paymasterVerificationGasLimit: userOp.paymasterVerificationGasLimit
          ? Number(userOp.paymasterVerificationGasLimit)
          : undefined,
      };
      return await this.go(signedUserOp);
    } catch (error) {
      console.error('Finalize and go error:', error);
      throw error;
    }
  }

  /**
   * 토큰 상환과 UserOperation 실행을 한 번에 처리하는 편의 메서드
   */
  async redeemAndGo(safeSmartAccount: SafeSmartAccount, redeemData: RedeemRequestBody) {
    try {
      // 1. 토큰 상환 UserOperation 생성
      const userOp = await this.redeem(redeemData);

      // 2. BigInt로 변환
      const userOpForSigning = convertUserOpToBigInt(userOp);

      // 3. paymasterAndData 생성
      const paymasterAndData = createPaymasterAndData(userOpForSigning);

      // 4. 서명
      const signature = await safeSmartAccount.signUserOperation({
        ...userOpForSigning,
        paymasterAndData,
      } as unknown as UserOperation);

      // 5. 서명된 UserOperation으로 실행 (bigint를 number로 변환)
      const signedUserOp = {
        ...userOp,
        signature: signature as Hex,
        maxFeePerGas: Number(userOp.maxFeePerGas),
        maxPriorityFeePerGas: Number(userOp.maxPriorityFeePerGas),
        nonce: Number(userOp.nonce),
        callGasLimit: Number(userOp.callGasLimit),
        preVerificationGas: Number(userOp.preVerificationGas),
        verificationGasLimit: Number(userOp.verificationGasLimit),
        paymasterPostOpGasLimit: userOp.paymasterPostOpGasLimit
          ? Number(userOp.paymasterPostOpGasLimit)
          : undefined,
        paymasterVerificationGasLimit: userOp.paymasterVerificationGasLimit
          ? Number(userOp.paymasterVerificationGasLimit)
          : undefined,
      };
      return await this.go(signedUserOp);
    } catch (error) {
      console.error('Redeem and go error:', error);
      throw error;
    }
  }

  /**
   * 마켓 무효화와 UserOperation 실행을 한 번에 처리하는 편의 메서드
   */
  async voidAndGo(safeSmartAccount: SafeSmartAccount, voidData: VoidRequestBody) {
    try {
      // 1. 마켓 무효화 UserOperation 생성
      const userOp = await this.void(voidData);

      // 2. BigInt로 변환
      const userOpForSigning = convertUserOpToBigInt(userOp);

      // 3. paymasterAndData 생성
      const paymasterAndData = createPaymasterAndData(userOpForSigning);

      // 4. 서명
      const signature = await safeSmartAccount.signUserOperation({
        ...userOpForSigning,
        paymasterAndData,
      } as unknown as UserOperation);

      // 5. 서명된 UserOperation으로 실행 (bigint를 number로 변환)
      const signedUserOp = {
        ...userOp,
        signature: signature as Hex,
        maxFeePerGas: Number(userOp.maxFeePerGas),
        maxPriorityFeePerGas: Number(userOp.maxPriorityFeePerGas),
        nonce: Number(userOp.nonce),
        callGasLimit: Number(userOp.callGasLimit),
        preVerificationGas: Number(userOp.preVerificationGas),
        verificationGasLimit: Number(userOp.verificationGasLimit),
        paymasterPostOpGasLimit: userOp.paymasterPostOpGasLimit
          ? Number(userOp.paymasterPostOpGasLimit)
          : undefined,
        paymasterVerificationGasLimit: userOp.paymasterVerificationGasLimit
          ? Number(userOp.paymasterVerificationGasLimit)
          : undefined,
      };
      return await this.go(signedUserOp);
    } catch (error) {
      console.error('Void and go error:', error);
      throw error;
    }
  }

  /**
   * 토큰 인출과 UserOperation 실행을 한 번에 처리하는 편의 메서드
   */
  async withdrawAndGo(safeSmartAccount: SafeSmartAccount, withdrawData: WithdrawRequestBody) {
    try {
      // 1. 토큰 인출 UserOperation 생성
      const userOp = await this.withdraw(withdrawData);

      // 2. BigInt로 변환
      const userOpForSigning = convertUserOpToBigInt(userOp);

      // 3. paymasterAndData 생성
      const paymasterAndData = createPaymasterAndData(userOpForSigning);

      // 4. 서명
      const signature = await safeSmartAccount.signUserOperation({
        ...userOpForSigning,
        paymasterAndData,
      } as unknown as UserOperation);

      // 5. 서명된 UserOperation으로 실행 (bigint를 number로 변환)
      const signedUserOp = {
        ...userOp,
        signature: signature as Hex,
        maxFeePerGas: Number(userOp.maxFeePerGas),
        maxPriorityFeePerGas: Number(userOp.maxPriorityFeePerGas),
        nonce: Number(userOp.nonce),
        callGasLimit: Number(userOp.callGasLimit),
        preVerificationGas: Number(userOp.preVerificationGas),
        verificationGasLimit: Number(userOp.verificationGasLimit),
        paymasterPostOpGasLimit: userOp.paymasterPostOpGasLimit
          ? Number(userOp.paymasterPostOpGasLimit)
          : undefined,
        paymasterVerificationGasLimit: userOp.paymasterVerificationGasLimit
          ? Number(userOp.paymasterVerificationGasLimit)
          : undefined,
      };
      return await this.go(signedUserOp);
    } catch (error) {
      console.error('Withdraw and go error:', error);
      throw error;
    }
  }

  /**
   * 채널 담보 인출과 UserOperation 실행을 한 번에 처리하는 편의 메서드
   */
  async withdrawChannelCollateralAndGo(
    safeSmartAccount: SafeSmartAccount,
    data: WithdrawChannelCollateralRequestBody
  ) {
    try {
      const req = {
        ...data,
        amount: toAmount(data.amount),
      };

      const userOp = await this.withdrawChannelCollateral(req);

      const userOpForSigning = convertUserOpToBigInt(userOp);
      const paymasterAndData = createPaymasterAndData(userOpForSigning);

      const signature = await safeSmartAccount.signUserOperation({
        ...userOpForSigning,
        paymasterAndData,
      } as unknown as UserOperation);

      const signedUserOp = {
        ...userOp,
        signature: signature as Hex,
        maxFeePerGas: Number(userOp.maxFeePerGas),
        maxPriorityFeePerGas: Number(userOp.maxPriorityFeePerGas),
        nonce: Number(userOp.nonce),
        callGasLimit: Number(userOp.callGasLimit),
        preVerificationGas: Number(userOp.preVerificationGas),
        verificationGasLimit: Number(userOp.verificationGasLimit),
        paymasterPostOpGasLimit: userOp.paymasterPostOpGasLimit
          ? Number(userOp.paymasterPostOpGasLimit)
          : undefined,
        paymasterVerificationGasLimit: userOp.paymasterVerificationGasLimit
          ? Number(userOp.paymasterVerificationGasLimit)
          : undefined,
      };
      return await this.go(signedUserOp);
    } catch (error) {
      console.error('Withdraw channel collateral and go error:', error);
      throw error;
    }
  }
}

export const predictService = new PredictService();
