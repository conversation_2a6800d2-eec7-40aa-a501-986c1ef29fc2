import { z } from 'zod';
import { toAmount } from '@/lib/format';

const UserOperationDto = z
  .object({
    paymaster: z.unknown().optional(),
    sender: z.unknown(),
    callData: z.unknown(),
    signature: z.unknown(),
    paymasterData: z.unknown().optional(),
    maxFeePerGas: z.number().int(),
    maxPriorityFeePerGas: z.number().int(),
    nonce: z.number().int(),
    paymasterPostOpGasLimit: z.number().int().optional(),
    callGasLimit: z.number().int(),
    preVerificationGas: z.number().int(),
    verificationGasLimit: z.number().int(),
    paymasterVerificationGasLimit: z.number().int().optional(),
    factory: z.unknown().optional(),
    factoryData: z.unknown().optional(),
  })
  .passthrough();
const GoResDto = z.object({ hash: z.string() }).passthrough();

export const ClaimRequestBodySchema = z.discriminatedUnion('distributionType', [
  z.object({
    distributionType: z.literal(2),
    category: z.enum(['ChannelLeader', 'Maker', 'PlatformFee']),
  }),
  z.object({
    distributionType: z.literal(1),
    category: z.enum(['Channel', 'ChannelDispute', 'Dispute']),
  }),
  z.object({
    distributionType: z.literal(0),
    category: z.enum(['CommissionReward', 'FeeRebate', 'Share']),
  }),
]);
export type ClaimRequestBody = z.infer<typeof ClaimRequestBodySchema>;

export const DepositChannelCollateralRequestBodySchema = z.object({
  amount: z.string(),
});

export const DepositDisputeCollateralRequestBodySchema = z.object({
  marketId: z.string().min(66).max(66),
  amount: z.string(),
  description: z.string().max(1000).optional(),
  referenceURL: z.string().optional(),
  files: z.array(z.instanceof(File)).max(3).optional(),
});

export const FinalizeRequestBodySchema = z.object({
  marketId: z.string().min(66).max(66),
});

export const PredictRequestBodySchema = z.object({
  marketId: z.string(),
  outcome: z.string(),
  amount: z.string().transform(val => toAmount(val).toString()),
});

export const RedeemRequestBodySchema = z.object({
  marketIds: z.array(z.string()).min(1).max(20),
});

export const VoidRequestBodySchema = z.any();

export const WithdrawRequestBodySchema = z.object({
  to: z.string(),
  amount: z.string(),
});

export const WithdrawChannelCollateralRequestBodySchema = z.object({
  amount: z.string(),
});

export const UserOperationRequestSchema = UserOperationDto;

// Response DTOs
// export const UserOperationSchema = schemas.UserOperationDto;

export const UserOperationSchema = z.object({
  paymaster: z.unknown().optional(),
  sender: z.unknown(),
  callData: z.unknown(),
  signature: z.unknown(),
  paymasterData: z.unknown().optional(),
  maxFeePerGas: z.coerce.bigint(),
  maxPriorityFeePerGas: z.coerce.bigint(),
  nonce: z.coerce.bigint(),
  paymasterPostOpGasLimit: z.coerce.bigint().optional(),
  callGasLimit: z.coerce.bigint(),
  preVerificationGas: z.coerce.bigint(),
  verificationGasLimit: z.coerce.bigint(),
  paymasterVerificationGasLimit: z.coerce.bigint().optional(),
  factory: z.unknown().optional(),
  factoryData: z.unknown().optional(),
});

export const GoResponseSchema = GoResDto;

// Type exports

export type DepositChannelCollateralRequestBody = z.infer<
  typeof DepositChannelCollateralRequestBodySchema
>;
export type DepositDisputeCollateralRequestBody = z.infer<
  typeof DepositDisputeCollateralRequestBodySchema
>;
export type FinalizeRequestBody = z.infer<typeof FinalizeRequestBodySchema>;
export type PredictRequestBody = z.infer<typeof PredictRequestBodySchema>;
export type RedeemRequestBody = z.infer<typeof RedeemRequestBodySchema>;
export type VoidRequestBody = z.infer<typeof VoidRequestBodySchema>;
export type WithdrawRequestBody = z.infer<typeof WithdrawRequestBodySchema>;
export type WithdrawChannelCollateralRequestBody = z.infer<
  typeof WithdrawChannelCollateralRequestBodySchema
>;
export type UserOperationRequest = z.infer<typeof UserOperationRequestSchema>;
export type UserOperation = z.infer<typeof UserOperationSchema>;
export type GoResponse = z.infer<typeof GoResponseSchema>;
