import { DEFAULT_MARKET_AVATAR_URL, MARKET_STATUS_TEXT_MAP } from '@/lib/constants';
import type {
  GetMarketsResponseType,
  MarketActivitiesResponseSchema,
  MarketActivitySchema,
  MarketServerSchema,
  MarketTopPredictorsResponseSchema,
} from './market.schema.server';
import { formatUsdc } from '@/lib/format';
import { toRelativeTime } from '@/lib/utils';
import BigNumber from 'bignumber.js';

export const transformMarket = (data: MarketServerSchema) => {
  return {
    ...data,
    imageUrl: data.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    channel: {
      ...data.channel,
      imageUrl: data.channel.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    },
    outcomes: data.outcomes.map(outcome => ({
      ...outcome,
      formattedVolume: formatUsdc(outcome.volume),
      rawVolume: BigNumber(outcome.volume),
    })),
    formattedTotalVolume: formatUsdc(data.totalVolume),
    rawTotalVolume: BigNumber(data.totalVolume),
    formattedCollateralAmount: formatUsdc(data.collateralAmount),
    rawCollateralAmount: BigNumber(data.collateralAmount),
    formattedMaxOutcomeVolume: formatUsdc(data.maxOutcomeVolume),
    rawMaxOutcomeVolume: BigNumber(data.maxOutcomeVolume),
    statusText: MARKET_STATUS_TEXT_MAP[data.status],
    predictionDeadline: new Date(data.predictionDeadline).getTime(),
    resultConfirmDeadline: new Date(data.resultConfirmDeadline).getTime(),
    outcomeProposedAt: data.outcomeProposedAt ? new Date(data.outcomeProposedAt).getTime() : null,
    disputedAt: data.disputedAt ? new Date(data.disputedAt).getTime() : null,
    finalizedAt: data.finalizedAt ? new Date(data.finalizedAt).getTime() : null,
    createdAt: new Date(data.createdAt).getTime(),
    disputeDeadline: data.disputeDeadline ? new Date(data.disputeDeadline).getTime() : null,
  };
};

export type MarketOutcome = ReturnType<typeof transformMarket>['outcomes'][number];

export const transformGetMarketsResponse = (data: GetMarketsResponseType) => {
  return {
    ...data,
    markets: data.markets.map(transformMarket),
  };
};

export const transformMarketTopPredictorsResponse = (data: MarketTopPredictorsResponseSchema) => {
  return {
    ...data,
    topPredictors: data.topPredictors.map(predictor => ({
      ...predictor,
      imageUrl: predictor.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
      formattedAmount: formatUsdc(predictor.amount),
      rawAmount: BigNumber(predictor.amount),
    })),
  };
};

const transformMarketActivity = (data: MarketActivitySchema) => {
  return {
    ...data,
    market: {
      ...data.market,
      imageUrl: data.market.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    },
    user: {
      ...data.user,
      imageUrl: data.user.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    },
    formattedAmount: formatUsdc(data.amount),
    rawAmount: BigNumber(data.amount),
    relativeTime: toRelativeTime(data.timestamp) + ' ago',
  };
};

export const transformMarketActivitiesResponse = (data: MarketActivitiesResponseSchema) => {
  return {
    ...data,
    activities: data.activities.map(transformMarketActivity),
  };
};
