import { z } from 'zod';

export const ValidateRequestBodySchema = z.object({
  title: z.string(),
});

export type ValidateRequestBody = z.infer<typeof ValidateRequestBodySchema>;

export const ValidateResponseSchema = z.object({
  isEthical: z.boolean(),
  validationId: z.string().optional(),
});

export const CreateMarketRequestBodySchema = z.object({
  channelId: z.string(),
  title: z.string().min(2).max(100),
  description: z.string().min(2).max(2000),
  predictionDeadline: z.number().describe('ms'),
  resultConfirmDeadline: z.number().describe('ms'),
  disputedPeriod: z.string().describe('30m'),
  category: z.string().min(2).max(50),
  collateralAmount: z.coerce.string(),
  outcomes: z.array(z.string().min(2).max(50)).min(2).max(10),
  tags: z.array(z.string().min(2).max(50)).max(3).optional().default([]),
  broadcastURL: z.string().optional(),
  referenceURL: z.string(),
  image: z.instanceof(File).optional(),
  signature: z.string(),
  validationId: z.string(),
});

export type CreateMarketRequestBody = z.infer<typeof CreateMarketRequestBodySchema>;
export const ProposeRequestBodySchema = z.object({
  marketId: z.string().min(66).max(66),
  outcome: z.string().min(2).max(50),
  signature: z.string(),
});
export type ProposeRequestBody = z.infer<typeof ProposeRequestBodySchema>;
export const SearchMarketsRequestQuerySchema = z.object({
  query: z.string().min(2).max(50),
  page: z.number().int().gte(0).optional().default(0),
  limit: z.number().int().gt(0).lte(50).optional().default(50),
});

const GetMarketsOrderSchema = z.enum(['VOLUME', 'NEWEST', 'ENDING_SOON', 'COMPETITIVE']);

export type GetMarketsOrderEnum = z.infer<typeof GetMarketsOrderSchema>;

export const GetMarketsRequestQuerySchema = z.object({
  page: z.number().default(0).optional(),
  limit: z.number().default(50).optional(),
  order: GetMarketsOrderSchema.default('VOLUME').optional(),
});

export const MarketStatusTextEnum = z.enum([
  'Live',
  'Awaiting Result',
  'Disputable',
  'Under Review',
  'Resolved',
]);

export type MarketStatusText = z.infer<typeof MarketStatusTextEnum>;

export const MarketStatusEnumSchema = z.enum([
  'OPEN',
  'REVIEWING',
  'DISPUTABLE',
  'DISPUTED',
  'CLOSED_WITHOUT_DISPUTE',
  'CLOSED_WITH_DISPUTE_ACCEPTED',
  'CLOSED_WITH_DISPUTE_REJECTED',
  'CANCELLED_WITH_UNMET',
  'CANCELLED_WITH_INVALID',
  'CANCELLED_WITH_OUTCOME_NOT_PROPOSED',
]);

export type MarketStatusEnum = z.infer<typeof MarketStatusEnumSchema>;

export const GetMarketsWithStatusRequestQuerySchema = GetMarketsRequestQuerySchema.extend({
  statusText: z.array(MarketStatusTextEnum).optional(),
});

export const MarketSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  imageUrl: z.string().optional(),
  channel: z.object({
    id: z.string(),
    name: z.string(),
    imageUrl: z.string().nullable(),
  }),
  maker: z.string(),
  category: z.string().min(2).max(50),
  minPredictCount: z.number(),
  collateralAmount: z.string().describe('usdc'),
  totalVolume: z.coerce.bigint().describe('usdc'),
  competitive: z.number(),
  broadcastURL: z.string().optional(),
  referenceURL: z.string(),
  predictionDeadline: z.string(),
  resultConfirmDeadline: z.string(),
  disputeDeadline: z.string().optional(),
  proposedOutcome: z.string().optional(),
  finalOutcome: z.string().optional(),
  outcomeProposedAt: z.string().optional(),
  disputedAt: z.string().optional(),
  finalizedAt: z.string().optional(),
  createdAt: z.string(),
  totalPredictor: z.number(),
  predictCount: z.number(),
  outcomes: z
    .array(
      z.object({
        marketId: z.string(),
        outcome: z.string(),
        order: z.number(),
        volume: z.string().describe('usdc'),
      })
    )
    .min(2),
  maxOutcomeVolume: z.string().describe('usdc'),
  status: MarketStatusEnumSchema,
});

export type MarketOutcomeServer = z.infer<typeof MarketSchema>['outcomes'][number];
export type MarketServerSchema = z.infer<typeof MarketSchema>;

export const GetMarketsResponseSchema = z.object({
  markets: z.array(MarketSchema),
  totalLength: z.number(),
});

export const MarketBriefInfosResponseSchema = z.object({
  markets: z.array(
    z.object({
      id: z.string(),
      title: z.string(),
      imageUrl: z.string().optional(),
    })
  ),
});
export type MarketBriefInfosResponseSchema = z.infer<typeof MarketBriefInfosResponseSchema>;

export type GetMarketsResponseType = z.infer<typeof GetMarketsResponseSchema>;

export const MarketTopPredictorsResponseSchema = z.object({
  marketId: z.string(),
  outcome: z.string(),
  topPredictors: z.array(
    z.object({
      address: z.string(),
      nickname: z.string(),
      imageUrl: z.string().optional().nullable(),
      // amount: z.string().transform(val => formatUsdc(val)),
      amount: z.string().describe('usdc'),
    })
  ),
  totalLength: z.number(),
});

export type MarketTopPredictorsResponseSchema = z.infer<typeof MarketTopPredictorsResponseSchema>;

export const MarketActivitiesResponseSchema = z.object({
  activities: z.array(
    z.object({
      market: z.object({ id: z.string(), title: z.string(), imageUrl: z.string().nullable() }),
      user: z.object({
        address: z.string(),
        nickname: z.string(),
        imageUrl: z.string().nullable(),
      }),
      outcomes: z.array(z.object({ outcome: z.string(), outcomeOrder: z.number() })),
      amount: z.string().describe('usdc'),
      timestamp: z.string(),
      type: z.string(),
    })
  ),
  totalLength: z.number(),
});
export type MarketActivitiesResponseSchema = z.infer<typeof MarketActivitiesResponseSchema>;

export type MarketActivitySchema = z.infer<
  typeof MarketActivitiesResponseSchema
>['activities'][number];

export const ProposeResponseSchema = z.object({
  success: z.boolean(),
});

export type GetMarketsRequestQuery = z.infer<typeof GetMarketsRequestQuerySchema>;

export type GetMarketsWithStatusRequestQuery = Partial<
  z.infer<typeof GetMarketsWithStatusRequestQuerySchema>
>;
