import { ApiClient } from '@/lib/api/base-api';
import {
  CreateMarketRequestBody,
  GetMarketsRequestOptions,
  MarketActivitiesResponseSchema,
  MarketSchema,
  ProposeRequestBody,
  ProposeResponseSchema,
  MarketTopPredictorsResponseSchema,
  ValidateResponseSchema,
  ValidateRequestBody,
  MarketStatusEnum,
  GetMarketsResponseSchema,
  MarketBriefInfosResponseSchema,
  GetMarketsByCategoryRequestOptions,
} from './market.schema.server';
import { ApiError } from '../base-api.error';
import {
  transformGetMarketsResponse,
  transformMarket,
  transformMarketActivitiesResponse,
  transformMarketTopPredictorsResponse,
} from './market.transform';

export class MarketService {
  static BASE_PATH = '/data-api/v1/market';
  static ROUTES = {
    GET: {
      '/': '',
      '/{marketId}': (marketId: string) => `${marketId}`,
      '/activities/{marketId}/{outcome}': (marketId: string, outcome: string) =>
        `activities/${marketId}/${outcome}`,
      '/search': 'search',
      '/top-predictors/{marketId}/{outcome}': (marketId: string, outcome: string) =>
        `top-predictors/${marketId}/${outcome}`,
      '/category/{category}': (category: string) => `category/${category}`,
    },
    POST: {
      '/': '',
      '/propose': 'propose',
      '/validate': 'validate',
    },
  };
  private api: ApiClient;

  constructor() {
    this.api = new ApiClient(MarketService.BASE_PATH);
  }

  /**
   *
   * GET /v1/market/{marketId}
   * 마켓 ID로 특정 마켓 조회
   */
  async getMarketById(marketId: string) {
    const response = await this.api.get(MarketService.ROUTES.GET['/{marketId}'](marketId));
    const result = MarketSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        MarketService.ROUTES.GET['/{marketId}'](marketId),
        response
      );
    }

    return transformMarket(result.data);
  }

  /**
   * GET /v1/market
   * 마켓 목록 조회
   *
   * @param options - 조회 옵션
   * @param options.page - 페이지 번호 (기본값: 0)
   * @param options.limit - 페이지당 항목 수 (기본값: 50)
   * @param options.order - 정렬 순서 (VOLUME, NEWEST, ENDING_SOON, COMPETITIVE)
   * @param options.filter - 필터 조건 (LIVE, DISPUTABLE)
   * @returns 변환된 마켓 목록 데이터
   */
  async getMarkets(options?: GetMarketsRequestOptions) {
    const searchParams = new URLSearchParams();
    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());
    if (options?.order !== undefined) searchParams.set('order', options.order);
    if (options?.filter !== undefined) searchParams.set('filter', options.filter);

    const response = await this.api.get(MarketService.ROUTES.GET['/'], { searchParams });

    const result = GetMarketsResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(result.error, MarketService.ROUTES.GET['/'], response);
    }

    return transformGetMarketsResponse(result.data);
  }

  /**
   * GET /v1/market?page=1&limit=10 with client-side status filtering
   * 마켓 상태별로 필터링된 마켓 조회 (클라이언트 사이드 필터링)
   */
  async getMarketsWithStatusFilter(
    status: MarketStatusEnum[] | undefined,
    options?: GetMarketsRequestOptions
  ) {
    const result = await this.getMarkets(options);

    if (!status || status.length === 0) {
      return result;
    }

    const filteredMarkets = result.markets.filter(market => {
      return status.includes(market.status as MarketStatusEnum);
    });

    return {
      ...result,
      markets: filteredMarkets,
    };
  }

  /**
   * GET /v1/market with client-side 'ALL' filtering
   * 'ALL' 필터: 취소된 마켓 중 일부를 제외하고 모든 마켓 조회
   * 제외되는 상태: 'CANCELLED_WITH_UNMET', 'CANCELLED_WITH_OUTCOME_NOT_PROPOSED'
   * 주의: filter가 'ALL'인 경우에만 서버에 filter를 전달하지 않음
   */
  async getMarketsWithAll(options?: GetMarketsRequestOptions) {
    const { filter, ...otherOptions } = options || {};
    const serverOptions = filter === 'ALL' ? otherOptions : options;

    const result = await this.getMarkets(serverOptions);

    // 'ALL' 필터에서 제외할 상태들 (Set 사용으로 성능 최적화)
    const excludedStatuses = new Set<MarketStatusEnum>([
      'CANCELLED_WITH_UNMET',
      'CANCELLED_WITH_OUTCOME_NOT_PROPOSED',
    ]);

    const filteredMarkets = result.markets.filter(market => {
      return !excludedStatuses.has(market.status as MarketStatusEnum);
    });

    return {
      ...result,
      markets: filteredMarkets,
    };
  }

  async getMarketByCategory(category: string, options?: GetMarketsByCategoryRequestOptions) {
    const searchParams = new URLSearchParams();
    const { page, limit, order, tag, filter } = options || {};

    if (page !== undefined) searchParams.set('page', page.toString());
    if (limit !== undefined) searchParams.set('limit', limit.toString());
    if (order !== undefined) searchParams.set('order', order);
    if (tag !== undefined) searchParams.set('tag', tag);
    if (filter !== undefined && filter !== 'ALL') searchParams.set('filter', filter);

    const response = await this.api.get(
      MarketService.ROUTES.GET['/category/{category}'](category),
      {
        searchParams,
      }
    );

    const result = GetMarketsResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        MarketService.ROUTES.GET['/category/{category}'](category),
        response
      );
    }

    const excludedStatuses = new Set<MarketStatusEnum>([
      'CANCELLED_WITH_UNMET',
      'CANCELLED_WITH_OUTCOME_NOT_PROPOSED',
    ]);

    const filteredMarkets = result.data.markets.filter(market => {
      return !excludedStatuses.has(market.status);
    });

    return transformGetMarketsResponse({
      totalLength: result.data.totalLength,
      markets: filteredMarkets,
    });
  }

  async getMarketActivities(
    marketId: string,
    outcome: string,
    options?: { page?: number; limit?: number }
  ) {
    const searchParams = new URLSearchParams();
    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());

    const response = await this.api.get(
      MarketService.ROUTES.GET['/activities/{marketId}/{outcome}'](marketId, outcome),
      { searchParams }
    );
    const result = MarketActivitiesResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        MarketService.ROUTES.GET['/activities/{marketId}/{outcome}'](marketId, outcome),
        response
      );
    }
    return transformMarketActivitiesResponse(result.data);
  }

  async proposeMarket(data: ProposeRequestBody) {
    const response = await this.api.post(MarketService.ROUTES.POST['/propose'], {
      json: data,
    });
    const result = ProposeResponseSchema.safeParse(response);
    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        MarketService.ROUTES.POST['/propose'],
        response
      );
    }
    return result.data;
  }

  async searchMarkets(query: string, options?: { page?: number; limit?: number }) {
    const searchParams = new URLSearchParams();
    searchParams.set('query', query);
    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());

    const response = await this.api.get(MarketService.ROUTES.GET['/search'], { searchParams });
    const result = MarketBriefInfosResponseSchema.safeParse(response);
    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        MarketService.ROUTES.GET['/search'],
        response
      );
    }
    return result.data;
  }

  async getMarketTopPredictors(
    marketId: string,
    outcome: string,
    options?: { page?: number; limit?: number }
  ) {
    const searchParams = new URLSearchParams();
    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());

    const response = await this.api.get(
      MarketService.ROUTES.GET['/top-predictors/{marketId}/{outcome}'](marketId, outcome),
      { searchParams }
    );
    const result = MarketTopPredictorsResponseSchema.safeParse(response);
    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        MarketService.ROUTES.GET['/top-predictors/{marketId}/{outcome}'](marketId, outcome),
        response
      );
    }

    return transformMarketTopPredictorsResponse(result.data);
  }

  async createMarket(data: CreateMarketRequestBody) {
    const formData = new FormData();

    formData.append('channelId', data.channelId);
    formData.append('title', data.title);
    formData.append('description', data.description);
    formData.append('predictionDeadline', data.predictionDeadline.toString());
    formData.append('resultConfirmDeadline', data.resultConfirmDeadline.toString());
    formData.append('disputedPeriod', data.disputedPeriod);
    formData.append('category', data.category);
    formData.append('collateralAmount', data.collateralAmount.toString());
    formData.append('referenceURL', data.referenceURL);
    formData.append('signature', data.signature);
    formData.append('validationId', data.validationId);
    if (data.image) {
      formData.append('image', data.image);
    }
    data.outcomes.forEach(outcome => {
      formData.append('outcomes', outcome);
    });

    data.tags.forEach(tag => {
      formData.append('tags', tag);
    });

    if (data.broadcastURL) {
      formData.append('broadcastURL', data.broadcastURL);
    }

    const response = await this.api.post(MarketService.ROUTES.POST['/'], {
      body: formData,
    });

    const result = MarketSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(result.error, MarketService.ROUTES.POST['/'], response);
    }

    return transformMarket(result.data);
  }

  async validateMarket(data: ValidateRequestBody) {
    const response = await this.api.post(MarketService.ROUTES.POST['/validate'], {
      json: data,
    });

    const result = ValidateResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        MarketService.ROUTES.POST['/validate'],
        response
      );
    }
    return result.data;
  }
}

export const marketService = new MarketService();
