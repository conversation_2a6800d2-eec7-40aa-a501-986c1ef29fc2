import { <PERSON><PERSON><PERSON>cher, HTTPError, type Options } from '@repo/shared/lib/base-fetcher';
import { env } from '../env';
import { BaseResponseShapeSchema } from './base.schema';
import { toast } from '@repo/ui/components/sonner';
import { logger } from '../logger';
import { ApiError } from './base-api.error';

const createResponseValidationHook = () => {
  return async (request: Request, _options: any, response: Response) => {
    const startTime = Date.now();
    const method = request.method;
    const endpoint = request.url;

    // Only validate JSON responses
    const contentType = response.headers.get('content-type');
    if (!contentType?.includes('application/json')) {
      const duration = Date.now() - startTime;
      // logger.apiResponse(method, endpoint, response.status, duration);
      return response;
    }

    // Clone response to avoid consuming the body
    const clonedResponse = response.clone();

    try {
      const data = await clonedResponse.json();
      const validatedData = BaseResponseShapeSchema.parse(data);

      // const duration = Date.now() - startTime;
      // logger.apiResponse(method, endpoint, response.status, duration, validatedData.result);

      // For error responses (4xx, 5xx), return the entire JSON data
      if (response.status >= 400) {
        const errorResponse = new Response(JSON.stringify(data), {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
        });

        return errorResponse;
      }

      // For success responses (2xx), return just the result field
      const resultResponse = new Response(JSON.stringify(validatedData.result), {
        status: response.status,
        statusText: response.statusText,
        headers: response.headers,
      });

      return resultResponse;
    } catch (error) {
      throw new Error(
        `API Response validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
        {
          cause: error,
        }
      );
    }
  };
};

const createBeforeRequestHook = () => {
  return (request: Request) => {
    const method = request.method;
    const endpoint = request.url;
    let requestData;

    try {
      if (request.body && typeof request.body === 'string') {
        requestData = JSON.parse(request.body);
      }
    } catch {
      // Ignore parse errors for non-JSON bodies
    }

    logger.apiRequest(method, endpoint, requestData);
    return request;
  };
};

const createBeforeErrorHook = () => {
  return (error: any) => {
    const method = error.request?.method || 'UNKNOWN';
    const endpoint = error.request?.url || 'UNKNOWN';
    const statusCode = error.response?.status;

    if (statusCode) {
      logger.httpError(statusCode, method, endpoint, error);
    } else {
      logger.apiError(method, endpoint, error);
    }

    console.log('beforeError::', error);
    toast.error(`${error.message} ${error.response?.status || ''}`);
    return error;
  };
};

export class ApiClient extends BaseFetcher {
  constructor(path: string = '') {
    const prefixUrl = env.NEXT_PUBLIC_API_URL + path;
    super({
      prefixUrl,
      timeout: 30000,
      credentials: 'include',
      hooks: {
        afterResponse: [createResponseValidationHook()],
        // beforeRequest: [createBeforeRequestHook()],
        // beforeError: [createBeforeErrorHook()],
      },
    });
  }

  async get(url: string = '', options: Options = {}) {
    try {
      const result = await this.kyInstance.get(url, options).json();
      return result;
    } catch (error) {
      if (error instanceof HTTPError) {
        throw await ApiError.fromHTTPError(error, url);
      }
      throw error;
    }
  }

  async post(url: string, options: Options = {}) {
    try {
      const result = await this.kyInstance.post(url, options).json();
      return result;
    } catch (error) {
      if (error instanceof HTTPError) {
        throw await ApiError.fromHTTPError(error, url);
      }
      throw error;
    }
  }

  async put(url: string, options: Options = {}) {
    try {
      const result = await this.kyInstance.put(url, options).json();
      return result;
    } catch (error) {
      if (error instanceof HTTPError) {
        throw await ApiError.fromHTTPError(error, url);
      }
      throw error;
    }
  }

  async delete(url: string, options: Options = {}) {
    try {
      const result = await this.kyInstance.delete(url, options).json();
      return result;
    } catch (error) {
      if (error instanceof HTTPError) {
        throw await ApiError.fromHTTPError(error, url);
      }
      throw error;
    }
  }

  async patch(url: string, options: Options = {}) {
    try {
      const result = await this.kyInstance.patch(url, options).json();
      return result;
    } catch (error) {
      if (error instanceof HTTPError) {
        throw await ApiError.fromHTTPError(error, url);
      }
      throw error;
    }
  }
}
