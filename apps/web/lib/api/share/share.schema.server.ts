import { z } from 'zod';

// Share Dashboard Response Schema
export const ShareDashboardResponseSchema = z.object({
  totalShareReward: z.string(),
  totalShared: z.number(),
});

// My Share Dashboard Response Schema
export const MyShareDashboardResponseSchema = z.object({
  claimableAmount: z.string(),
  unLockableShareRewards: z.string(),
  totalClaimed: z.string(),
});

// Get Shares Response Schema
export const GetSharesResponseSchema = z.object({
  shares: z.array(
    z.object({
      market: z.object({
        id: z.string(),
        title: z.string(),
        imageUrl: z.string().optional(),
      }),
      winnings: z.string(),
      shareableAmount: z.string(),
      sharedAt: z.string().datetime({ offset: true }).optional(),
    })
  ),
  totalLength: z.number(),
});

// Share Request Schema
export const ShareRequestSchema = z.object({
  marketId: z.string(),
  signature: z.string(),
});

// Get Shares Request Schema
export const GetSharesRequestSchema = z.object({
  page: z.number().int().gte(0).default(0).optional(),
  limit: z.number().int().gt(0).lte(50).default(50).optional(),
  order: z.enum(['value', 'newest']).default('value').optional(),
});

// Success Response Schema
export const SuccessResponseSchema = z.object({
  success: z.boolean(),
});

// Type exports
export type ShareDashboardResponse = z.infer<typeof ShareDashboardResponseSchema>;
export type MyShareDashboardResponse = z.infer<typeof MyShareDashboardResponseSchema>;
export type GetSharesResponse = z.infer<typeof GetSharesResponseSchema>;
export type ShareRequest = z.infer<typeof ShareRequestSchema>;
export type GetSharesRequest = z.infer<typeof GetSharesRequestSchema>;
export type SuccessResponse = z.infer<typeof SuccessResponseSchema>;

// Schema types for transforms
export type ShareItem = z.infer<typeof GetSharesResponseSchema>['shares'][number];
export type ShareMarket = ShareItem['market'];
