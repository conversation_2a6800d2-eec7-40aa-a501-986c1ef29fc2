import { HTTPError } from '@repo/shared/lib/base-fetcher';
import { AuthSessionSchema, GetNonceSchema, VerifyResponseSchema } from './auth.schema';
import { ApiClient } from '../base-api';
import { isLocal } from '@/constants';
import { ApiError } from '../base-api.error';

export class AuthApis {
  static BASE_PATH = isLocal ? '/v1' : '/auth/v1';
  static ROUTES = {
    GET: {
      '/nonce/{address}': (address: string) => `nonce/${address}`,
      '/session': 'session',
    },
    POST: {
      '/verify': 'verify',
      '/signout': 'signout',
    },
  };
  api: ApiClient;

  constructor() {
    this.api = new ApiClient(AuthApis.BASE_PATH);
  }

  async getNonce(address: string) {
    const result = await this.api.get(AuthApis.ROUTES.GET['/nonce/{address}'](address));
    const nonce = GetNonceSchema.safeParse(result);

    if (!nonce.success) {
      throw ApiError.fromValidationError(
        nonce.error,
        AuthApis.ROUTES.GET['/nonce/{address}'](address),
        result
      );
    }
    return nonce.data;
  }

  async getSession() {
    const res = await this.api.get(AuthApis.ROUTES.GET['/session']);
    const result = AuthSessionSchema.safeParse(res);

    if (!result.success) {
      throw ApiError.fromValidationError(result.error, AuthApis.ROUTES.GET['/session'], res);
    }
    return result.data;
  }

  async verify(params: { message: string; signature: string }) {
    const result = await this.api.post(AuthApis.ROUTES.POST['/verify'], {
      json: params,
    });
    const parsed = VerifyResponseSchema.safeParse(result);

    if (!parsed.success) {
      throw ApiError.fromValidationError(parsed.error, AuthApis.ROUTES.POST['/verify'], result);
    }

    return parsed.data;
  }

  async signOut() {
    try {
      return await this.api.throttleRequest('signout', async () =>
        this.api.post(AuthApis.ROUTES.POST['/signout'])
      );
    } catch (error) {
      if (error instanceof HTTPError && error.response?.status === 401) {
        return console.log('401 error occurred during signout');
      }
      throw error;
    }
  }
}
