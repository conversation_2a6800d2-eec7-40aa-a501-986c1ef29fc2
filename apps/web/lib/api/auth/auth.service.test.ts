import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { AuthApis } from './auth.service';
import { HTTPError } from '@repo/shared/lib/base-fetcher';
import { polygonAmoy } from 'viem/chains';
import type { SIWECreateMessageArgs } from '@reown/appkit-siwe';
import { MockWallet, createTestSIWEArgs } from '@/lib/test-utils';
import { ApiError } from '../base-api.error';

// Mock environment variables
vi.mock('@/lib/env', () => ({
  env: {
    NEXT_PUBLIC_DEPLOYMENT_TARGET: 'development',
    NEXT_PUBLIC_API_URL: 'http://localhost:3000/api',
  },
  isProduction: false,
}));

// Mock constants
vi.mock('@/constants', () => ({
  isLocal: true,
}));

// Mock logger
vi.mock('@/lib/logger', () => ({
  logger: {
    apiRequest: vi.fn(),
    apiResponse: vi.fn(),
    httpError: vi.fn(),
    apiError: vi.fn(),
  },
}));

// Mock toast
vi.mock('@repo/ui/components/sonner', () => ({
  toast: {
    error: vi.fn(),
  },
}));

describe('AuthApis Unit Tests', () => {
  let authApis: AuthApis;

  beforeEach(() => {
    authApis = new AuthApis();

    // Mock API client methods
    vi.spyOn(authApis.api, 'get').mockImplementation(vi.fn());
    vi.spyOn(authApis.api, 'post').mockImplementation(vi.fn());
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Mock Wallet Configuration', () => {
    it('should have correct test wallet address', () => {
      expect(MockWallet.ADDRESS).toBe('******************************************');
    });

    it('should be able to sign messages', async () => {
      const testMessage = 'Hello, world!';
      const signature = await MockWallet.signMessage(testMessage);

      expect(MockWallet.isValidSignature(signature)).toBe(true);
    });
  });

  describe('getNonce', () => {
    it('should successfully fetch nonce for test address', async () => {
      // Arrange
      const mockResponse = {
        nonce: 'test-nonce-12345',
      };

      const mockGet = vi.spyOn(authApis.api, 'get').mockResolvedValue(mockResponse);

      // Act
      const result = await authApis.getNonce(MockWallet.ADDRESS);

      // Assert
      expect(mockGet).toHaveBeenCalledWith(`nonce/${MockWallet.ADDRESS}`);
      expect(result).toEqual(mockResponse);
      expect(result.nonce).toBe('test-nonce-12345');
    });

    it('should throw error when nonce API returns invalid response', async () => {
      // Arrange
      const invalidResponse = {
        // Missing nonce field
      };

      vi.spyOn(authApis.api, 'get').mockResolvedValue(invalidResponse);

      // Act & Assert
      await expect(authApis.getNonce(MockWallet.ADDRESS)).rejects.toThrow();
    });

    it('should handle network errors gracefully', async () => {
      // Arrange
      vi.spyOn(authApis.api, 'get').mockRejectedValue(new Error('Network timeout'));

      // Act & Assert
      await expect(authApis.getNonce(MockWallet.ADDRESS)).rejects.toThrow('Network timeout');
    });
  });

  describe('getSession', () => {
    it('should successfully fetch authenticated session', async () => {
      // Arrange
      const mockSessionResponse = {
        siwe: {
          address: MockWallet.ADDRESS,
          chainId: MockWallet.CHAIN_ID,
        },
      };

      vi.spyOn(authApis.api, 'get').mockResolvedValue(mockSessionResponse);

      // Act
      const result = await authApis.getSession();

      // Assert
      expect(authApis.api.get).toHaveBeenCalledWith('session');
      expect(result).toEqual(mockSessionResponse);
      expect(result.siwe?.address).toBe(MockWallet.ADDRESS);
      expect(result.siwe?.chainId).toBe(MockWallet.CHAIN_ID);
    });

    it('should return session with null siwe when not authenticated', async () => {
      // Arrange
      const mockUnauthenticatedResponse = {
        siwe: null,
      };

      vi.spyOn(authApis.api, 'get').mockResolvedValue(mockUnauthenticatedResponse);

      // Act
      const result = await authApis.getSession();

      // Assert
      expect(result.siwe).toBeNull();
    });

    it('should handle session fetch errors', async () => {
      // Arrange
      const mockResponse = new Response('Unauthorized', { status: 401 });
      const mockError = new HTTPError(mockResponse, {} as any, {} as any);
      const apiError = await ApiError.fromHTTPError(mockError, 'session');

      vi.spyOn(authApis.api, 'get').mockRejectedValue(apiError);

      // Act & Assert
      await expect(authApis.getSession()).rejects.toThrow();
    });
  });

  describe('verify', () => {
    it('should successfully verify valid SIWE signature', async () => {
      // Arrange
      const { message, signature } = await MockWallet.signSIWEMessage({
        nonce: 'test-nonce-verify-123',
      });

      const mockVerifyResponse = {
        success: true,
      };

      vi.spyOn(authApis.api, 'post').mockResolvedValue(mockVerifyResponse);

      // Act
      const result = await authApis.verify({
        message,
        signature,
      });

      // Assert
      expect(authApis.api.post).toHaveBeenCalledWith('verify', {
        json: {
          message,
          signature,
        },
      });
      expect(result.success).toBe(true);
    });

    it('should handle invalid signature verification', async () => {
      // Arrange
      const invalidParams = {
        message: 'invalid SIWE message format',
        signature: '0xinvalidsignature',
      };

      const mockResponse = new Response('Invalid signature', { status: 400 });
      const mockError = new HTTPError(mockResponse, {} as any, {} as any);
      const apiError = await ApiError.fromHTTPError(mockError, 'verify');

      vi.spyOn(authApis.api, 'post').mockRejectedValue(apiError);

      // Act & Assert
      await expect(authApis.verify(invalidParams)).rejects.toThrow();
    });

    it('should handle verification service errors', async () => {
      // Arrange
      const params = {
        message: 'test message',
        signature: '0xtest',
      };

      vi.spyOn(authApis.api, 'post').mockRejectedValue(
        new Error('Verification service unavailable')
      );

      // Act & Assert
      await expect(authApis.verify(params)).rejects.toThrow('Verification service unavailable');
    });
  });

  describe('signOut', () => {
    it('should successfully sign out user', async () => {
      // Arrange
      const mockSignOutResponse = {
        message: 'Successfully signed out',
      };

      vi.spyOn(authApis.api, 'post').mockResolvedValue(mockSignOutResponse);

      // Act
      const result = await authApis.signOut();

      // Assert
      expect(authApis.api.post).toHaveBeenCalledWith('signout');
      expect(result).toEqual(mockSignOutResponse);
    });

    it('should handle 401 error gracefully during signout', async () => {
      // Arrange
      const mockResponse = new Response('Unauthorized', { status: 401 });
      const mock401Error = new HTTPError(mockResponse, {} as any, {} as any);

      vi.spyOn(authApis.api, 'post').mockRejectedValue(mock401Error);

      // Mock console.log to capture the output
      const consoleSpy = vi.spyOn(console, 'log').mockImplementation(() => {});

      // Act
      const result = await authApis.signOut();

      // Assert
      expect(consoleSpy).toHaveBeenCalledWith('401 error occurred during signout');
      expect(result).toBeUndefined();

      consoleSpy.mockRestore();
    });

    it('should re-throw non-401 errors during signout', async () => {
      // Arrange
      const mockResponse = new Response('Internal Server Error', { status: 500 });
      const mockServerError = new HTTPError(mockResponse, {} as any, {} as any);

      vi.spyOn(authApis.api, 'post').mockRejectedValue(mockServerError);

      // Act & Assert
      await expect(authApis.signOut()).rejects.toThrow();
    });
  });

  describe('API Configuration', () => {
    it('should use correct base path for local environment', () => {
      expect(AuthApis.BASE_PATH).toBe('/v1');
    });

    it('should have correct route definitions', () => {
      expect(AuthApis.ROUTES.GET['/nonce/{address}']('test-address')).toBe('nonce/test-address');
      expect(AuthApis.ROUTES.GET['/session']).toBe('session');
      expect(AuthApis.ROUTES.POST['/verify']).toBe('verify');
      expect(AuthApis.ROUTES.POST['/signout']).toBe('signout');
    });
  });

  describe('Complete SIWE Authentication Flow', () => {
    it('should complete full authentication workflow with test wallet', async () => {
      // Arrange - Setup mock responses for complete flow
      const mockNonce = 'complete-flow-nonce-456';
      const mockNonceResponse = {
        nonce: mockNonce,
      };

      const mockVerifyResponse = {
        success: true,
      };

      const mockAuthenticatedSession = {
        siwe: {
          address: MockWallet.ADDRESS,
          chainId: MockWallet.CHAIN_ID,
        },
      };

      // Setup sequential mock responses
      const mockGet = vi
        .spyOn(authApis.api, 'get')
        .mockResolvedValueOnce(mockNonceResponse)
        .mockResolvedValueOnce(mockAuthenticatedSession);

      const mockPost = vi.spyOn(authApis.api, 'post').mockResolvedValue(mockVerifyResponse);

      // Act - Execute complete SIWE flow
      // 1. Get nonce
      const nonceResult = await authApis.getNonce(MockWallet.ADDRESS);
      expect(nonceResult.nonce).toBe(mockNonce);

      // 2. Create and sign SIWE message
      const { message, signature } = await MockWallet.signSIWEMessage({
        nonce: mockNonce,
      });

      // 3. Verify signature
      const verifyResult = await authApis.verify({ message, signature });
      expect(verifyResult.success).toBe(true);

      // 4. Get authenticated session
      const sessionResult = await authApis.getSession();
      expect(sessionResult.siwe?.address).toBe(MockWallet.ADDRESS);

      // Assert all API calls were made correctly
      expect(mockGet).toHaveBeenNthCalledWith(1, `nonce/${MockWallet.ADDRESS}`);
      expect(mockPost).toHaveBeenCalledWith('verify', {
        json: { message, signature },
      });
      expect(mockGet).toHaveBeenNthCalledWith(2, 'session');
    });

    it('should handle authentication flow errors gracefully', async () => {
      // Arrange - Setup nonce to succeed but verification to fail
      const mockNonceResponse = {
        nonce: 'error-test-nonce',
      };

      vi.spyOn(authApis.api, 'get').mockResolvedValue(mockNonceResponse);

      const mockResponse = new Response('Signature verification failed', { status: 400 });
      const mockVerifyError = new HTTPError(mockResponse, {} as any, {} as any);
      const apiError = await ApiError.fromHTTPError(mockVerifyError, 'verify');
      vi.spyOn(authApis.api, 'post').mockRejectedValue(apiError);

      // Act - Try to complete flow but expect failure at verification
      const nonceResult = await authApis.getNonce(MockWallet.ADDRESS);
      expect(nonceResult.nonce).toBe('error-test-nonce');

      const { message, signature } = await MockWallet.signSIWEMessage({
        nonce: 'error-test-nonce',
      });

      // Assert - Verification should fail
      await expect(authApis.verify({ message, signature })).rejects.toThrow();
    });
  });
});
