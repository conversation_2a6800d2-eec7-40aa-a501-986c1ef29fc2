import { DEFAULT_CHANNEL_BANNER_URL, DEFAULT_USER_AVATAR_URL } from '@/lib/constants';
import { formatUsdc } from '@/lib/format';
import type {
  GetPredictLeaderboardResDto,
  GetPredictLeaderboardByAddressResDto,
  GetChannelLeaderboardResDto,
  GetChannelLeaderboardByAddressResDto,
  PredictLeaderboardSchema,
  ChannelLeaderboardSchema,
} from './leaderboard.schema.server';

export const transformPredictLeaderboardEntry = (data: PredictLeaderboardSchema) => {
  return {
    ...data,
    imageUrl: data.imageUrl ?? DEFAULT_USER_AVATAR_URL,
    formattedValue: formatUsdc(data.value),
  };
};

export const transformChannelLeaderboardEntry = (data: ChannelLeaderboardSchema) => {
  return {
    ...data,
    imageUrl: data.imageUrl ?? DEFAULT_CHANNEL_BANNER_URL,
    formattedValue: formatUsdc(data.value),
  };
};

export const transformGetPredictLeaderboardResponse = (data: GetPredictLeaderboardResDto) => {
  return {
    results: data.results.map(transformPredictLeaderboardEntry),
  };
};

export const transformGetPredictLeaderboardByAddressResponse = (
  data: GetPredictLeaderboardByAddressResDto
) => {
  return {
    ...data,
    imageUrl: data.imageUrl ?? DEFAULT_USER_AVATAR_URL,
    formattedValue: formatUsdc(data.value),
  };
};

export const transformGetChannelLeaderboardResponse = (data: GetChannelLeaderboardResDto) => {
  return {
    results: data.results.map(transformChannelLeaderboardEntry),
  };
};

export const transformGetChannelLeaderboardByChannelIdResponse = (
  data: GetChannelLeaderboardByAddressResDto
) => {
  return {
    ...data,
    imageUrl: data.imageUrl ?? DEFAULT_CHANNEL_BANNER_URL,
    formattedValue: formatUsdc(data.value),
  };
};
