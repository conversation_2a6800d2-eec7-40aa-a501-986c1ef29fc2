import { DEFAULT_USER_AVATAR_URL } from '@/lib/constants';
import { formatUsdc } from '@/lib/format';
import type {
  GetLeaderboardResDto,
  GetLeaderboardByAddressResDto,
  LeaderboardSchema,
} from './leaderboard.schema.server';

export const transformLeaderboardEntry = (data: LeaderboardSchema) => {
  return {
    ...data,
    imageUrl: data.imageUrl ?? DEFAULT_USER_AVATAR_URL,
    formattedValue: formatUsdc(data.value),
    rawValue: BigInt(data.value),
  };
};

export const transformGetLeaderboardResponse = (data: GetLeaderboardResDto) => {
  return {
    results: data.results.map(transformLeaderboardEntry),
  };
};

export const transformGetLeaderboardByAddressResponse = (data: GetLeaderboardByAddressResDto) => {
  return {
    ...data,
    imageUrl: data.imageUrl ?? DEFAULT_USER_AVATAR_URL,
    formattedValue: formatUsdc(data.value),
    rawValue: BigInt(data.value),
  };
};
