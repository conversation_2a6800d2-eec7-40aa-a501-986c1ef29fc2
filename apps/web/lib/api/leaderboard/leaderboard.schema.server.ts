import { z } from 'zod';

export const GetLeaderboardResDto = z.object({
  results: z.array(
    z.object({
      address: z.string(),
      nickname: z.string(),
      imageUrl: z.string().nullable(),
      value: z.string(),
      rank: z.number(),
    })
  ),
});

export const GetLeaderboardByAddressResDto = z.object({
  address: z.string(),
  nickname: z.string(),
  imageUrl: z.string().nullable(),
  value: z.string(),
  rank: z.number(),
});

export const LeaderboardType = z.enum(['volume', 'profit']);
export type LeaderboardType = z.infer<typeof LeaderboardType>;

export const LeaderboardPeriod = z.enum(['day', 'week', 'month', 'all']);
export type LeaderboardPeriod = z.infer<typeof LeaderboardPeriod>;

export type GetLeaderboardResDto = z.infer<typeof GetLeaderboardResDto>;
export type GetLeaderboardByAddressResDto = z.infer<typeof GetLeaderboardByAddressResDto>;
export type LeaderboardSchema = z.infer<typeof GetLeaderboardResDto>['results'][number];
