import { z } from 'zod';

export const GetPredictLeaderboardResDto = z.object({
  results: z.array(
    z.object({
      address: z.string(),
      nickname: z.string(),
      imageUrl: z.string().nullish(),
      value: z.string(),
      rank: z.number(),
    })
  ),
});

export const GetPredictLeaderboardByAddressResDto = z.object({
  address: z.string(),
  nickname: z.string(),
  imageUrl: z.string().nullable(),
  value: z.string(),
  rank: z.number(),
});

export const GetChannelLeaderboardResDto = z.object({
  results: z.array(
    z.object({
      channelId: z.string(),
      name: z.string(),
      imageUrl: z.string().nullish(),
      value: z.string(),
      rank: z.number(),
    })
  ),
});

export const GetChannelLeaderboardByAddressResDto = z.object({
  channelId: z.string(),
  name: z.string(),
  imageUrl: z.string().nullish(),
  value: z.string(),
  rank: z.number(),
});

export const PredictLeaderboardTypeEnum = z.enum(['VOLUME', 'PROFIT']);
export type PredictLeaderboardType = z.infer<typeof PredictLeaderboardTypeEnum>;

export const LeaderboardPeriodEnum = z.enum(['DAY', 'WEEK', 'MONTH', 'ALL']);
export type LeaderboardPeriod = z.infer<typeof LeaderboardPeriodEnum>;

export type GetPredictLeaderboardResDto = z.infer<typeof GetPredictLeaderboardResDto>;
export type GetPredictLeaderboardByAddressResDto = z.infer<
  typeof GetPredictLeaderboardByAddressResDto
>;
export type GetChannelLeaderboardResDto = z.infer<typeof GetChannelLeaderboardResDto>;
export type GetChannelLeaderboardByAddressResDto = z.infer<
  typeof GetChannelLeaderboardByAddressResDto
>;

export type PredictLeaderboardSchema = z.infer<
  typeof GetPredictLeaderboardResDto
>['results'][number];
export type ChannelLeaderboardSchema = z.infer<
  typeof GetChannelLeaderboardResDto
>['results'][number];
