import { ApiClient } from '@/lib/api/base-api';
import { ApiError } from '../base-api.error';
import {
  GetChannelLeaderboardByAddressResDto,
  GetChannelLeaderboardResDto,
  GetPredictLeaderboardByAddressResDto,
  GetPredictLeaderboardResDto,
  LeaderboardPeriod,
  PredictLeaderboardType,
} from './leaderboard.schema.server';
import {
  transformGetChannelLeaderboardByChannelIdResponse,
  transformGetChannelLeaderboardResponse,
  transformGetPredictLeaderboardByAddressResponse,
  transformGetPredictLeaderboardResponse,
} from './leaderboard.transform';

export class LeaderboardService {
  static BASE_PATH = '/data-api/v1/leaderboard';
  static ROUTES = {
    GET: {
      '/predict/:type/:period': 'predict/:type/:period',
      '/predict/:type/:period/:address': 'predict/:type/:period/:address',
      '/channel/:period': 'channel/:period',
      '/channel/:period/:channelId': 'channel/:period/:channelId',
    },
  };
  private api: ApiClient;

  constructor() {
    this.api = new ApiClient(LeaderboardService.BASE_PATH);
  }

  /**
   * 예측 리더보드 조회
   */
  async getPredictLeaderboard(type: PredictLeaderboardType, period: LeaderboardPeriod) {
    const url = LeaderboardService.ROUTES.GET['/predict/:type/:period']
      .replace(':type', type)
      .replace(':period', period);

    const response = await this.api.get(url);
    const result = GetPredictLeaderboardResDto.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(result.error, url, response);
    }

    return transformGetPredictLeaderboardResponse(result.data);
  }

  /**
   * 주소로 예측 리더보드 조회
   */
  async getPredictLeaderboardByAddress(
    type: PredictLeaderboardType,
    period: LeaderboardPeriod,
    address: string
  ) {
    const url = LeaderboardService.ROUTES.GET['/predict/:type/:period/:address']
      .replace(':type', type)
      .replace(':period', period)
      .replace(':address', address);

    const response = await this.api.get(url);

    const result = GetPredictLeaderboardByAddressResDto.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(result.error, url, response);
    }

    return transformGetPredictLeaderboardByAddressResponse(result.data);
  }

  /**
   * 채널 리더보드 조회
   */
  async getChannelLeaderboard(period: LeaderboardPeriod) {
    const url = LeaderboardService.ROUTES.GET['/channel/:period'].replace(':period', period);

    const response = await this.api.get(url);
    const result = GetChannelLeaderboardResDto.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(result.error, url, response);
    }

    return transformGetChannelLeaderboardResponse(result.data);
  }

  /**
   * 채널 ID로 채널 리더보드 조회
   */
  async getChannelLeaderboardByChannelId(period: LeaderboardPeriod, channelId: string) {
    const url = LeaderboardService.ROUTES.GET['/channel/:period/:channelId']
      .replace(':period', period)
      .replace(':channelId', channelId);

    const response = await this.api.get(url);

    const result = GetChannelLeaderboardByAddressResDto.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(result.error, url, response);
    }

    return transformGetChannelLeaderboardByChannelIdResponse(result.data);
  }
}

export const leaderboardService = new LeaderboardService();
