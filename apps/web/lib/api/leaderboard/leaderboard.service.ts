import { ApiClient } from '@/lib/api/base-api';
import {
  GetLeaderboardResDto,
  GetLeaderboardByAddressResDto,
  LeaderboardPeriod,
  LeaderboardType,
} from './leaderboard.schema.server';
import {
  transformGetLeaderboardResponse,
  transformGetLeaderboardByAddressResponse,
} from './leaderboard.transform';
import { ApiError } from '../base-api.error';

export class LeaderboardService {
  static BASE_PATH = '/data-api/v1/leaderboard';
  static ROUTES = {
    GET: {
      '/:type/:period': ':type/:period',
      '/:type/:period/:address': ':type/:period/:address',
    },
  };
  private api: ApiClient;

  constructor() {
    this.api = new ApiClient(LeaderboardService.BASE_PATH);
  }

  /**
   * 리더보드 조회
   */
  async getLeaderboard(type: LeaderboardType, period: LeaderboardPeriod) {
    const url = LeaderboardService.ROUTES.GET['/:type/:period']
      .replace(':type', type)
      .replace(':period', period);

    const response = await this.api.get(url);
    const result = GetLeaderboardResDto.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(result.error, url, response);
    }

    return transformGetLeaderboardResponse(result.data);
  }

  /**
   * 주소로 리더보드 조회
   */
  async getLeaderboardByAddress(type: LeaderboardType, period: LeaderboardPeriod, address: string) {
    const url = LeaderboardService.ROUTES.GET['/:type/:period/:address']
      .replace(':type', type)
      .replace(':period', period)
      .replace(':address', address);

    const response = await this.api.get(url);

    const result = GetLeaderboardByAddressResDto.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(result.error, url, response);
    }

    return transformGetLeaderboardByAddressResponse(result.data);
  }
}

export const leaderboardService = new LeaderboardService();
