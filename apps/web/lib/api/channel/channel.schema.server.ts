import { z } from 'zod';
import { MarketStatusEnumSchema } from '../market/market.schema.server';

const UpdateChannelReqDto = z
  .object({
    name: z
      .string()
      .min(1)
      .max(20)
      .regex(/^[a-zA-Z0-9\s]+$/),
    description: z.string().max(255),
    image: z.instanceof(File),
    banner: z.instanceof(File),
    channelSns: z.array(
      z
        .object({
          snsType: z.enum([
            'youtube',
            'twitter',
            'telegram',
            'facebook',
            'discord',
            'tiktok',
            'instagram',
            'abstract',
          ]),
          snsUrl: z.union([z.string(), z.literal('')]),
        })
        .passthrough()
    ),
  })
  .partial()
  .passthrough();
const ChannelIdReqDto = z.object({ channelId: z.string() }).passthrough();
const ChannelBriefInfosResDto = z
  .object({
    channels: z.array(
      z
        .object({
          id: z.string(),
          name: z.string(),
          imageUrl: z.union([z.string(), z.literal('')]).optional(),
        })
        .passthrough()
    ),
  })
  .passthrough();

export const UpdateChannelRequestBodySchema = UpdateChannelReqDto;

export const ChannelPredictionDepositRequestBodySchema = z.object({
  marketId: z.string().min(66).max(66),
  amount: z.string(),
  signature: z.string(),
});

export const ChannelSubscribeRequestSchema = ChannelIdReqDto;
export const ChannelUnsubscribeRequestSchema = ChannelIdReqDto;

export const ChannelInfoResponseSchema = z.object({
  id: z.string(),
  name: z.string(),
  imageUrl: z.string().optional(),
  bannerUrl: z.string().optional(),
  description: z.string().optional(),
  subscribers: z.number(),
  totalVolume: z.string(),
  totalMarkets: z.number(),
  channelSns: z.array(
    z.object({
      channelId: z.string(),
      snsType: z.enum([
        'youtube',
        'twitter',
        'telegram',
        'facebook',
        'discord',
        'tiktok',
        'instagram',
        'abstract',
      ]),
      snsUrl: z.string(),
    })
  ),
  isLeaderLive: z.boolean(),
  isSubscribed: z.boolean(),
});

export const GetInChannelLeaderboardResponseSchema = z.object({
  rankings: z.array(
    z.object({
      rank: z.number(),
      address: z.string(),
      nickname: z.string(),
      pnl: z.string(),
      volume: z.string(),
    })
  ),
  myRank: z
    .object({
      rank: z.number(),
      address: z.string(),
      nickname: z.string(),
      pnl: z.string(),
      volume: z.string(),
    })
    .optional(),
});

export const ChannelCollateralResponseSchema = z.object({
  available: z.string(),
  tied: z.string(),
});

export const ChannelCollateralHistoryResponseSchema = z.object({
  histories: z.array(
    z.object({
      type: z.string(),
      category: z.string().optional(),
      amount: z.string(),
      timestamp: z.string(),
    })
  ),
  totalLength: z.number(),
});

export const PopularChannelResponseSchema = z.object({
  channels: z.array(
    z.object({
      id: z.string(),
      name: z.string(),
      imageUrl: z.string().optional(),
      volume: z.string(),
    })
  ),
});

export const ChannelActivePredictionsResponseSchema = z.object({
  markets: z.array(
    z.object({
      id: z.string(),
      title: z.string(),
      imageUrl: z.string().optional(),
      totalVolume: z.string().describe('usd'),
      deposit: z.string().describe('usd'),
      status: MarketStatusEnumSchema,
    })
  ),
  totalLength: z.number(),
});

export const ChannelHistoryPredictionsResponseSchema = z.object({
  markets: z.array(
    z.object({
      id: z.string(),
      title: z.string(),
      imageUrl: z.string().nullable(),
      finalizedAt: z.string(),
      rewards: z.object({
        prediction: z.string(),
        dispute: z.string(),
      }),
      outcomes: z.object({ initial: z.string(), final: z.string() }),
    })
  ),
  totalLength: z.number(),
});

export const ChannelRewardsResponseSchema = z.object({
  totalRewards: z.string(),
  claimableRewards: z.string(),
});

export const ChannelRewardsHistoryResponseSchema = z.object({
  histories: z.array(
    z.object({
      amount: z.string(),
      timestamp: z.string(),
      claimedAt: z.string().optional(),
    })
  ),
  totalLength: z.number(),
});

export const ChannelBriefInfosResponseSchema = ChannelBriefInfosResDto;
export const SuccessResponseSchema = z.object({ success: z.boolean() });

// Type exports
export type UpdateChannelRequestBody = z.infer<typeof UpdateChannelRequestBodySchema>;
export type ChannelPredictionDepositRequestBody = z.infer<
  typeof ChannelPredictionDepositRequestBodySchema
>;
export type ChannelRewardsHistory = z.infer<typeof ChannelRewardsHistoryResponseSchema>;
export type ChannelSubscribeRequest = z.infer<typeof ChannelSubscribeRequestSchema>;
export type ChannelUnsubscribeRequest = z.infer<typeof ChannelUnsubscribeRequestSchema>;
export type ChannelInfoResponse = z.infer<typeof ChannelInfoResponseSchema>;

// Schema types for transforms
export type ChannelCollateralSchema = z.infer<typeof ChannelCollateralResponseSchema>;
export type ChannelLeaderboardSchema = z.infer<typeof GetInChannelLeaderboardResponseSchema>;
export type ActivePredictionSchema = z.infer<
  typeof ChannelActivePredictionsResponseSchema
>['markets'][number];
export type HistoryPredictionSchema = z.infer<
  typeof ChannelHistoryPredictionsResponseSchema
>['markets'][number];
export type RewardHistorySchema = z.infer<
  typeof ChannelRewardsHistoryResponseSchema
>['histories'][number];

export type LeaderboardType = 'volume' | 'profit';
export type MarketStatus = 'live' | 'ended';
export type MarketOrder = 'volume' | 'createdAt';
export type RewardsOrder = 'value' | 'newest';
