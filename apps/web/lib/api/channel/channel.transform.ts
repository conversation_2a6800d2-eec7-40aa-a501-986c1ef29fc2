import {
  DEFAULT_CHANNEL_BANNER_URL,
  DEFAULT_MARKET_AVATAR_URL,
  MARKET_STATUS_TEXT_MAP,
} from '@/lib/constants';
import { formatUsdc } from '@/lib/format';
import { shortenNumber, toLocalDate } from '@/lib/utils';
import { z } from 'zod';
import {
  ChannelCollateralHistoryResponseSchema,
  ChannelActivePredictionsResponseSchema,
  ChannelHistoryPredictionsResponseSchema,
  ChannelRewardsResponseSchema,
  ChannelRewardsHistoryResponseSchema,
  PopularChannelResponseSchema,
} from './channel.schema.server';
import type {
  ChannelInfoResponse,
  ChannelCollateralSchema,
  ChannelLeaderboardSchema,
  ActivePredictionSchema,
  HistoryPredictionSchema,
  RewardHistorySchema,
} from './channel.schema.server';
import BigNumber from 'bignumber.js';

export const transformChannelInfo = (data: ChannelInfoResponse) => {
  return {
    ...data,
    imageUrl: data.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    bannerUrl: data.bannerUrl ?? DEFAULT_CHANNEL_BANNER_URL,
    totalVolumeFormatted: formatUsdc(data.totalVolume),
    subscribers: shortenNumber(data.subscribers),
  };
};

export const transformChannelCollateral = (data: ChannelCollateralSchema) => {
  const available = BigNumber(data.available);
  const tied = BigNumber(data.tied);
  const total = available.plus(tied);

  return {
    ...data,
    availableFormatted: formatUsdc(data.available),
    tiedFormatted: formatUsdc(data.tied),
    totalFormatted: formatUsdc(total.toString()),
    // rawAvailable: available,
    // rawTied: tied,
    // rawTotal: total,
  };
};

export const transformChannelLeaderboard = (data: ChannelLeaderboardSchema) => {
  return {
    rankings: data.rankings.map(ranking => ({
      ...ranking,
      formattedPnl: formatUsdc(ranking.pnl),
      formattedVolume: formatUsdc(ranking.volume),
      rawPnl: BigNumber(ranking.pnl),
      rawVolume: BigNumber(ranking.volume),
    })),
    myRank: data.myRank
      ? {
          ...data.myRank,
          formattedPnl: formatUsdc(data.myRank.pnl),
          formattedVolume: formatUsdc(data.myRank.volume),
          rawPnl: BigNumber(data.myRank.pnl),
          rawVolume: BigNumber(data.myRank.volume),
        }
      : undefined,
  };
};

export const transformActivePrediction = (data: ActivePredictionSchema) => {
  return {
    ...data,
    imageUrl: data.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    formattedTotalVolume: formatUsdc(data.totalVolume, { withDollarSign: true }),
    formattedDeposit: formatUsdc(data.deposit, { withDollarSign: true }),
    rawTotalVolume: BigNumber(data.totalVolume),
    rawDeposit: BigNumber(data.deposit),
    statusText: MARKET_STATUS_TEXT_MAP[data.status],
  };
};

export type ActivePredictionTransformed = ReturnType<typeof transformActivePrediction>;

export const transformActivePredictionsResponse = (
  data: z.infer<typeof ChannelActivePredictionsResponseSchema>
) => {
  return {
    ...data,
    markets: data.markets.map(transformActivePrediction),
  };
};

export const transformHistoryPrediction = (data: HistoryPredictionSchema) => {
  const date = new Date(data.finalizedAt);
  const formattedDate = date.toLocaleDateString('en-US', {
    month: 'short',
    day: 'numeric',
    year: 'numeric',
    hour: 'numeric',
    minute: '2-digit',
    hour12: true,
  });

  return {
    ...data,
    imageUrl: data.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    finalizedAt: formattedDate,
    rewards: {
      prediction: formatUsdc(data.rewards.prediction),
      dispute: formatUsdc(data.rewards.dispute),
    },
    rawRewards: {
      prediction: BigNumber(data.rewards.prediction),
      dispute: BigNumber(data.rewards.dispute),
    },
  };
};

export const transformHistoryPredictionsResponse = (
  data: z.infer<typeof ChannelHistoryPredictionsResponseSchema>
) => {
  return {
    ...data,
    markets: data.markets.map(transformHistoryPrediction),
  };
};

export const transformChannelRewards = (data: z.infer<typeof ChannelRewardsResponseSchema>) => {
  return {
    ...data,
    formattedTotalRewards: formatUsdc(data.totalRewards),
    formattedClaimableRewards: formatUsdc(data.claimableRewards),
    rawTotalRewards: BigNumber(data.totalRewards),
    rawClaimableRewards: BigNumber(data.claimableRewards),
  };
};

export type ChannelRewardsTransformed = ReturnType<typeof transformChannelRewards>;

export const transformRewardHistory = (data: RewardHistorySchema) => {
  return {
    ...data,
    formattedAmount: formatUsdc(data.amount),
    rawAmount: BigNumber(data.amount),
    timestamp: toLocalDate(data.timestamp),
    claimedAt: data.claimedAt ? toLocalDate(data.claimedAt) : null,
    status: data.claimedAt ? 'Claimed' : 'Unclaimed',
  };
};

export const transformRewardsHistoryResponse = (
  data: z.infer<typeof ChannelRewardsHistoryResponseSchema>
) => {
  return {
    ...data,
    histories: data.histories.map(transformRewardHistory),
  };
};

export type RewardHistoryTransformed = ReturnType<typeof transformRewardHistory>;

export const transformCollateralHistoryResponse = (
  data: z.infer<typeof ChannelCollateralHistoryResponseSchema>
) => {
  return {
    ...data,
    histories: data.histories.map(history => ({
      ...history,
      formattedAmount: formatUsdc(history.amount),
      rawAmount: BigNumber(history.amount),
      timestamp: toLocalDate(history.timestamp),
    })),
  };
};

export const transformPopularChannelsResponse = (
  data: z.infer<typeof PopularChannelResponseSchema>
) => {
  return {
    channels: data.channels.map(channel => ({
      ...channel,
      imageUrl: channel.imageUrl || undefined,
      formattedVolume: formatUsdc(channel.volume),
      rawVolume: BigNumber(channel.volume),
    })),
  };
};
