import { ApiClient } from '@/lib/api/base-api';
import {
  CategoryPageResponseSchema,
  CategoriesResponseSchema,
  MarketOrder,
} from './category.schema.server';
import { transformCategoryPageResponse, transformCategoriesResponse } from './category.transform';
import { ApiError } from '../base-api.error';
import { GetMarketsResponseSchema } from '../market/market.schema.server';

export class CategoryService {
  static BASE_PATH = '/data-api/v1/category';
  static ROUTES = {
    GET: {
      '/{category}': (category: string) => `${category}`,
      '/{category}/tags': (category: string) => `${category}/tags`,
      '/categories': 'categories',
      '/markets/{category}/{tag}': (category: string, tag: string) => `markets/${category}/${tag}`,
    },
  };
  private api: ApiClient;

  constructor() {
    this.api = new ApiClient(CategoryService.BASE_PATH);
  }

  /**
   * 특정 카테고리의 통계 정보 조회
   */
  async getCategoryStats(category: string) {
    const response = await this.api.get(CategoryService.ROUTES.GET['/{category}'](category));

    const result = CategoryPageResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        CategoryService.ROUTES.GET['/{category}'](category),
        response
      );
    }
    return transformCategoryPageResponse(result.data);
  }

  /**
   * 카테고리 내 태그 목록 조회
   */
  async getTagsInCategory(category: string, options?: { page?: number; limit?: number }) {
    const searchParams = new URLSearchParams();
    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());

    const response = await this.api.get(CategoryService.ROUTES.GET['/{category}/tags'](category), {
      searchParams,
    });

    // Note: According to the API spec, this returns void, but we'll return the response
    return response;
  }

  async getCategories(options?: { page?: number; limit?: number }) {
    const searchParams = new URLSearchParams();
    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());

    const response = await this.api.get(CategoryService.ROUTES.GET['/categories'], {
      searchParams,
    });

    const result = CategoriesResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        CategoryService.ROUTES.GET['/categories'],
        response
      );
    }

    return transformCategoriesResponse(result.data);
  }

  /**
   * 카테고리와 태그별 마켓 목록 조회
   */
  async getMarketsInCategory(
    category: string,
    tag: string = 'All',
    options?: {
      page?: number;
      limit?: number;
      order?: MarketOrder;
    }
  ) {
    const searchParams = new URLSearchParams();
    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());
    if (options?.order !== undefined) searchParams.set('order', options.order);

    const response = await this.api.get(
      CategoryService.ROUTES.GET['/markets/{category}/{tag}'](category, tag),
      { searchParams }
    );

    const result = GetMarketsResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        CategoryService.ROUTES.GET['/markets/{category}/{tag}'](category, tag),
        response
      );
    }
    return result.data;
  }

  /**
   * 카테고리 통계와 마켓을 함께 가져오는 편의 메서드
   */
  async getCategoryWithMarkets(
    category: string,
    tag: string = 'All',
    options?: {
      page?: number;
      limit?: number;
      order?: MarketOrder;
    }
  ) {
    try {
      const [categoryStats, markets] = await Promise.all([
        this.getCategoryStats(category),
        this.getMarketsInCategory(category, tag, options),
      ]);

      return {
        categoryStats,
        markets,
      };
    } catch (error) {
      console.error('Get category with markets error:', error);
      throw error;
    }
  }
}

export const categoryService = new CategoryService();
