import { z } from 'zod';

const CategoryPageResDto = z
  .object({
    category: z
      .object({
        name: z.string().min(1).max(50),
        imageUrl: z.union([z.string(), z.literal('')]).optional(),
      })
      .passthrough(),
    liveMarketCount: z.number().int().gte(0),
    totalVolume: z.number().int(),
  })
  .passthrough();

export const CategoryPageResponseSchema = CategoryPageResDto;
export const CategoriesResponseSchema = z.object({
  categories: z.array(
    z.object({
      name: z.string().min(1).max(50),
    })
  ),
  totalLength: z.number(),
});

export type CategoryPageResponse = z.infer<typeof CategoryPageResponseSchema>;
export type CategoriesResponse = z.infer<typeof CategoriesResponseSchema>;
export type MarketOrder = 'VOLUME' | 'NEWEST' | 'ENDING_SOON' | 'COMPETITIVE';
