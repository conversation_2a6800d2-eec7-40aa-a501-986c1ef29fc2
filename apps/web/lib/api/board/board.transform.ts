import { DEFAULT_USER_AVATAR_URL } from '@/lib/constants';
import { formatUsdc } from '@/lib/format';
import type { CommentsResponse, CommentSchema } from './board.schema.server';

export const transformComment = (data: CommentSchema) => {
  return {
    ...data,
    author: {
      ...data.author,
      imageUrl: data.author.imageUrl ?? DEFAULT_USER_AVATAR_URL,
    },
    positions: data.positions?.map(position => ({
      ...position,
      formattedValue: formatUsdc(position.value),
      rawValue: BigInt(position.value),
    })),
  };
};

export const transformCommentsResponse = (data: CommentsResponse) => {
  return {
    ...data,
    comments: data.comments.map(transformComment),
  };
};
