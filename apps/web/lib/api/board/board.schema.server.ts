import { z } from 'zod';

const LikeResDto = z.object({ id: z.string(), isLiked: z.boolean() }).passthrough();
const PostResDto = z
  .object({
    id: z.string(),
    refId: z.string(),
    title: z.string().min(1).max(100),
    content: z.string().min(1).max(2000),
    createdAt: z.string().datetime({ offset: true }),
    updatedAt: z.string().datetime({ offset: true }),
    views: z.number().int().gte(0),
    likes: z.number().int().gte(0),
    commentCount: z.number().int().gte(0),
    author: z
      .object({
        address: z.string(),
        nickname: z.string(),
        imageUrl: z.union([z.string(), z.literal('')]).optional(),
      })
      .passthrough(),
    isLiked: z.boolean(),
    isPinned: z.boolean(),
  })
  .passthrough();
const EditChannelPostReqDto = z
  .object({ title: z.string().min(1).max(100), content: z.string().min(1).max(2000) })
  .partial()
  .passthrough();
const PostsResDto = z
  .object({
    posts: z.array(
      z
        .object({
          id: z.string(),
          refId: z.string(),
          title: z.string().min(1).max(100),
          content: z.string().min(1).max(2000),
          createdAt: z.string().datetime({ offset: true }),
          updatedAt: z.string().datetime({ offset: true }),
          views: z.number().int().gte(0),
          likes: z.number().int().gte(0),
          commentCount: z.number().int().gte(0),
          author: z
            .object({
              address: z.string(),
              nickname: z.string(),
              imageUrl: z.union([z.string(), z.literal('')]).optional(),
            })
            .passthrough(),
          isLiked: z.boolean(),
          isPinned: z.boolean(),
        })
        .passthrough()
    ),
    totalLength: z.number().int().gte(0),
  })
  .passthrough();
const CreateChannelPostReqDto = z
  .object({ title: z.string().min(1).max(100), content: z.string().min(1).max(2000) })
  .passthrough();
const CommentResDto = z
  .object({
    id: z.string(),
    parentId: z.string(),
    content: z.string().min(1).max(1000),
    createdAt: z.string().datetime({ offset: true }),
    author: z
      .object({
        address: z.string(),
        nickname: z.string(),
        imageUrl: z.union([z.string(), z.literal('')]).optional(),
      })
      .passthrough(),
    likes: z.number().int().gte(0),
    commentCount: z.number().int().gte(0),
    isLiked: z.boolean(),
    positions: z
      .array(
        z
          .object({
            outcome: z.string(),
            outcomeOrder: z.number().int(),
            value: z.number().int(),
          })
          .passthrough()
      )
      .optional(),
  })
  .passthrough();

export const CreateCommentRequestSchema = z.object({ content: z.string() });
export const CreateChannelPostRequestSchema = CreateChannelPostReqDto;
export const EditChannelPostRequestSchema = EditChannelPostReqDto;
export const ReportPostRequestSchema = z.object({ postId: z.string() });

export const CommentsResponseSchema = z.object({
  comments: z.array(
    z.object({
      id: z.string(),
      parentId: z.string(),
      content: z.string(),
      createdAt: z.string(),
      author: z.object({
        address: z.string(),
        nickname: z.string(),
        imageUrl: z.string().optional(),
      }),
      likes: z.number(),
      commentCount: z.number(),
      isLiked: z.boolean(),
      positions: z
        .array(
          z.object({
            outcome: z.string(),
            outcomeOrder: z.number(),
            value: z.string(),
          })
        )
        .optional(),
    })
  ),
  totalLength: z.number(),
});

export const CommentResponseSchema = CommentResDto;
export const LikeResponseSchema = LikeResDto;
export const PostResponseSchema = PostResDto;
export const PostsResponseSchema = PostsResDto;
export const SuccessResponseSchema = z.object({ success: z.boolean() });

// Type exports
export type CreateCommentRequest = z.infer<typeof CreateCommentRequestSchema>;
export type CreateChannelPostRequest = z.infer<typeof CreateChannelPostRequestSchema>;
export type EditChannelPostRequest = z.infer<typeof EditChannelPostRequestSchema>;
export type ReportPostRequest = z.infer<typeof ReportPostRequestSchema>;

export type CommentsResponse = z.infer<typeof CommentsResponseSchema>;
export type CommentResponse = z.infer<typeof CommentResponseSchema>;
export type LikeResponse = z.infer<typeof LikeResponseSchema>;
export type PostResponse = z.infer<typeof PostResponseSchema>;
export type PostsResponse = z.infer<typeof PostsResponseSchema>;
export type SuccessResponse = z.infer<typeof SuccessResponseSchema>;
export type CommentSchema = z.infer<typeof CommentsResponseSchema>['comments'][number];

// Enums and types
export type CommentType = 'board' | 'market';
export type CommentOrder = 'latest' | 'likes';
export type PredictorsFilter = 'true' | 'false';
