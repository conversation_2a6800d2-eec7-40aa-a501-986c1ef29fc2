import { DEFAULT_MARKET_AVATAR_URL, DEFAULT_USER_AVATAR_URL } from '@/lib/constants';
import { formatUsdc } from '@/lib/format';
import { toPercentage } from '@/lib/utils';
import type {
  UserInfoResponse,
  UserActivitiesResponse,
  UserPositionsResponse,
  UserStatsResponse,
  UserSubscriptionsResponse,
  UserActivity,
  UserPosition,
  UserSubscription,
} from './user.schema.server';

export const transformUserInfoResponse = (data: UserInfoResponse) => {
  return {
    ...data,
    imageUrl: data.imageUrl ?? DEFAULT_USER_AVATAR_URL,
  };
};

export const transformUserActivity = (data: UserActivity) => {
  return {
    ...data,
    market: data.market
      ? {
          ...data.market,
          imageUrl: data.market.imageUrl ?? DEFAULT_USER_AVATAR_URL,
        }
      : null,
    value: data.value ? formatUsdc(data.value) : null,
  };
};

export const transformUserActivitiesResponse = (data: UserActivitiesResponse) => {
  return {
    ...data,
    activities: data.activities.map(transformUserActivity),
  };
};

export const transformUserPosition = (data: UserPosition) => {
  return {
    ...data,
    market: {
      ...data.market,
      imageUrl: data.market.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    },
    user: {
      ...data.user,
      imageUrl: data.user.imageUrl ?? DEFAULT_USER_AVATAR_URL,
    },
    value: formatUsdc(data.value),
    estimatedOdds: toPercentage(data.estimatedOdds),
    estimatedWin: formatUsdc(data.estimatedWin),
  };
};

export const transformUserPositionsResponse = (data: UserPositionsResponse) => {
  return {
    ...data,
    positions: data.positions.map(transformUserPosition),
  };
};

export const transformUserStatsResponse = (data: UserStatsResponse) => {
  return {
    ...data,
    positionsValue: formatUsdc(data.positionsValue),
    profit: formatUsdc(data.profit),
    volume: formatUsdc(data.volume),
  };
};

export const transformUserSubscription = (data: UserSubscription) => {
  return {
    ...data,
    imageUrl: data.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    leader: {
      ...data.leader,
      imageUrl: data.leader.imageUrl ?? DEFAULT_USER_AVATAR_URL,
    },
  };
};

export const transformUserSubscriptionsResponse = (data: UserSubscriptionsResponse) => {
  return {
    ...data,
    channels: data.channels.map(transformUserSubscription),
  };
};
