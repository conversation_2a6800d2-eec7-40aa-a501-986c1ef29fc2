import { z } from 'zod';

export const CreateUserRequestBodySchema = z.object({ referralCode: z.string() }).partial();

export const UpdateUserProfileRequestBodySchema = z
  .object({
    image: z.instanceof(File).optional(),
    nickname: z
      .string()
      .min(2)
      .max(42)
      .regex(/^[a-zA-Z0-9]*$/)
      .optional(),
    bio: z.string().max(255).optional(),
    email: z.string().email().optional().or(z.literal('')),
  })
  .partial();

export const UserInfoResponseSchema = z.object({
  address: z.string(),
  nickname: z.string(),
  imageUrl: z.string().nullable(),
  email: z.string().nullable(),
  bio: z.string(),
});

export const UserActivitiesResponseSchema = z.object({
  address: z.string(),
  activities: z.array(
    z.object({
      id: z.number(),
      market: z
        .object({
          id: z.string(),
          title: z.string(),
          imageUrl: z.string().nullable(),
        })
        .nullable(),
      type: z.string(),
      outcome: z.string().nullable(),
      value: z.string().nullable(),
      timestamp: z.string(),
    })
  ),
  totalLength: z.number(),
});

export const UserPositionsResponseSchema = z.object({
  positions: z.array(
    z.object({
      market: z.object({
        id: z.string(),
        title: z.string(),
        imageUrl: z.string().nullable(),
        status: z
          .enum([
            'OPEN',
            'REVIEWING',
            'DISPUTABLE',
            'DISPUTED',
            'CLOSED_WITHOUT_DISPUTE',
            'CLOSED_WITH_DISPUTE_ACCEPTED',
            'CLOSED_WITH_DISPUTE_REJECTED',
            'CANCELLED_WITH_UNMET',
            'CANCELLED_WITH_INVALID',
            'CANCELLED_WITH_OUTCOME_NOT_PROPOSED',
          ])
          .optional(),
      }),
      user: z.object({
        address: z.string(),
        nickname: z.string(),
        imageUrl: z.string().nullable(),
      }),
      outcome: z.string(),
      outcomeOrder: z.number(),
      value: z.string(),
      estimatedOdds: z.string(),
      estimatedWin: z.string(),
      updatedAt: z.string(),
      redeemedAt: z.string().nullish(),
      voidedAt: z.string().nullish(),
    })
  ),
  totalLength: z.number().int().gte(0),
});

export const UserStatsResponseSchema = z.object({
  address: z.string(),
  positionsValue: z.string(),
  profit: z.string(),
  volume: z.string(),
  tradedMarketsCount: z.number(),
});

export const UserSubscriptionsResponseSchema = z.object({
  channels: z.array(
    z.object({
      id: z.string(),
      name: z.string(),
      imageUrl: z.string().nullable(),
      leader: z.object({
        address: z.string(),
        nickname: z.string(),
        imageUrl: z.string().nullable(),
      }),
      subscribers: z.number(),
      isSubscribed: z.boolean(),
    })
  ),
  totalLength: z.number(),
});

// Type exports
export type CreateUserRequestBody = z.infer<typeof CreateUserRequestBodySchema>;
export type UpdateUserProfileRequestBody = z.infer<typeof UpdateUserProfileRequestBodySchema>;
export type UserInfoResponse = z.infer<typeof UserInfoResponseSchema>;
export type UserActivitiesResponse = z.infer<typeof UserActivitiesResponseSchema>;
export type UserPositionsResponse = z.infer<typeof UserPositionsResponseSchema>;
export type UserStatsResponse = z.infer<typeof UserStatsResponseSchema>;
export type UserSubscriptionsResponse = z.infer<typeof UserSubscriptionsResponseSchema>;

// Schema types for transforms
export type UserActivity = z.infer<typeof UserActivitiesResponseSchema>['activities'][number];
export type UserPosition = z.infer<typeof UserPositionsResponseSchema>['positions'][number];
export type UserSubscription = z.infer<typeof UserSubscriptionsResponseSchema>['channels'][number];
