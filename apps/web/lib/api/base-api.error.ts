import { z } from 'zod';
import { extractBodyFromResponse, formatZodError } from './utils';
import { HTTPError } from '@repo/shared/lib/base-fetcher';

enum ApiErrorCode {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  API_ERROR = 'API_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR',
  // UNAUTHORIZED = 'UNAUTHORIZED',
  // FORBIDDEN = 'FORBIDDEN',
}

export class ApiError extends Error {
  constructor(
    message: string,
    public code: ApiErrorCode,
    public statusCode?: number,
    public originalError?: unknown,
    public endpoint?: string,
    public data?: unknown
  ) {
    super(message, { cause: originalError });
    this.name = 'ApiError';
    this.code = code;
    this.statusCode = statusCode;
    this.originalError = originalError;
    this.endpoint = endpoint;
    this.data = data;
  }

  getMessage() {
    return this.message;
  }

  to<PERSON><PERSON>() {
    return {
      message: this.message,
      statusCode: this.statusCode,
      originalError: this.originalError,
      endpoint: this.endpoint,
      code: this.code,
      data: this.data,
    };
  }

  static fromValidationError(error: z.ZodError, endpoint: string, data: unknown) {
    const details = formatZodError(error);
    console.error('fromValidationError::', details);
    return new ApiError(
      `VALIDATION_ERROR::${details}`,
      ApiErrorCode.VALIDATION_ERROR,
      400,
      error,
      endpoint,
      data
    );
  }

  static async fromHTTPError(error: HTTPError<unknown>, endpoint: string) {
    const body = await extractBodyFromResponse(error.response);
    const message = body?.desc || 'Unknown error';
    return new ApiError(
      `API_ERROR::${message}`,
      ApiErrorCode.API_ERROR,
      error.response.status,
      error,
      endpoint
    );
  }

  static isApiError(error: unknown): error is ApiError {
    return error instanceof ApiError;
  }
}
