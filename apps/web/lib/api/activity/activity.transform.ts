import { DEFAULT_MARKET_AVATAR_URL, DEFAULT_USER_AVATAR_URL } from '@/lib/constants';
import { formatUsdc } from '@/lib/format';
import type { MarketActivitiesResDto, ActivitySchema } from './activity.schema.server';

export const transformActivity = (data: ActivitySchema) => {
  return {
    ...data,
    market: {
      ...data.market,
      imageUrl: data.market.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    },
    user: {
      ...data.user,
      imageUrl: data.user.imageUrl ?? DEFAULT_USER_AVATAR_URL,
    },
    formattedValue: formatUsdc(data.value),
    rawValue: BigInt(data.value),
  };
};

export const transformMarketActivitiesResponse = (data: MarketActivitiesResDto) => {
  return {
    ...data,
    activities: data.activities.map(transformActivity),
  };
};
