import { z } from 'zod';

export const MarketActivitiesResDto = z.object({
  activities: z
    .object({
      market: z.object({
        id: z.string(),
        title: z.string(),
        imageUrl: z.string().nullish(),
      }),
      user: z.object({
        address: z.string(),
        nickname: z.string(),
        imageUrl: z.string().nullish(),
      }),
      outcome: z.string(),
      outcomeOrder: z.number(),
      value: z.string(),
      timestamp: z.string(),
      transactionHash: z.string(),
    })
    .array(),
  totalLength: z.number(),
});

export type MarketActivitiesResDto = z.infer<typeof MarketActivitiesResDto>;
export type ActivitySchema = z.infer<typeof MarketActivitiesResDto>['activities'][number];
