import { ApiClient } from '@/lib/api/base-api';
import { MarketActivitiesResDto } from './activity.schema.server';
import { transformMarketActivitiesResponse } from './activity.transform';
import { ApiError } from '../base-api.error';

export class ActivityService {
  static BASE_PATH = '/data-api/v1/activity';

  static ROUTES = {
    GET: {
      '/predict': 'predict',
    },
  };
  private api: ApiClient;

  constructor() {
    this.api = new ApiClient(ActivityService.BASE_PATH);
  }

  /**
   * 예측 활동 내역 조회
   */
  async getPredictActivities(options?: { page?: number; limit?: number }) {
    const searchParams = new URLSearchParams();
    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());

    const response = await this.api.get(ActivityService.ROUTES.GET['/predict'], {
      searchParams,
    });

    const result = MarketActivitiesResDto.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        ActivityService.ROUTES.GET['/predict'],
        response
      );
    }

    return transformMarketActivitiesResponse(result.data);
  }
}

export const activityService = new ActivityService();
