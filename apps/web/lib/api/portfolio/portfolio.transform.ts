import { formatUsdc } from '@/lib/format';
import { DEFAULT_MARKET_AVATAR_URL, DEFAULT_USER_AVATAR_URL } from '@/lib/constants';
import { shortenNumber, toPercentage } from '@/lib/utils';
import type {
  UserPortfolioResponse,
  ClaimableDisputeResponse,
  PortfolioPosition,
  PortfolioActivity,
  PortfolioPositionsResponse,
  PortfolioActivitiesResponse,
  PortfolioDisputesResponse,
} from './portfolio.schema.server';
import BigNumber from 'bignumber.js';

export const transformUserPortfolioResponse = (data: UserPortfolioResponse) => {
  const formattedPositionsValue = formatUsdc(data.positionsValue);
  return {
    formattedPositionsValue,
    rawPositionsValue: BigInt(data.positionsValue),
    formattedPositionsValueShortened: shortenNumber(formattedPositionsValue),
    profit: {
      day: formatUsdc(data.profit.day),
      week: formatUsdc(data.profit.week),
      month: formatUsdc(data.profit.month),
      total: formatUsdc(data.profit.total),
    },
    // rawProfit: {
    //   day: BigInt(data.profit.day),
    //   week: BigInt(data.profit.week),
    //   month: BigInt(data.profit.month),
    //   total: BigInt(data.profit.total),
    // },
  };
};

export const transformClaimableDisputeResponse = (data: ClaimableDisputeResponse) => {
  return {
    formattedClaimable: formatUsdc(data.claimable),
    rawClaimable: BigInt(data.claimable),
  };
};

export const transformPortfolioPosition = (data: PortfolioPosition) => {
  return {
    ...data,
    market: {
      ...data.market,
      imageUrl: data.market.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    },
    user: {
      ...data.user,
      imageUrl: data.user.imageUrl ?? DEFAULT_USER_AVATAR_URL,
    },
    formattedValue: formatUsdc(data.value),
    rawValue: BigInt(data.value),
    formattedEstimatedOdds: toPercentage(data.estimatedOdds),
    formattedEstimatedWin: formatUsdc(data.estimatedWin),
    rawEstimatedWin: BigInt(data.estimatedWin),
  };
};

export const transformPortfolioPositionsResponse = (data: PortfolioPositionsResponse) => {
  return {
    ...data,
    positions: data.positions.map(transformPortfolioPosition),
  };
};

export const transformPortfolioActivity = (data: PortfolioActivity) => {
  return {
    ...data,
    market: {
      ...data.market,
      imageUrl: data.market.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
    },
    user: {
      ...data.user,
      imageUrl: data.user.imageUrl ?? DEFAULT_USER_AVATAR_URL,
    },
    formattedAmount: formatUsdc(data.amount),
    rawAmount: BigNumber(data.amount),
  };
};

export const transformPortfolioActivitiesResponse = (data: PortfolioActivitiesResponse) => {
  return {
    ...data,
    activities: data.activities.map(transformPortfolioActivity),
  };
};

export const transformPortfolioDisputesResponse = (data: PortfolioDisputesResponse) => {
  return {
    ...data,
    disputes: data.disputes.map(dispute => ({
      ...dispute,
      market: {
        ...dispute.market,
        imageUrl: dispute.market.imageUrl ?? DEFAULT_MARKET_AVATAR_URL,
      },
      formattedAmount: formatUsdc(dispute.amount),
      rawAmount: BigNumber(dispute.amount),
      formattedReward: dispute.reward ? formatUsdc(dispute.reward) : undefined,
    })),
  };
};
