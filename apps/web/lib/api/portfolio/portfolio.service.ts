import { ApiClient } from '@/lib/api/base-api';
import { ApiError } from '../base-api.error';
import {
  ActivityFilter,
  ActivityOrder,
  ClaimableDisputeResponseSchema,
  DisputeOrder,
  PortfolioActivitiesResponseSchema,
  PortfolioDisputesResponseSchema,
  PortfolioPositionsResponseSchema,
  PositionFilter,
  PositionOrder,
  UserPortfolioResponseSchema,
} from './portfolio.schema.server';
import {
  transformClaimableDisputeResponse,
  transformPortfolioActivitiesResponse,
  transformPortfolioDisputesResponse,
  transformPortfolioPositionsResponse,
  transformUserPortfolioResponse,
} from './portfolio.transform';

export class PortfolioService {
  static BASE_PATH = '/data-api/v1/portfolio';
  static ROUTES = {
    GET: {
      '/': '',
      '/activities': 'activities',
      '/dispute': 'dispute',
      '/dispute/claimable': 'dispute/claimable',
      '/positions': 'positions',
    },
  };
  private api: ApiClient;

  constructor() {
    this.api = new ApiClient(PortfolioService.BASE_PATH);
  }

  /**
   * 사용자 포트폴리오 요약 정보 조회
   */
  async getUserPortfolio() {
    const response = await this.api.get(PortfolioService.ROUTES.GET['/']);

    const result = UserPortfolioResponseSchema.safeParse(response);

    if (!result.success) {
      throw ApiError.fromValidationError(result.error, PortfolioService.ROUTES.GET['/'], response);
    }

    return transformUserPortfolioResponse(result.data);
  }

  /**
   * 포트폴리오 활동 내역 조회
   */
  async getActivities(options?: {
    page?: number;
    limit?: number;
    filter?: ActivityFilter;
    order?: ActivityOrder;
  }) {
    const searchParams = new URLSearchParams();
    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());
    if (options?.filter) searchParams.set('filter', options.filter);
    if (options?.order) searchParams.set('order', options.order);

    const response = await this.api.get(PortfolioService.ROUTES.GET['/activities'], {
      searchParams,
    });

    const result = PortfolioActivitiesResponseSchema.safeParse(response);
    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        PortfolioService.ROUTES.GET['/activities'],
        response
      );
    }
    return transformPortfolioActivitiesResponse(result.data);
  }

  /**
   * 포트폴리오 포지션 목록 조회
   */
  async getPositions(options?: {
    page?: number;
    limit?: number;
    filter?: PositionFilter;
    order?: PositionOrder;
  }) {
    const searchParams = new URLSearchParams();
    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());
    if (options?.filter) searchParams.set('filter', options.filter);
    if (options?.order) searchParams.set('order', options.order);

    const response = await this.api.get(PortfolioService.ROUTES.GET['/positions'], {
      searchParams,
    });

    const result = PortfolioPositionsResponseSchema.safeParse(response);
    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        PortfolioService.ROUTES.GET['/positions'],
        response
      );
    }
    return transformPortfolioPositionsResponse(result.data);
  }

  /**
   * 포트폴리오 분쟁 목록 조회
   */
  async getDisputes(options?: { page?: number; limit?: number; order?: DisputeOrder }) {
    const searchParams = new URLSearchParams();
    if (options?.page !== undefined) searchParams.set('page', options.page.toString());
    if (options?.limit !== undefined) searchParams.set('limit', options.limit.toString());
    if (options?.order) searchParams.set('order', options.order);

    const response = await this.api.get(PortfolioService.ROUTES.GET['/dispute'], {
      searchParams,
    });

    const result = PortfolioDisputesResponseSchema.safeParse(response);
    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        PortfolioService.ROUTES.GET['/dispute'],
        response
      );
    }
    return transformPortfolioDisputesResponse(result.data);
  }

  /**
   * 청구 가능한 분쟁 금액 조회
   */
  async getClaimableDispute() {
    const response = await this.api.get(PortfolioService.ROUTES.GET['/dispute/claimable']);
    const result = ClaimableDisputeResponseSchema.safeParse(response);
    if (!result.success) {
      throw ApiError.fromValidationError(
        result.error,
        PortfolioService.ROUTES.GET['/dispute/claimable'],
        response
      );
    }
    return transformClaimableDisputeResponse(result.data);
  }
}

export const portfolioService = new PortfolioService();
