import { formatUsdc } from '@/lib/format';
import { toLocalDate, toPercentage } from '@/lib/utils';
import type {
  ReferralBenefitResponse,
  ReferralBenefitsResponse,
  ReferralDashboardResponse,
  ReferralLeaderboardResponse,
  RewardDetailsResponse,
  ReferralBenefit,
  ReferralLeaderboardEntry,
  RewardDetail,
} from './referral.schema.server';

export const transformReferralBenefitResponse = (data: ReferralBenefitResponse) => {
  return {
    ...data,
    commissionRewardRatioPercentage: toPercentage(data.commissionRewardRatio),
    feeRebateRatioPercentage: toPercentage(data.feeRebateRatio),
    accumulatedProfitFormatted: formatUsdc(data.accumulatedProfit),
    profitFormatted: formatUsdc(data.profit),
  };
};

export const transformReferralBenefit = (data: ReferralBenefit) => {
  return {
    ...data,
    commissionRewardRatio: toPercentage(data.commissionRewardRatio),
    feeRebateRatio: toPercentage(data.feeRebateRatio),
    accumulatedProfit: formatUsdc(data.accumulatedProfit),
  };
};

export const transformReferralBenefitsResponse = (data: ReferralBenefitsResponse) => {
  return {
    ...data,
    benefits: data.benefits.map(transformReferralBenefit),
  };
};

export const transformReferralTotalResponse = (data: ReferralDashboardResponse) => {
  return {
    ...data,
    totalCommissionRewardFormatted: formatUsdc(data.totalCommissionReward),
    totalFeeRebateFormatted: formatUsdc(data.totalFeeRebate),
    rawTotalCommissionReward: BigNumber(data.totalCommissionReward),
    rawTotalFeeRebate: BigNumber(data.totalFeeRebate),
  };
};

export const transformReferralLeaderboardEntry = (data: ReferralLeaderboardEntry) => {
  return {
    ...data,
    commissionReward: formatUsdc(data.commissionReward),
    rawCommissionReward: BigNumber(data.commissionReward),
  };
};

export const transformReferralLeaderboardResponse = (data: ReferralLeaderboardResponse) => {
  return {
    ...data,
    leaderboard: data.leaderboard.map(transformReferralLeaderboardEntry),
  };
};

export const transformRewardDetail = (data: RewardDetail) => {
  return {
    ...data,
    amountFormatted: formatUsdc(data.amount),
    localTimestamp: toLocalDate(data.timestamp),
  };
};

export const transformRewardDetailsResponse = (data: RewardDetailsResponse) => {
  return {
    ...data,
    rewards: data.rewards.map(transformRewardDetail),
  };
};
