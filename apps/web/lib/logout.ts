import { disconnect } from '@wagmi/core';
import { wagmiConfig } from './web3/wagmi';
import { useGlobalStore } from '@/store/global.store';
import { queryClient } from '@/components/providers/tanstack-query-client-provider';
import { userKeys, portfolioKeys, safeSmartAccountKeys } from '@/hooks/query/query-keys';

export const logout = async () => {
  try {
    await disconnect(wagmiConfig);
  } catch (error) {
    console.error('Failed to disconnect:', error);
  } finally {
    useGlobalStore.getState().clearSession();
    queryClient.removeQueries({ queryKey: userKeys.all });
    queryClient.removeQueries({ queryKey: portfolioKeys.all });
    queryClient.removeQueries({ queryKey: safeSmartAccountKeys.all });
  }
};
