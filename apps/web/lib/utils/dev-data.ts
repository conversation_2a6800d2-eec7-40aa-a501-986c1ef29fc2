import { faker } from '@faker-js/faker';
import type { UseFormReturn } from 'react-hook-form';
import type { CreateMarketFormValues } from '@/app/(main)/create-prediction/page';

export function fillDevData(form: UseFormReturn<CreateMarketFormValues>) {
  if (process.env.NODE_ENV !== 'development') return;

  const now = new Date();

  const twoMinutesLater = new Date(now.getTime() + 5 * 60 * 1000);
  const fourMinutesLater = new Date(now.getTime() + 20 * 60 * 1000);

  const predictionDeadlineTimestamp = Math.floor(twoMinutesLater.getTime() / 1000);
  const resultConfirmDeadlineTimestamp = Math.floor(fourMinutesLater.getTime() / 1000);

  // 랜덤 예측 주제 생성
  const predictionTopics = [
    {
      subject: faker.company.name(),
      context: 'stock price',
      threshold: faker.number.int({ min: 50, max: 500 }),
      outcomes: ['Above', 'Below'],
    },
    {
      subject: faker.location.country(),
      context: 'next election winner',
      threshold: null,
      outcomes: [faker.person.lastName(), faker.person.lastName()],
    },
    {
      subject: faker.finance.currencyName(),
      context: 'price',
      threshold: faker.number.float({ min: 0.1, max: 10, fractionDigits: 2 }),
      outcomes: ['Higher', 'Lower'],
    },
    {
      subject: faker.science.chemicalElement().name,
      context: 'discovery announcement',
      threshold: null,
      outcomes: ['Yes', 'No'],
    },
    {
      subject: faker.animal.type(),
      context: 'population increase',
      threshold: faker.number.int({ min: 5, max: 50 }),
      outcomes: ['Increase', 'Decrease', 'Stable'],
    },
  ];

  const randomTopic = faker.helpers.arrayElement(predictionTopics);

  // 랜덤 타이틀 생성
  let title = '';
  if (randomTopic.threshold) {
    title = `Will ${randomTopic.subject} ${randomTopic.context} reach ${randomTopic.threshold} by ${faker.date.future({ years: 1 }).getFullYear()}?`;
  } else {
    title = `Will ${randomTopic.subject} ${randomTopic.context} happen in ${faker.date.future({ years: 1 }).getFullYear()}?`;
  }

  // 랜덤 설명 생성
  const description = `${faker.lorem.paragraph(3)} ${faker.lorem.sentence()} The outcome will be determined based on ${faker.helpers.arrayElement(
    [
      'official announcements from relevant authorities',
      'market data from major exchanges',
      'verified news sources and official reports',
      'government statistics and official publications',
      'industry reports and verified measurements',
    ]
  )}. ${faker.lorem.sentence()} ${faker.lorem.sentence()}`;

  // 랜덤 참조 URL 생성
  const referenceUrls = [
    'https://coinmarketcap.com',
    'https://www.bloomberg.com',
    'https://finance.yahoo.com',
    'https://www.reuters.com',
    'https://www.bbc.com/news',
    'https://www.cnn.com',
    'https://www.wikipedia.org',
  ];

  // 폼 데이터 설정
  form.setValue('title', title);
  form.setValue('description', description);
  form.setValue('collateralAmount', 100);
  form.setValue('category', 'Test');
  form.setValue('outcomes', randomTopic.outcomes);
  form.setValue('tags', []);
  form.setValue('referenceURL', faker.helpers.arrayElement(referenceUrls));
  form.setValue('predictionDeadline', predictionDeadlineTimestamp);
  form.setValue('resultConfirmDeadline', resultConfirmDeadlineTimestamp);
  form.setValue('broadcastURL', '');

  return {
    title,
    description,
    outcomes: randomTopic.outcomes,
    referenceURL: faker.helpers.arrayElement(referenceUrls),
    predictionDeadline: predictionDeadlineTimestamp,
    resultConfirmDeadline: resultConfirmDeadlineTimestamp,
  };
}