'use client';

/**
 * AppKit Shadow DOM Controller - Switch 버튼을 찾아서 비활성화하는 유틸리티 (지속 감시 버전)
 */

type TargetElement = HTMLButtonElement | HTMLElement;

// 전역 감시 상태
let isWatching = false;

// 디버그 로그 헬퍼
const log = (debug: boolean, msg: string, ...args: any[]) => debug && console.log(msg, ...args);

/**
 * 모든 Shadow DOM에서 accent 버튼들을 찾는 함수
 */
function findTargetElements(debug = false): TargetElement[] {
  const elements: TargetElement[] = [];

  const search = (root: Document | ShadowRoot) => {
    root.querySelectorAll('*').forEach(el => {
      if (!el.shadowRoot) return;

      // Switch 버튼
      el.shadowRoot.querySelectorAll('button, wui-button').forEach(btn => {
        if (btn.textContent?.trim() === 'Switch') {
          log(debug, '🎯 Switch 엘리먼트 발견');
          elements.push(btn as TargetElement);
        }
      });

      // Toggle 엘리먼트
      el.shadowRoot
        .querySelectorAll('[data-testid="account-toggle-preferred-account-type"]')
        .forEach(btn => {
          log(debug, '🎯 Toggle 엘리먼트 발견');
          elements.push(btn as TargetElement);
        });

      search(el.shadowRoot);
    });
  };

  search(document);
  log(debug, `📊 ${elements.length}개 엘리먼트 발견`);
  return elements;
}

/**
 * 버튼들을 비활성화하거나 제거하는 함수
 */
function processElements(elements: TargetElement[], debug = false): number {
  let count = 0;

  elements.forEach(el => {
    const isToggle = el.getAttribute('data-testid') === 'account-toggle-preferred-account-type';
    const attr = isToggle ? 'data-switch-removed' : 'data-switch-disabled';

    if (el.hasAttribute(attr)) return;

    try {
      el.setAttribute(attr, 'true');

      if (isToggle) {
        el.remove();
        log(debug, '🗑️ Toggle 제거');
      } else {
        const element = el as HTMLButtonElement;
        element.disabled = true;
        Object.assign(element.style, {
          opacity: '0.3',
          cursor: 'not-allowed',
          pointerEvents: 'none',
        });
        element.setAttribute('aria-disabled', 'true');

        ['click', 'mousedown', 'touchstart'].forEach(event => {
          element.addEventListener(
            event,
            e => {
              e.preventDefault();
              e.stopImmediatePropagation();
            },
            true
          );
        });

        log(debug, '✅ Switch 비활성화');
      }
      count++;
    } catch (error) {
      log(debug, '❌ 처리 실패:', error);
    }
  });

  return count;
}

/**
 * AppKit Shadow DOM에서 Switch 버튼을 지속적으로 감시하여 비활성화하는 함수
 * @param shouldWatch 감시 여부 (false시 감시 중지)
 * @param intervalMs 감시 주기 (기본값: 1000ms)
 * @param debug 디버그 로그 출력 여부 (기본값: false)
 * @returns 감시를 중지하는 함수
 */
export function controlAppKitShadowDOM(
  shouldWatch: boolean = true,
  intervalMs: number = 200,
  debug: boolean = false
): () => void {
  log(debug, `🚀 컨트롤러 시작 (${shouldWatch ? '감시' : '중지'})`);

  if (isWatching && shouldWatch) {
    log(debug, '⚠️ 이미 감시 중');
    return () => {};
  }

  if (!shouldWatch) {
    isWatching = false;
    return () => {};
  }

  isWatching = true;

  const check = () => {
    if (!isWatching) return;
    const elements = findTargetElements(debug);
    if (elements.length > 0) {
      const count = processElements(elements, debug);
      log(debug, `🎯 ${count}개 처리`);
    }
  };

  check();
  const id = setInterval(() => {
    if (!isWatching) {
      clearInterval(id);
      return;
    }
    check();
  }, intervalMs);

  return () => {
    isWatching = false;
    clearInterval(id);
  };
}

// 기존 함수명과의 호환성을 위한 alias
export const disableSwitchButton = controlAppKitShadowDOM;
