import { formatUnits, parseUnits } from 'viem';
import { network } from '@/lib/web3/wagmi';
import { env } from '@/lib/env';

const USDC_DECIMALS = 6;

export const formatEndTime = (date: Date | string | number) => {
  const endDate = new Date(date);
  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  const month = months[endDate.getUTCMonth()];
  const day = endDate.getUTCDate();
  const year = endDate.getUTCFullYear();
  const hours = endDate.getUTCHours().toString().padStart(2, '0');
  const minutes = endDate.getUTCMinutes().toString().padStart(2, '0');
  return `${month} ${day}, ${year}   ${hours}:${minutes} (UTC)`;
};

export const formatCurrency = (amount: number | string): string => {
  if (typeof amount === 'string') {
    amount = Number(amount);
  }

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

export const formatUsdc = (
  amount: number | bigint | string,
  options?: {
    fractionDigits?: number;
    withDollarSign?: boolean;
  }
) => {
  const fractionDigits = options?.fractionDigits ?? 2;
  const numericValue = parseFloat(formatUnits(BigInt(amount), USDC_DECIMALS));

  // 소수점이 없는 경우 정수로 표시
  const result =
    numericValue % 1 === 0 ? numericValue.toString() : numericValue.toFixed(fractionDigits);

  if (options?.withDollarSign) {
    return '$' + result;
  }

  return result;
};

export const toAmount = (amount: number | bigint | string) => {
  return parseUnits(amount.toString(), USDC_DECIMALS).toString();
};

// Generate EIP-681 compliant USDC transfer URL
export const generateTransferURL = (recipientAddress: string, amount?: string | number) => {
  const tokenAddress = env.NEXT_PUBLIC_USDC_ADDRESS;

  if (amount) {
    // Convert amount to USDC units (6 decimals)
    // Use transfer function with specific amount
    return `ethereum:${tokenAddress}@${network.id}/transfer?address=${recipientAddress}&uint256=${parseUnits(
      amount.toString(),
      USDC_DECIMALS
    ).toString()}`;
  } else {
    // Just user address - let user handle manually
    return `ethereum:${recipientAddress}@${network.id}`;
  }
};

export const toMilliSeconds = (date: Date | string | number) => {
  return new Date(date).getTime();
};

/**
 * FormData 전송 형태에 맞게 텍스트의 줄바꿈 문자를 정규화합니다.
 * textarea의 \n을 FormData가 전송하는 \r\n 형태로 변환하여
 * 시그니처 생성과 실제 전송 데이터 간의 일관성을 보장합니다.
 */
export const normalizeTextForFormData = (text: string): string => {
  return text.replace(/\n/g, '\r\n');
};

// 숫자 타입별 포맷팅 함수들
/**
 * 숫자 타입 A: 축약 버전 ($999.99K/M/B)
 */
export const formatNumberCompact = (value: number): string => {
  if (value >= 1e9) {
    return `$${(value / 1e9).toFixed(2)}B`;
  }
  if (value >= 1e6) {
    return `$${(value / 1e6).toFixed(2)}M`;
  }
  if (value >= 1e3) {
    return `$${(value / 1e3).toFixed(2)}K`;
  }
  return `$${value.toFixed(2)}`;
};

/**
 * 숫자 타입 B: 전체 노출 및 천단위 컴마 표시 ($123,456.78)
 */
export const formatNumberFull = (value: number): string => {
  return value.toLocaleString('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  });
};

/**
 * 숫자 타입 C: 전체 노출, 천단위 컴마 표시 없음 ($123456.78)
 */
export const formatNumberNoComma = (value: number): string => {
  return `$${value.toFixed(2)}`;
};

/**
 * 숫자 타입 D: 전체 노출, 천단위 컴마 표시, 소수점 없음 ($123,456)
 */
export const formatNumberInteger = (value: number): string => {
  return Math.floor(value).toLocaleString('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  });
};

/**
 * Profit/Loss용 특별 포맷터 (마이너스 표시 포함)
 */
export const formatProfitLoss = (value: number): string => {
  const formatted = formatNumberFull(Math.abs(value));
  return value < 0 ? `-${formatted}` : formatted;
};
