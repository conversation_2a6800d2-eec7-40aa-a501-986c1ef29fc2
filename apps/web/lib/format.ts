import { formatUnits, parseUnits } from 'viem';
import { network } from '@/lib/web3/wagmi';
import { env } from '@/lib/env';

const USDC_DECIMALS = 6;

export const formatEndTime = (date: Date | string | number) => {
  const endDate = new Date(date);
  const months = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  const month = months[endDate.getUTCMonth()];
  const day = endDate.getUTCDate();
  const year = endDate.getUTCFullYear();
  const hours = endDate.getUTCHours().toString().padStart(2, '0');
  const minutes = endDate.getUTCMinutes().toString().padStart(2, '0');
  return `${month} ${day}, ${year}   ${hours}:${minutes} (UTC)`;
};

export const formatCurrency = (amount: number | string): string => {
  if (typeof amount === 'string') {
    amount = Number(amount);
  }

  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 2,
    maximumFractionDigits: 2,
  }).format(amount);
};

export const formatUsdc = (
  amount: number | bigint | string,
  options?: {
    fractionDigits?: number;
    withDollarSign?: boolean;
  }
) => {
  const fractionDigits = options?.fractionDigits ?? 2;
  const numericValue = parseFloat(formatUnits(BigInt(amount), USDC_DECIMALS));

  // 소수점이 없는 경우 정수로 표시
  const result =
    numericValue % 1 === 0 ? numericValue.toString() : numericValue.toFixed(fractionDigits);

  if (options?.withDollarSign) {
    return '$' + result;
  }

  return result;
};

export const toAmount = (amount: number | bigint | string) => {
  return parseUnits(amount.toString(), USDC_DECIMALS).toString();
};

// Generate EIP-681 compliant USDC transfer URL
export const generateTransferURL = (recipientAddress: string, amount?: string | number) => {
  const tokenAddress = env.NEXT_PUBLIC_USDC_ADDRESS;

  if (amount) {
    // Convert amount to USDC units (6 decimals)
    // Use transfer function with specific amount
    return `ethereum:${tokenAddress}@${network.id}/transfer?address=${recipientAddress}&uint256=${parseUnits(
      amount.toString(),
      USDC_DECIMALS
    ).toString()}`;
  } else {
    // Just user address - let user handle manually
    return `ethereum:${recipientAddress}@${network.id}`;
  }
};

export const toMilliSeconds = (date: Date | string | number) => {
  return new Date(date).getTime();
};
