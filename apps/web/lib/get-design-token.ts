import tokens from '../constants/design-tokens.json';

// Infer the structure and keys from the imported JSON
type DesignTokens = typeof tokens;
type TokenCollectionKey = keyof DesignTokens; // '01_yistyle_css' | 'tailwindcss'

// Helper type to recursively extract the 'value' type if present, otherwise the original type
type GetValue<T> = T extends { value: infer V } ? V : T;

// Recursive Mapped Type to represent the proxy's structure
// For each property K in T, if T[K] is an object, map it recursively with TokenProxy,
// after potentially extracting its 'value'. Otherwise, just extract the 'value'.
type TokenProxy<T> = {
  // We use keyof GetValue<T> because if T is { value: Primitive }, GetValue<T> is Primitive,
  // and keyof Primitive is different from keyof { value: Primitive }
  // This part is complex and might need refinement based on the exact JSON structure.
  // A simpler version might just be: [K in keyof T]: T[K] extends object ? TokenProxy<GetValue<T[K]>> : GetValue<T[K]>;
  [K in keyof T]: T[K] extends object
    ? TokenProxy<GetValue<T[K]>> // Recursively proxy nested objects/resolved values
    : GetValue<T[K]>; // Return the resolved value type for primitives/non-objects
};

// Helper function to check if a string is a hex color code
function isHexColorString(value: any): value is string {
  if (typeof value !== 'string') {
    return false;
  }
  // Regex for #rgb, #rgba, #rrggbb, #rrggbbaa
  return /^#([0-9a-fA-F]{3,4}|[0-9a-fA-F]{6}|[0-9a-fA-F]{8})$/.test(value);
}

// Helper function to normalize hex code to 6 digits
function normalizeHexToSixDigits(hex: string): string {
  // Remove the hash and convert to uppercase
  hex = hex.replace(/^#/, '').toUpperCase();

  // Convert 3-digit hex to 6-digit (#RGB -> #RRGGBB)
  if (hex.length === 3) {
    return hex
      .split('')
      .map(char => char + char)
      .join('');
  }

  // Remove alpha channel from 4-digit or 8-digit hex
  if (hex.length === 4) {
    return hex
      .slice(0, 3)
      .split('')
      .map(char => char + char)
      .join('');
  }
  if (hex.length === 8) {
    return hex.slice(0, 6);
  }

  // Return as is if already 6-digit
  return hex;
}

// Helper function to convert token name to CSS class name format
function tokenNameToCssClass(name: string): string {
  // Remove '_color' suffix
  name = name.replace(/_color$/, '');

  // Replace underscores with hyphens and convert to lowercase
  return name.replace(/_/g, '-').toLowerCase();
}

// Internal function to create the proxy
function createTokenProxyInternal(target: Record<string, any>): any {
  // 1. Handle cases where the target itself is the final primitive value
  if (typeof target !== 'object' || target === null) {
    if (isHexColorString(target)) {
      return '#' + normalizeHexToSixDigits(target);
    }
    return target;
  }

  // 2. Handle standard token objects like { value: ..., type: ... }
  if ('value' in target && 'type' in target) {
    const value = target.value;
    if (typeof value === 'object' && value !== null) {
      return createTokenProxyInternal(value);
    } else {
      if (isHexColorString(value)) {
        return '#' + normalizeHexToSixDigits(value);
      }
      return value;
    }
  }

  // 3. Handle simple { value: X } cases
  if ('value' in target && Object.keys(target).length === 1) {
    const value = target.value;
    if (isHexColorString(value)) {
      return '#' + normalizeHexToSixDigits(value);
    }
    return value;
  }

  // 4. Create Proxy for nested objects that are not final tokens
  return new Proxy(target, {
    get(obj, prop: string | symbol): any {
      const key = String(prop);
      const decodedKey = decodeURIComponent(key.replace(/\+/g, ' '));
      let valueToProxy: any = undefined;

      // Convert the property name to match CSS class format
      const cssKey = tokenNameToCssClass(decodedKey);

      if (decodedKey in obj) {
        valueToProxy = obj[decodedKey];
      } else if (key in obj) {
        valueToProxy = obj[key];
      } else if (cssKey in obj) {
        valueToProxy = obj[cssKey];
      } else {
        console.warn(`Token path segment not found: "${key}"`);
        return undefined;
      }
      return createTokenProxyInternal(valueToProxy);
    },
    has(obj, prop: string | symbol) {
      const key = String(prop);
      const decodedKey = decodeURIComponent(key.replace(/\+/g, ' '));
      const cssKey = tokenNameToCssClass(decodedKey);
      return decodedKey in obj || key in obj || cssKey in obj;
    },
    ownKeys(obj) {
      return Reflect.ownKeys(obj).map(key =>
        typeof key === 'string'
          ? tokenNameToCssClass(encodeURIComponent(key).replace(/%20/g, '+'))
          : key
      );
    },
  });
}

/**
 * Creates a typed proxy object for accessing design tokens using chained dot/bracket notation.
 * Accessing a final token structure automatically resolves to its 'value'.
 * Hex color string values are returned in 6-digit format (#RRGGBB).
 * Color token names are normalized to match web.css class names (e.g., 'point_bright_color' -> 'point-bright').
 *
 * https://www.figma.com/community/plugin/888356646278934516/design-tokens
 *
 * @param collection - The top-level collection key (e.g., '01_yistyle_css'). Defaults to '01_yistyle_css'.
 * @returns A typed proxy object for the collection, or undefined if collection not found.
 *
 * @example
 * import { designTokens, createTokenProxy } from '@/lib/get-design-token';
 *
 * // Using the exported default instance (defaults to '01_yistyle_css')
 * if (designTokens) {
 *    // Token names match web.css class names (without 'bg-' prefix)
 *    const pointBright = designTokens.color['point-bright']; // Returns '#AADC36'
 *    const grapNoPoint3 = designTokens.color['grap-no-point3']; // Returns '#F76566'
 *    const darkBlue = designTokens.color['dark-blue']; // Returns '#2E343B'
 * }
 */
export function createTokenProxy<K extends TokenCollectionKey = '01_yistyle_css'>(
  collection: K = '01_yistyle_css' as K
): TokenProxy<DesignTokens[K]> | undefined {
  if (!(collection in tokens)) {
    console.warn(`Collection "${collection}" not found in design tokens.`);
    return undefined;
  }
  const collectionData = tokens[collection];
  return createTokenProxyInternal(collectionData as DesignTokens[K]) as TokenProxy<DesignTokens[K]>;
}

// Create and export an instance of the default proxy ('01_yistyle_css')
export const designTokens = createTokenProxy();
export const typoTokens = createTokenProxy('typography');
