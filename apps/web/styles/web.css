@import 'tailwindcss';

/* Swiper */
/* @import 'swiper/css';
@import 'swiper/css/free-mode'; */

@layer base {
  :root,
  :host {
    /* element size */
    --market-card-width: calc(325 / var(--root-font-size) * 1rem);
    --market-card-height: calc(216 / var(--root-font-size) * 1rem);
    --market-header-height: 3rem; /* 48px */
    --market-footer-height: 1.625rem; /* 26px */
    --popup-width: calc(406 / var(--root-font-size) * 1rem);

    --input-height-md: calc(36 / var(--root-font-size) * 1rem);
    --input-height-lg: calc(40 / var(--root-font-size) * 1rem);
    --footer-height: calc(160 / var(--root-font-size) * 1rem);
    --main-banner-height: calc(347 / var(--root-font-size) * 1rem);
    --tag-height: 1.5rem; /* 24px */
    --banner-image-height: calc(237 / var(--root-font-size) * 1rem);
    --dashboard-card-height: calc(166 / var(--root-font-size) * 1rem);
    --market-card-border-width: 6px;

    --nav-height: calc(58 / var(--root-font-size) * 1rem);
    --sub-nav-height: calc(50 / var(--root-font-size) * 1rem);
    /* default */

    --root-font-size: 16;
    --font-inter: 'Inter', 'Inter Fallback';
    --default-font-family: var(--font-inter);
    --default-mono-font-family: var(--font-mono);

    /* Design token colors */
    --color-point-hbright: #aadc36ff;
    --color-no-red: #f76566ff;
    --color-point-3: #5ac8faff;
    --color-icon-gray: #c1c1c1ff;
    --color-icon-dark: #3b424bff;
    --color-white: #ffffffff;
    --color-gray-1: #ecececff;
    --color-gray-3: #8e8e93;
    --color-yes-green: #8dc016ff;
    --color-mid-dark: #23252bff;
    --color-dark-deep: #1d1d1dff;
    --color-dark-blue: #2e343bff;
    --color-line: #eaeaeaff;
    --color-gray-2: #f9f9f9ff;
    --color-graph-1: #f19595ff;
    --color-graph-2: #f1c695ff;
    --color-graph-3: #f1eb8dff;
    --color-graph-4: #c9f18dff;
    --color-graph-5: #a9e9a3ff;
    --color-graph-6: #a3e9dfff;
    --color-graph-7: #b5d0f1ff;
    --color-graph-8: #bcb5f1ff;
    --color-graph-9: #ebc2efff;
    --color-graph-10: #ffc3d7ff;
    --color-graph-11: #dad2d5ff;
    --color-line: #e3e3e3;
    --page-max-width: calc(1440 / var(--root-font-size) * 1rem);

    --space-5: calc(5 / var(--root-font-size) * 1rem);
    --space-6: 0.375rem; /* 6px */
    --space-8: 0.5rem; /* 8px */
    --space-10: 0.625rem; /* 10px */
    --space-12: 0.75rem; /* 12px */
    --space-15: 0.9375rem; /* 15px */
    --space-20: 1.25rem; /* 20px */
    --space-25: calc(25 / var(--root-font-size) * 1rem);
    --space-30: 1.875rem; /* 30px */
    --space-40: calc(40 / var(--root-font-size) * 1rem);
    --space-50: calc(50 / var(--root-font-size) * 1rem);
    --space-60: calc(60 / var(--root-font-size) * 1rem);

    --avatar-size-sm: calc(24 / var(--root-font-size) * 1rem); /* 24px */
    --avatar-size-md: calc(40 / var(--root-font-size) * 1rem); /* 40px */
    --avatar-size-md2: calc(48 / var(--root-font-size) * 1rem); /* 48px */
    --avatar-size-md3: calc(60 / var(--root-font-size) * 1rem); /* 60px */
    --avatar-size-lg: calc(80 / var(--root-font-size) * 1rem); /* 80px */

    --round-sm: 0.25rem; /* 4px */
    --round-md: 0.75rem; /* 12px */
    --round-lg: 1.5rem; /* 24px */

    --button-height-xxs: calc(18 / var(--root-font-size) * 1rem); /* 18px */
    --button-height-xs: calc(24 / var(--root-font-size) * 1rem); /* 24px */
    --button-height-sm30: calc(30 / var(--root-font-size) * 1rem); /* 30px */
    --button-height-sm-1: calc(34 / var(--root-font-size) * 1rem); /* 34px */
    --button-height-sm-2: calc(36 / var(--root-font-size) * 1rem); /* 36px */
    --button-height-md: calc(40 / var(--root-font-size) * 1rem); /* 40px */
    --button-height-lg: calc(46 / var(--root-font-size) * 1rem); /* 46px */
    --button-height-xl-1: calc(54 / var(--root-font-size) * 1rem); /* 54px */
    --button-height-xl-2: calc(56 / var(--root-font-size) * 1rem); /* 56px */

    --text-size-xxs8: 0.5rem; /* 8px */
    --text-size-xxs10: 0.625rem; /* 10px */
    --text-size-xxs: 0.6875rem; /* 11px */
    --text-size-xs: 0.75rem; /* 12px */
    --text-size-sm13: 0.8125rem; /* 13px */
    --text-size-sm: calc(14 / var(--root-font-size) * 1rem);
    --text-size-base15: 0.9375rem; /* 15px */
    --text-size-base: 1rem; /* 16px */
    --text-size-lg: 1.125rem; /* 18px */
    --text-size-xl: 1.25rem; /* 20px */
    --text-size-2xl: 1.5rem; /* 24px */
    --text-size-3xl: 1.875rem; /* 30px */
    --text-size-4xl: 2.25rem; /* 36px */
    --text-size-5xl: 3rem; /* 48px */
    /* --text-size-6xl: 3.75rem; 60px */
    --text-size-6xl: calc(64 / var(--root-font-size) * 1rem);
    --text-size-7xl: 4.5rem; /* 72px */
    --text-size-8xl: 6rem; /* 96px */
    --text-size-9xl: 8rem; /* 128px */

    --tab-trigger-width: calc(200 / var(--root-font-size) * 1rem);
  }
}

@theme {
  /* https://tailwindcss.com/docs/theme#theme-variable-namespaces */

  --color-yes-green: var(--color-yes-green);
  --color-no-red: var(--color-no-red);
  --color-sky: var(--color-point-3);
  --color-point-3: var(--color-point-3);
  --color-gray-1: var(--color-gray-1);
  --color-gray-2: var(--color-gray-2);
  --color-gray-3: var(--color-gray-3);
  --color-icon-gray: var(--color-icon-gray);
  --color-line: var(--color-line);
  --color-dark: var(--color-icon-dark);
  --color-icon-dark: var(--color-icon-dark);
  --color-mid-dark: var(--color-mid-dark);
  --color-dark-deep: var(--color-dark-deep);
  --color-graph-1: var(--color-graph-1);
  --color-graph-2: var(--color-graph-2);
  --color-graph-3: var(--color-graph-3);
  --color-graph-4: var(--color-graph-4);
  --color-graph-5: var(--color-graph-5);
  --color-graph-6: var(--color-graph-6);
  --color-graph-7: var(--color-graph-7);
  --color-graph-8: var(--color-graph-8);
  --color-graph-9: var(--color-graph-9);
  --color-graph-10: var(--color-graph-10);
  --color-graph-11: var(--color-graph-11);

  --spacing-space-5: var(--space-5);
  --spacing-space-6: var(--space-6);
  --spacing-space-8: var(--space-8);
  --spacing-space-10: var(--space-10);
  --spacing-space-12: var(--space-12);
  --spacing-space-15: var(--space-15);
  --spacing-space-20: var(--space-20);
  --spacing-space-25: var(--space-25);
  --spacing-space-30: var(--space-30);
  --spacing-space-40: var(--space-40);
  --spacing-space-50: var(--space-50);
  --spacing-space-60: var(--space-60);

  /* --text-size */
  --text-size-xxs8: var(--text-size-xxs8);
  --text-size-xxs10: var(--text-size-xxs10);
  --text-size-xxs: var(--text-size-xxs);
  --text-size-xs: var(--text-size-xs);
  --text-size-sm: var(--text-size-sm);
  --text-size-sm13: var(--text-size-sm13);
  --text-size-base15: var(--text-size-base15);
  --text-size-base: var(--text-size-base);
  --text-size-lg: var(--text-size-lg);
  --text-size-xl: var(--text-size-xl);
  --text-size-2xl: var(--text-size-2xl);
  --text-size-3xl: var(--text-size-3xl);
  --text-size-4xl: var(--text-size-4xl);
  --text-size-5xl: var(--text-size-5xl);
  --text-size-6xl: var(--text-size-6xl);
  --text-size-7xl: var(--text-size-7xl);
  --text-size-8xl: var(--text-size-8xl);
  --text-size-9xl: var(--text-size-9xl);

  /* radius */
  --radius-round-sm: var(--round-sm);
  --radius-round-md: var(--round-md);
  --radius-round-lg: var(--round-lg);

  /* font weights */
  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;
}

button {
  cursor: pointer;
}
button:disabled {
  cursor: not-allowed;
}

svg[data-label='icon'] path[fill],
svg[data-label='icon'] rect[fill],
svg[data-label='icon'] circle[fill],
svg[data-label='icon'] g[fill] {
  fill: currentColor;
}

svg[data-label='icon'] path[stroke],
svg[data-label='icon'] rect[stroke],
svg[data-label='icon'] circle[stroke] {
  stroke: currentColor;
}

@layer components {
  .dashboard-h2 {
    @apply text-size-base text-mid-dark font-bold;
  }
  .dashboard-h3 {
    @apply text-size-sm text-mid-dark font-semibold;
  }

  .page {
    @apply px-space-30 py-space-30 mx-auto w-full max-w-(--page-max-width);
  }
  .shell {
    @apply mx-auto max-w-(--page-max-width);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeIn {
  animation: fadeIn 300ms ease-out forwards;
}
