# 마켓 상세 페이지

베팅이 이루어지는 마켓의 상세 페이지입니다.

## 1. 마켓 정보 영역

### 1-1. 마켓 이미지
- 마켓 생성시 업로드한 이미지 노출
- 등록하지 않을 시 디폴트 이미지 노출 (디자인 필요)

### 1-2. 마켓 타이틀
- 마켓 생성시 등록한 마켓 제목

### 1-3. 베팅 종료 일시
- 형식: `March dd, yyyy hh:mm (UTC)`

### 1-4. 마켓 총 베팅액
- 소수점 3자리에서 반올림하여 2자리까지 노출

### 1-5. 마켓 베팅자 수

### 1-6. 마켓 최소 베팅 수
- 베팅이 10번 미만인 경우 베팅 취소 및 전액 환불, $0.1 수수료도 환불
- 베팅이 10회 일어난 경우 해당 UI는 제거

### 1-7. 마켓 최소 베팅 도움말 툴팁
- 문구: "Minimum 10 predictions needed. All predicts refunded if not met."

### 1-8. 라이브 영상 영역
- 마켓 생성시 임베디드 가능한 영상 URL을 등록한 경우 노출
- 없는 경우 해당 영역은 제거되고, 하단의 베팅 그래프가 노출

## 2. 베팅 영역

### 2-1. 선택지명

### 2-2. 베팅 금액 입력 영역
- 가이드 문구: "Minimum Amount $1"

### 2-3. 이용자의 USDC Balance

### 2-4. [버튼] 베팅 금액 입력 버튼
- $1, $10, $100 각 버튼을 클릭할때마다 입력
- max 버튼 클릭시 이용자의 USDC 잔고 전액 입력
- USDC 잔고와 해당 선택지 베팅 가능금액 중 MIN값 입력

### 2-5. 예상 배당률
- 공식: `1 + (패배풀 * 수수료 / 내가 배팅한 풀 볼륨+나의 볼륨)`
- 소수점 3자리에서 반올림 후 2자리까지 노출

### 2-6. 승리시 예상 획득 금액
- 공식: `(Estimated Odds) * (이용자 베팅액)`
- 소수점 3자리에서 반올림 후 2자리까지 노출

### 2-7. [버튼] 베팅 버튼
- 클릭시 메타마스크 서명 노출

### 2-8. 베팅 안내 문구
- "Based on current predictions, the final odds are determined at the close of predictions."

### 2-9. 네트워크 이용 수수료
- 베팅시 $0.1를 자동으로 차감
- 마우스 호버시 문구 노출: "Maximum predictable volume per outcome set by the channel owner"

### 2-10. 현재 선택지에 베팅한 금액 / 선택지 베팅 가능 금액
- 물음표 툴팁 클릭시 문구 노출

## 3. 베팅시 예외 사항

### 3-1. Gnosis Safe 주소 미생성
- Gnosis safe 주소가 생성되지 않은 경우에 베팅 시도시 주소 생성 절차 진행

### 3-2. 베팅 서명 완료
- "Prediction Completed"
- "Prediction history can be checked on the profile."

### 3-3. 이용자가 서명을 취소한 경우
- "Transaction Failed"
- "User rejected the request."
- "For a better user experience, PredictGo applies a fee to cover all associated network charges."

### 3-4. 나머지 오류시
- "Transaction Error"
- "Please try again."

