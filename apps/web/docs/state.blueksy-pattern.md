import React, { useReducer, useCallback, useContext, useMemo, useRef, useEffect } from 'react'

// ===== 타입 정의 =====
interface UserProfile {
  id: string
  email: string
  name: string
  avatar?: string
  bio?: string
  createdAt: string
}

interface UserSettings {
  theme: 'light' | 'dark' | 'system'
  language: 'ko' | 'en'
  notifications: {
    email: boolean
    push: boolean
    marketing: boolean
  }
  privacy: {
    profileVisibility: 'public' | 'friends' | 'private'
    showOnlineStatus: boolean
  }
}

interface UserActivity {
  status: 'online' | 'away' | 'busy' | 'offline'
  lastSeen: string
  currentDevice: string
}

// 전체 유저 상태
interface UserState {
  profile: UserProfile | null
  settings: UserSettings | null
  activity: UserActivity | null
  isLoading: {
    profile: boolean
    settings: boolean
  }
  errors: {
    profile: string | null
    settings: string | null
  }
  needsPersist: boolean
}

// Discriminated Union Actions
type UserAction =
  // Profile Actions
  | { type: 'SET_PROFILE'; profile: UserProfile }
  | { type: 'UPDATE_PROFILE'; updates: Partial<UserProfile> }
  | { type: 'PROFILE_LOADING'; isLoading: boolean }
  | { type: 'PROFILE_ERROR'; error: string }
  
  // Settings Actions  
  | { type: 'SET_SETTINGS'; settings: UserSettings }
  | { type: 'UPDATE_THEME'; theme: UserSettings['theme'] }
  | { type: 'UPDATE_LANGUAGE'; language: UserSettings['language'] }
  | { type: 'UPDATE_NOTIFICATIONS'; notifications: Partial<UserSettings['notifications']> }
  | { type: 'UPDATE_PRIVACY'; privacy: Partial<UserSettings['privacy']> }
  | { type: 'SETTINGS_LOADING'; isLoading: boolean }
  | { type: 'SETTINGS_ERROR'; error: string }
  
  // Activity Actions
  | { type: 'UPDATE_ACTIVITY'; activity: UserActivity }
  | { type: 'SET_STATUS'; status: UserActivity['status'] }
  
  // General Actions
  | { type: 'RESET_USER_STATE' }
  | { type: 'SYNC_FROM_CACHE'; state: Partial<UserState> }

// Context 타입들
interface UserProfileContext {
  profile: UserProfile | null
  isProfileLoading: boolean
  profileError: string | null
}

interface UserSettingsContext {
  settings: UserSettings | null
  isSettingsLoading: boolean
  settingsError: string | null
}

interface UserActivityContext {
  activity: UserActivity | null
  isOnline: boolean
}

interface UserApiContext {
  // Profile
  loadProfile: () => Promise<void>
  updateProfile: (updates: Partial<UserProfile>) => Promise<void>
  
  // Settings
  loadSettings: () => Promise<void>
  updateTheme: (theme: UserSettings['theme']) => void
  updateLanguage: (language: UserSettings['language']) => void
  updateNotifications: (notifications: Partial<UserSettings['notifications']>) => void
  updatePrivacy: (privacy: Partial<UserSettings['privacy']>) => void
  
  // Activity
  setStatus: (status: UserActivity['status']) => void
  
  // General
  resetUserState: () => void
}

// ===== 기본 설정값 =====
const defaultSettings: UserSettings = {
  theme: 'system',
  language: 'ko',
  notifications: {
    email: true,
    push: true,
    marketing: false
  },
  privacy: {
    profileVisibility: 'public',
    showOnlineStatus: true
  }
}

// ===== Reducer =====
function userReducer(state: UserState, action: UserAction): UserState {
  console.log('🔄 User Reducer:', action.type)

  switch (action.type) {
    // Profile Actions
    case 'SET_PROFILE': {
      // 동일한 프로필이면 상태 변경하지 않음 (참조 동등성)
      if (JSON.stringify(state.profile) === JSON.stringify(action.profile)) {
        return state
      }

      return {
        ...state,
        profile: action.profile,
        needsPersist: true
      }
    }

    case 'UPDATE_PROFILE': {
      if (!state.profile) return state
      
      const updatedProfile = { ...state.profile, ...action.updates }
      
      // 실제 변경이 있는지 확인
      if (JSON.stringify(state.profile) === JSON.stringify(updatedProfile)) {
        return state
      }

      return {
        ...state,
        profile: updatedProfile,
        needsPersist: true
      }
    }

    case 'PROFILE_LOADING': {
      if (state.isLoading.profile === action.isLoading) return state
      
      return {
        ...state,
        isLoading: { ...state.isLoading, profile: action.isLoading },
        errors: { ...state.errors, profile: null }
      }
    }

    case 'PROFILE_ERROR': {
      return {
        ...state,
        isLoading: { ...state.isLoading, profile: false },
        errors: { ...state.errors, profile: action.error }
      }
    }

    // Settings Actions
    case 'SET_SETTINGS': {
      if (JSON.stringify(state.settings) === JSON.stringify(action.settings)) {
        return state
      }
      
      return {
        ...state,
        settings: action.settings,
        needsPersist: true
      }
    }

    case 'UPDATE_THEME': {
      if (!state.settings || state.settings.theme === action.theme) return state
      
      return {
        ...state,
        settings: { ...state.settings, theme: action.theme },
        needsPersist: true
      }
    }

    case 'UPDATE_LANGUAGE': {
      if (!state.settings || state.settings.language === action.language) return state
      
      return {
        ...state,
        settings: { ...state.settings, language: action.language },
        needsPersist: true
      }
    }

    case 'UPDATE_NOTIFICATIONS': {
      if (!state.settings) return state
      
      const updatedNotifications = {
        ...state.settings.notifications,
        ...action.notifications
      }
      
      // 변경사항 확인
      if (JSON.stringify(state.settings.notifications) === JSON.stringify(updatedNotifications)) {
        return state
      }
      
      return {
        ...state,
        settings: {
          ...state.settings,
          notifications: updatedNotifications
        },
        needsPersist: true
      }
    }

    case 'UPDATE_PRIVACY': {
      if (!state.settings) return state
      
      const updatedPrivacy = {
        ...state.settings.privacy,
        ...action.privacy
      }
      
      if (JSON.stringify(state.settings.privacy) === JSON.stringify(updatedPrivacy)) {
        return state
      }
      
      return {
        ...state,
        settings: {
          ...state.settings,
          privacy: updatedPrivacy
        },
        needsPersist: true
      }
    }

    // Activity Actions
    case 'UPDATE_ACTIVITY': {
      return {
        ...state,
        activity: action.activity
        // activity는 영속화하지 않음 (실시간 데이터)
      }
    }

    case 'SET_STATUS': {
      if (!state.activity || state.activity.status === action.status) return state
      
      return {
        ...state,
        activity: {
          ...state.activity,
          status: action.status,
          lastSeen: new Date().toISOString()
        }
      }
    }

    // General Actions
    case 'RESET_USER_STATE': {
      return {
        profile: null,
        settings: null,
        activity: null,
        isLoading: { profile: false, settings: false },
        errors: { profile: null, settings: null },
        needsPersist: true
      }
    }

    case 'SYNC_FROM_CACHE': {
      return {
        ...state,
        ...action.state,
        needsPersist: false
      }
    }

    default:
      return state
  }
}

// ===== API 시뮬레이션 =====
class UserApiClient {
  async fetchProfile(userId: string): Promise<UserProfile> {
    await new Promise(resolve => setTimeout(resolve, 1000))

    return {
      id: userId,
      email: `user${userId}@example.com`,
      name: `User ${userId}`,
      avatar: `https://ui-avatars.com/api/?name=User+${userId}`,
      bio: '안녕하세요!',
      createdAt: new Date().toISOString()
    }
  }

  async updateProfile(userId: string, updates: Partial<UserProfile>): Promise<UserProfile> {
    await new Promise(resolve => setTimeout(resolve, 500))

    // 실제로는 서버에 업데이트하고 결과 반환
    return {
      id: userId,
      email: `user${userId}@example.com`,
      name: updates.name || `User ${userId}`,
      avatar: updates.avatar,
      bio: updates.bio,
      createdAt: new Date().toISOString()
    }
  }

  async fetchSettings(userId: string): Promise<UserSettings> {
    await new Promise(resolve => setTimeout(resolve, 800))

    // 실제로는 서버에서 가져옴
    return defaultSettings
  }
}

// ===== Context 생성 =====
const ProfileContext = React.createContext<UserProfileContext | null>(null)
const SettingsContext = React.createContext<UserSettingsContext | null>(null)
const ActivityContext = React.createContext<UserActivityContext | null>(null)
const UserApiContext = React.createContext<UserApiContext | null>(null)

// ===== Provider 컴포넌트 =====
interface UserProviderProps {
  children: React.ReactNode
  userId: string | null
}

export function UserProvider({ children, userId }: UserProviderProps) {
  const abortControllerRef = useRef<AbortController | null>(null)
  const apiClient = useRef(new UserApiClient()).current
  
  const [state, dispatch] = useReducer(userReducer, {
    profile: null,
    settings: null,
    activity: null,
    isLoading: { profile: false, settings: false },
    errors: { profile: null, settings: null },
    needsPersist: false
  })

  // userId가 변경되면 상태 초기화
  useEffect(() => {
    if (!userId) {
      dispatch({ type: 'RESET_USER_STATE' })
    }
  }, [userId])

  // ===== API 메서드들 =====
  const loadProfile = useCallback(async () => {
    if (!userId) return

    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
    abortControllerRef.current = new AbortController()
    const signal = abortControllerRef.current.signal
    
    dispatch({ type: 'PROFILE_LOADING', isLoading: true })
    
    try {
      const profile = await apiClient.fetchProfile(userId)
      
      if (!signal.aborted) {
        dispatch({ type: 'SET_PROFILE', profile })
      }
    } catch (error) {
      if (!signal.aborted) {
        dispatch({ type: 'PROFILE_ERROR', error: '프로필을 불러올 수 없습니다' })
      }
    }
  }, [userId, apiClient])

  const updateProfile = useCallback(async (updates: Partial<UserProfile>) => {
    if (!userId) return

    try {
      const updatedProfile = await apiClient.updateProfile(userId, updates)
      dispatch({ type: 'SET_PROFILE', profile: updatedProfile })
    } catch (error) {
      dispatch({ type: 'PROFILE_ERROR', error: '프로필 업데이트 실패' })
    }
  }, [userId, apiClient])

  const loadSettings = useCallback(async () => {
    if (!userId) return

    dispatch({ type: 'SETTINGS_LOADING', isLoading: true })
    
    try {
      const settings = await apiClient.fetchSettings(userId)
      dispatch({ type: 'SET_SETTINGS', settings })
    } catch (error) {
      dispatch({ type: 'SETTINGS_ERROR', error: '설정을 불러올 수 없습니다' })
    }
  }, [userId, apiClient])

  const updateTheme = useCallback((theme: UserSettings['theme']) => {
    dispatch({ type: 'UPDATE_THEME', theme })
  }, [])

  const updateLanguage = useCallback((language: UserSettings['language']) => {
    dispatch({ type: 'UPDATE_LANGUAGE', language })
  }, [])

  const updateNotifications = useCallback((notifications: Partial<UserSettings['notifications']>) => {
    dispatch({ type: 'UPDATE_NOTIFICATIONS', notifications })
  }, [])

  const updatePrivacy = useCallback((privacy: Partial<UserSettings['privacy']>) => {
    dispatch({ type: 'UPDATE_PRIVACY', privacy })
  }, [])

  const setStatus = useCallback((status: UserActivity['status']) => {
    dispatch({ type: 'SET_STATUS', status })
  }, [])

  const resetUserState = useCallback(() => {
    dispatch({ type: 'RESET_USER_STATE' })
  }, [])

  // ===== 영속성 관리 =====
  useEffect(() => {
    if (state.needsPersist && userId) {
      state.needsPersist = false // 의도적인 뮤테이션

      const dataToCache = {
        profile: state.profile,
        settings: state.settings
      }
      
      localStorage.setItem(`user:${userId}`, JSON.stringify(dataToCache))
      console.log('💾 Cached user data:', dataToCache)
    }
  }, [state, userId])

  // 캐시에서 로드
  useEffect(() => {
    if (!userId) return

    const cached = localStorage.getItem(`user:${userId}`)
    if (cached) {
      try {
        const data = JSON.parse(cached)
        dispatch({ type: 'SYNC_FROM_CACHE', state: data })
      } catch (error) {
        console.error('Failed to load cached data:', error)
      }
    }
    
    // 캐시와 관계없이 최신 데이터 로드
    loadProfile()
    loadSettings()
  }, [userId, loadProfile, loadSettings])

  // ===== Activity 시뮬레이션 =====
  useEffect(() => {
    if (!userId) return

    // 초기 활동 상태 설정
    dispatch({
      type: 'UPDATE_ACTIVITY',
      activity: {
        status: 'online',
        lastSeen: new Date().toISOString(),
        currentDevice: 'web'
      }
    })
    
    // 5분 후 자동으로 away 상태로
    const timer = setTimeout(() => {
      dispatch({ type: 'SET_STATUS', status: 'away' })
    }, 5 * 60 * 1000)
    
    return () => clearTimeout(timer)
  }, [userId])

  // ===== Context 값들 (메모이제이션) =====
  const profileValue = useMemo<UserProfileContext>(() => ({
    profile: state.profile,
    isProfileLoading: state.isLoading.profile,
    profileError: state.errors.profile
  }), [state.profile, state.isLoading.profile, state.errors.profile])

  const settingsValue = useMemo<UserSettingsContext>(() => ({
    settings: state.settings,
    isSettingsLoading: state.isLoading.settings,
    settingsError: state.errors.settings
  }), [state.settings, state.isLoading.settings, state.errors.settings])

  const activityValue = useMemo<UserActivityContext>(() => ({
    activity: state.activity,
    isOnline: state.activity?.status === 'online' || state.activity?.status === 'away'
  }), [state.activity])

  const apiValue = useMemo<UserApiContext>(() => ({
    loadProfile,
    updateProfile,
    loadSettings,
    updateTheme,
    updateLanguage,
    updateNotifications,
    updatePrivacy,
    setStatus,
    resetUserState
  }), [
    loadProfile, updateProfile, loadSettings,
    updateTheme, updateLanguage, updateNotifications,
    updatePrivacy, setStatus, resetUserState
  ])

  return (
    <ProfileContext.Provider value={profileValue}>
      <SettingsContext.Provider value={settingsValue}>
        <ActivityContext.Provider value={activityValue}>
          <UserApiContext.Provider value={apiValue}>
            {children}
          </UserApiContext.Provider>
        </ActivityContext.Provider>
      </SettingsContext.Provider>
    </ProfileContext.Provider>
  )
}

// ===== Custom Hooks =====
export function useUserProfile() {
  const context = useContext(ProfileContext)
  if (!context) throw new Error('useUserProfile must be used within UserProvider')
  return context
}

export function useUserSettings() {
  const context = useContext(SettingsContext)
  if (!context) throw new Error('useUserSettings must be used within UserProvider')
  return context
}

export function useUserActivity() {
  const context = useContext(ActivityContext)
  if (!context) throw new Error('useUserActivity must be used within UserProvider')
  return context
}

export function useUserApi() {
  const context = useContext(UserApiContext)
  if (!context) throw new Error('useUserApi must be used within UserProvider')
  return context
}

// ===== 데모 컴포넌트들 =====
function ProfileCard() {
  const { profile, isProfileLoading } = useUserProfile()
  const { updateProfile } = useUserApi()
  const [isEditing, setIsEditing] = React.useState(false)
  const [bio, setBio] = React.useState('')

  React.useEffect(() => {
    if (profile?.bio) setBio(profile.bio)
  }, [profile?.bio])

  if (isProfileLoading) return <div>프로필 로딩중...</div>

  return (
    <div className="p-4 bg-white rounded shadow">
      <h3 className="font-bold mb-2">프로필</h3>
      {profile && (
        <div>
          <img src={profile.avatar} alt={profile.name} className="w-20 h-20 rounded-full mb-2" />
          <p className="font-semibold">{profile.name}</p>
          <p className="text-gray-600">{profile.email}</p>

          {isEditing ? (
            <div className="mt-2">
              <textarea
                value={bio}
                onChange={(e) => setBio(e.target.value)}
                className="w-full p-2 border rounded"
                rows={3}
              />
              <div className="flex gap-2 mt-2">
                <button
                  onClick={async () => {
                    await updateProfile({ bio })
                    setIsEditing(false)
                  }}
                  className="px-3 py-1 bg-blue-500 text-white rounded text-sm"
                >
                  저장
                </button>
                <button
                  onClick={() => {
                    setBio(profile.bio || '')
                    setIsEditing(false)
                  }}
                  className="px-3 py-1 bg-gray-300 rounded text-sm"
                >
                  취소
                </button>
              </div>
            </div>
          ) : (
            <div className="mt-2">
              <p className="text-sm text-gray-700">{profile.bio}</p>
              <button
                onClick={() => setIsEditing(true)}
                className="mt-1 text-blue-500 text-sm hover:underline"
              >
                소개 수정
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

function SettingsPanel() {
  const { settings } = useUserSettings()
  const { updateTheme, updateLanguage, updateNotifications, updatePrivacy } = useUserApi()

  if (!settings) return null

  return (
    <div className="p-4 bg-white rounded shadow">
      <h3 className="font-bold mb-4">설정</h3>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium mb-1">테마</label>
          <select
            value={settings.theme}
            onChange={(e) => updateTheme(e.target.value as UserSettings['theme'])}
            className="w-full p-2 border rounded"
          >
            <option value="light">라이트</option>
            <option value="dark">다크</option>
            <option value="system">시스템</option>
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">언어</label>
          <select
            value={settings.language}
            onChange={(e) => updateLanguage(e.target.value as UserSettings['language'])}
            className="w-full p-2 border rounded"
          >
            <option value="ko">한국어</option>
            <option value="en">English</option>
          </select>
        </div>

        <div>
          <h4 className="text-sm font-medium mb-2">알림 설정</h4>
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={settings.notifications.email}
              onChange={(e) => updateNotifications({ email: e.target.checked })}
            />
            <span className="text-sm">이메일 알림</span>
          </label>
          <label className="flex items-center gap-2 mt-1">
            <input
              type="checkbox"
              checked={settings.notifications.push}
              onChange={(e) => updateNotifications({ push: e.target.checked })}
            />
            <span className="text-sm">푸시 알림</span>
          </label>
        </div>

        <div>
          <h4 className="text-sm font-medium mb-2">프라이버시</h4>
          <label className="flex items-center gap-2">
            <input
              type="checkbox"
              checked={settings.privacy.showOnlineStatus}
              onChange={(e) => updatePrivacy({ showOnlineStatus: e.target.checked })}
            />
            <span className="text-sm">온라인 상태 표시</span>
          </label>
        </div>
      </div>
    </div>
  )
}

function ActivityIndicator() {
  const { activity, isOnline } = useUserActivity()
  const { setStatus } = useUserApi()

  const statusColors = {
    online: 'bg-green-500',
    away: 'bg-yellow-500',
    busy: 'bg-red-500',
    offline: 'bg-gray-500'
  }

  const statusLabels = {
    online: '온라인',
    away: '자리 비움',
    busy: '바쁨',
    offline: '오프라인'
  }

  return (
    <div className="p-4 bg-white rounded shadow">
      <h3 className="font-bold mb-2">활동 상태</h3>

      <div className="flex items-center gap-2 mb-3">
        <div className={`w-3 h-3 rounded-full ${activity ? statusColors[activity.status] : 'bg-gray-300'}`} />
        <span>{activity ? statusLabels[activity.status] : '없음'}</span>
      </div>

      <div className="flex gap-2">
        {(['online', 'away', 'busy', 'offline'] as const).map(status => (
          <button
            key={status}
            onClick={() => setStatus(status)}
            className={`px-3 py-1 rounded text-sm ${
              activity?.status === status
                ? 'bg-blue-500 text-white'
                : 'bg-gray-200 hover:bg-gray-300'
            }`}
          >
            {statusLabels[status]}
          </button>
        ))}
      </div>

      {activity && (
        <p className="text-xs text-gray-500 mt-2">
          마지막 활동: {new Date(activity.lastSeen).toLocaleTimeString()}
        </p>
      )}
    </div>
  )
}

// ===== 메인 데모 앱 =====
export default function App() {
  const [userId, setUserId] = React.useState<string | null>(null)

  return (
    <div className="max-w-4xl mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">
        Bluesky 패턴 유저 상태 관리
      </h1>

      {!userId ? (
        <div className="text-center py-8">
          <button
            onClick={() => setUserId('123')}
            className="px-6 py-3 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            유저로 로그인
          </button>
        </div>
      ) : (
        <UserProvider userId={userId}>
          <div className="mb-4 flex justify-between items-center">
            <span>로그인됨: User {userId}</span>
            <button
              onClick={() => setUserId(null)}
              className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
            >
              로그아웃
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <ProfileCard />
            <ActivityIndicator />
            <div className="md:col-span-2">
              <SettingsPanel />
            </div>
          </div>

          <div className="mt-4 p-4 bg-gray-50 rounded text-sm">
            <p>💡 구현된 패턴:</p>
            <ul className="list-disc list-inside mt-2">
              <li>Four-Context 아키텍처 (Profile, Settings, Activity, API)</li>
              <li>세밀한 상태 분리로 불필요한 리렌더링 방지</li>
              <li>Reducer에서 참조 동등성 체크로 최적화</li>
              <li>선택적 영속성 (프로필/설정만 저장, 활동상태는 제외)</li>
              <li>AbortController로 비동기 작업 취소</li>
              <li>userId 변경 시 자동 상태 초기화</li>
            </ul>
          </div>
        </UserProvider>
      )}
    </div>
  )
}
