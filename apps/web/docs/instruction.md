# React Component Creation Guide

This guide outlines the standards and best practices for creating React components in this project.

## Component Organization

The project organizes components into several key directories:

- `/components/ui/`: Base UI components like buttons, inputs, and labels
- `/components/common/`: Shared components used across multiple pages
- `/components/icons/`: Icon components organized by size and type
- `/components/layouts/`: Layout components like headers, footers, and navigation
- `/components/providers/`: Context providers for themes and authentication

## Component Naming Conventions

- **File Names**: Use PascalCase for component files (e.g., `BaseButton.tsx`)
- **Story Files**: Name story files with the same name plus `.stories.tsx` (e.g., `BaseButton.stories.tsx`)
- **Base Components**: Prefix shared/base components with "Base" (e.g., `BaseButton`, `BaseInput`)
- **Specific Components**: Use descriptive names for specific implementations (e.g., `PredictionLabel`, `MarketCountdown`)

## Component Structure

### Basic Component Template

```tsx
import { ComponentProps } from 'react';
import { cn } from '@repo/ui/lib/utils';

// Define component props interface
interface ComponentNameProps extends ComponentProps<'div'> {
  // Define custom props
  customProp?: string;
}

export function ComponentName({
  className,
  customProp = 'default',
  ...props
}: ComponentNameProps) {
  return (
    <div
      className={cn(
        'base-styles-here',
        className
      )}
      {...props}
    >
      {/* Component content */}
    </div>
  );
}
```

### Component with Variants

For components with multiple variants:

```tsx
import { cn, cva, VariantProps } from '@repo/ui/lib/utils';
import type { ComponentProps } from 'react';

// Define variants
const componentVariants = {
  primary: 'bg-primary text-white',
  secondary: 'bg-secondary text-dark',
};

type ComponentVariant = keyof typeof componentVariants;

// Define component props
type ComponentNameProps = ComponentProps<'div'> & {
  variant?: ComponentVariant;
}

const variantStyles = cva('base-styles', {
  variants: {
    variant: componentVariants,
  },
});

export function ComponentName({
  className,
  variant = 'primary',
  ...props
}: ComponentNameProps) {
  return (
    <div
      className={cn(
        variantStyles({ variant }),
        className
      )}
      {...props}
    />
  );
}
```

## Styling Approach

This project uses Tailwind CSS for styling with the following approaches:

1. **Utility Classes**: Use Tailwind utility classes directly in component JSX
2. **CSS Variables**: Use CSS variables for theming and consistent values
3. **Class Name Merging**: Use the `cn()` utility to merge class names
4. **Design Tokens**: CSS variables defined in `/styles/web.css` for consistent spacing, sizing, etc.

Example of CSS variable usage:
```tsx
<div className="h-(--avatar-size-md) text-size-sm" />
```

## TypeScript Practices

- Use TypeScript interfaces for component props
- Extend base component props from React's `ComponentProps` type
- Use type narrowing for variants and enums
- Export component prop interfaces when they may be reused
- Default values should be defined in the destructured props

Example:
```tsx
interface ButtonProps extends ComponentProps<'button'> {
  variant?: 'primary' | 'secondary';
  size?: 'sm' | 'md' | 'lg';
  loading?: boolean;
}

export function Button({
  variant = 'primary',
  size = 'md',
  loading = false,
  ...props
}: ButtonProps) {
  // Component implementation
}
```

## Creating Stories

Each component should have a corresponding Storybook story file:

```tsx
import type { Meta, StoryObj } from '@storybook/react';
import { ComponentName } from './component-name';

const meta: Meta<typeof ComponentName> = {
  title: 'Components/Category/ComponentName',
  component: ComponentName,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    // Define argument types and controls
  },
};

export default meta;

type Story = StoryObj<typeof ComponentName>;

export const Default: Story = {
  args: {
    // Default props
  },
};

export const Variant: Story = {
  args: {
    // Variant-specific props
  },
};

// Include a story showing all variants when applicable
export const AllVariants: Story = {
  render: () => (
    <div className="flex gap-4">
      <ComponentName variant="primary" />
      <ComponentName variant="secondary" />
    </div>
  ),
};
```

## Component Extension Pattern

When extending components from UI libraries:

1. Import the base component
2. Create a wrapper component that extends its props
3. Apply project-specific styling and behavior
4. Pass through all remaining props

Example:
```tsx
import { Input } from '@repo/ui/components/input';
import { cn } from '@repo/ui/lib/utils';
import type { ComponentProps } from 'react';

interface BaseInputProps extends ComponentProps<typeof Input> {
  icon?: React.ReactNode;
}

export function BaseInput({ className, icon, ...props }: BaseInputProps) {
  return (
    <div className={cn('relative', className)}>
      <Input className="w-full" {...props} />
      {icon && (
        <div className="absolute right-3 top-1/2 -translate-y-1/2">
          {icon}
        </div>
      )}
    </div>
  );
}
```

## Accessibility Considerations

- Use semantic HTML elements
- Include proper ARIA attributes when needed
- Ensure keyboard navigation works properly
- Maintain sufficient color contrast

## Testing Components

Use Vitest for testing components:

```tsx
import { render, screen } from '@testing-library/react';
import { describe, it, expect } from 'vitest';
import { ComponentName } from './component-name';

describe('ComponentName', () => {
  it('renders correctly', () => {
    render(<ComponentName />);
    // Add assertions
  });
  
  it('applies variant styles', () => {
    render(<ComponentName variant="secondary" />);
    // Add assertions
  });
});
```

## Best Practices

1. Keep components focused on a single responsibility
2. Avoid large, complex components - break them down into smaller parts
3. Use composition over inheritance
4. Utilize default props for common configurations
5. Ensure components are responsive
6. Document complex components with JSDoc comments
7. Use the existing design tokens and variables for consistency
8. Follow the PascalCase naming convention for components
9. Create Storybook stories for all components