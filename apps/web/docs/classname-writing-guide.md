# Tailwind CSS Class Naming Guide

This guide documents our standardized approach to using Tailwind CSS in the PredictGo frontend project. Following these conventions ensures consistent styling throughout the application.

## Cautions !

Always refers to the latest version of styles/web.css

## Table of Contents

1. [Design System Variables](#design-system-variables)
2. [Spacing Utilities](#spacing-utilities)
3. [Color Naming Conventions](#color-naming-conventions)
4. [Typography Classes](#typography-classes)
5. [Layout Patterns](#layout-patterns)
6. [Responsive Design](#responsive-design)
7. [Component-Specific Conventions](#component-specific-conventions)
8. [Custom CSS Variables with Tailwind](#custom-css-variables-with-tailwind)
9. [Best Practices](#best-practices)

## Design System Variables

Our design system variables are defined in `@apps/web/styles/web.css`. These variables are used throughout the application to maintain consistency.

### Key Variable Categories

- **Spacing**: `--space-*` variables for consistent margins, paddings, and gaps
- **Colors**: Semantic color variables like `--color-yes-green`, `--color-no-red`, etc.
- **Typography**: Text size variables like `--text-size-*`
- **Component Dimensions**: Variables for specific component sizes like `--avatar-size-*`, `--button-height-*`

## Spacing Utilities

### Standard Spacing Scale

Use our custom spacing utilities based on the design system:

```css
/* Correct */
<div className="p-space-10 m-space-20 gap-space-15">...</div>

/* Incorrect */
<div className="p-2.5 m-5 gap-4">...</div>
```

### Spacing Variables

| Variable | Value | Pixel Equivalent |
|----------|-------|------------------|
| `--space-5` | `calc(5 / var(--root-font-size) * 1rem)` | 5px |
| `--space-6` | `0.375rem` | 6px |
| `--space-8` | `0.5rem` | 8px |
| `--space-10` | `0.625rem` | 10px |
| `--space-12` | `0.75rem` | 12px |
| `--space-15` | `0.9375rem` | 15px |
| `--space-20` | `1.25rem` | 20px |
| `--space-25` | `calc(25 / var(--root-font-size) * 1rem)` | 25px |
| `--space-30` | `1.875rem` | 30px |
| `--space-40` | `calc(40 / var(--root-font-size) * 1rem)` | 40px |
| `--space-50` | `calc(50 / var(--root-font-size) * 1rem)` | 50px |
| `--space-60` | `calc(60 / var(--root-font-size) * 1rem)` | 60px |

### Usage Examples

```jsx
// Margins
<div className="mt-space-20 mb-space-30 mx-space-10">...</div>

// Paddings
<div className="pt-space-15 pb-space-20 px-space-30">...</div>

// Gaps (for flex and grid)
<div className="gap-space-10 flex">...</div>
<div className="gap-x-space-15 gap-y-space-20 grid">...</div>
```

## Color Naming Conventions

### Semantic Color Variables

Our color system uses semantic naming to ensure consistent usage across the application:

| Variable | Usage |
|----------|-------|
| `--color-yes-green` | Positive actions, confirmations, success states |
| `--color-no-red` | Negative actions, errors, destructive actions |
| `--color-point-3` (sky) | Primary actions, highlights, focus states |
| `--color-gray-1`, `--color-gray-2`, `--color-gray-3` | Various gray shades for backgrounds, borders, and text |
| `--color-mid-dark` | Medium emphasis text |
| `--color-dark-deep` | High emphasis text |
| `--color-line` | Border colors |
| `--color-graph-1` through `--color-graph-11` | Data visualization colors |

### Usage Examples

```jsx
// Text colors
<p className="text-yes-green">Positive message</p>
<p className="text-no-red">Error message</p>
<p className="text-mid-dark">Regular text</p>
<p className="text-gray-3">Secondary text</p>

// Background colors
<div className="bg-yes-green/25">Light green background with opacity</div>
<div className="bg-gray-2">Light gray background</div>
<div className="bg-sky">Blue background</div>

// Border colors
<div className="border border-line">Element with border</div>
```

## Typography Classes

### Text Sizes

Use our custom text size classes based on the design system:

| Class | Variable | Size |
|-------|----------|------|
| `text-size-xxs8` | `--text-size-xxs8` | 8px |
| `text-size-xxs10` | `--text-size-xxs10` | 10px |
| `text-size-xxs` | `--text-size-xxs` | 11px |
| `text-size-xs` | `--text-size-xs` | 12px |
| `text-size-sm13` | `--text-size-sm13` | 13px |
| `text-size-sm` | `--text-size-sm` | 14px |
| `text-size-base15` | `--text-size-base15` | 15px |
| `text-size-base` | `--text-size-base` | 16px |
| `text-size-lg` | `--text-size-lg` | 18px |
| `text-size-xl` | `--text-size-xl` | 20px |
| `text-size-2xl` | `--text-size-2xl` | 24px |
| `text-size-3xl` | `--text-size-3xl` | 30px |
| `text-size-4xl` | `--text-size-4xl` | 36px |
| `text-size-5xl` | `--text-size-5xl` | 48px |
| `text-size-6xl` | `--text-size-6xl` | 64px |

### Font Weights

Use Tailwind's standard font weight utilities:

- `font-normal`: Regular weight (400)
- `font-medium`: Medium weight (500)
- `font-semibold`: Semi-bold weight (600)
- `font-bold`: Bold weight (700)

### Typography Examples

```jsx
// Heading styles
<h1 className="text-size-2xl font-bold text-mid-dark">Main Heading</h1>
<h2 className="text-size-xl font-semibold text-mid-dark">Subheading</h2>

// Body text
<p className="text-size-base text-mid-dark">Regular paragraph text</p>
<p className="text-size-sm text-gray-3">Secondary information</p>

// Pre-defined text styles
<p className="dashboard-h2">Dashboard heading</p>
<p className="dashboard-h3">Dashboard subheading</p>
```

## Layout Patterns

### Flexbox

```jsx
// Basic flex container
<div className="flex">...</div>

// Common flex patterns
<div className="flex items-center justify-between">...</div>
<div className="flex flex-col gap-space-10">...</div>
<div className="flex items-start gap-space-15">...</div>

// With responsive adjustments
<div className="flex flex-col md:flex-row gap-space-20">...</div>
```

### Grid

```jsx
// Basic grid
<div className="grid grid-cols-2 gap-space-20">...</div>

// Responsive grid
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-space-30">...</div>

// Complex grid with specific column spans
<ul className="grid gap-3 p-6 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr]">...</ul>
```

### Container Classes

```jsx
// Page container with max width
<div className="page">...</div>

// Shell container (max width without padding)
<div className="shell">...</div>
```

## Responsive Design

### Breakpoint System

We follow Tailwind's standard breakpoint system:

- `sm`: 640px and above
- `md`: 768px and above
- `lg`: 1024px and above
- `xl`: 1280px and above
- `2xl`: 1536px and above

### Responsive Examples

```jsx
// Responsive text size
<h1 className="text-size-xl md:text-size-2xl lg:text-size-3xl">Responsive Heading</h1>

// Responsive layout
<div className="flex flex-col md:flex-row gap-space-20">...</div>

// Responsive width
<div className="w-full md:w-[500px] lg:w-[600px]">...</div>

// Responsive grid
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-space-20">...</div>
```

## Component-Specific Conventions

### Buttons

```jsx
// Base button with variants
<BaseButton variant="yes">Yes Button</BaseButton>
<BaseButton variant="no">No Button</BaseButton>
<BaseButton variant="info">Primary Button</BaseButton>
<BaseButton variant="neutral">Neutral Button</BaseButton>

// Button sizes
<BaseButton size="sm">Small Button</BaseButton>
<BaseButton size="md">Medium Button</BaseButton>
<BaseButton size="lg">Large Button</BaseButton>
```

### Form Elements

```jsx
// Input
<BaseInput className="w-full" placeholder="Enter text" />

// Input with icon
<BaseInput icon={<SvgIcon name="SearchIcon" />} placeholder="Search" />

// Textarea
<BaseTextarea className="min-h-[120px]" placeholder="Enter description" />
```

### Cards

```jsx
// Dashboard card
<DashboardCard variant="info">Card content</DashboardCard>

// Prediction market card
<Card className="py-space-10 p-space-10 gap-space-10 h-(--market-card-height) max-w-(--market-card-width)">
  Card content
</Card>
```

## Custom CSS Variables with Tailwind

### Using CSS Variables in Class Names

Tailwind CSS v4 allows direct usage of CSS variables in class names with the following syntax:

```jsx
// Height using CSS variable
<div className="h-(--avatar-size-md)">...</div>

// Width using CSS variable
<div className="w-(--market-card-width)">...</div>

// Max width using CSS variable
<div className="max-w-(--page-max-width)">...</div>

// Padding using CSS variable
<div className="p-(--space-10)">...</div>
```

### Common Variable Usage Patterns

```jsx
// Avatar sizes
<Avatar className="size-(--avatar-size-sm) rounded-full">...</Avatar>

// Button heights
<button className="h-(--button-height-md)">...</button>

// Input heights
<input className="h-(--input-height-md)">...</input>

// Container max width
<div className="max-w-(--page-max-width) mx-auto">...</div>
```

## Best Practices

### Class Order

Follow a consistent order for your Tailwind classes to improve readability:

1. Layout (display, position)
2. Sizing (width, height)
3. Spacing (margin, padding)
4. Typography (font, text)
5. Visual (colors, borders, shadows)
6. Interactive (hover, focus)
7. Responsive variants (grouped by breakpoint)

### Example of Ordered Classes

```jsx
// Good: Organized classes
<div className="flex items-center justify-between w-full h-(--input-height-md) px-space-15 py-space-10 text-size-sm text-mid-dark bg-gray-2 border border-line hover:bg-gray-1 md:flex-col">
  Content
</div>

// Bad: Disorganized classes
<div className="text-mid-dark border-line px-space-15 flex w-full hover:bg-gray-1 h-(--input-height-md) md:flex-col border py-space-10 bg-gray-2 text-size-sm items-center justify-between">
  Content
</div>
```

### Extracting Common Patterns

For frequently used class combinations, consider:

1. Creating component props for variations
2. Using the `cn()` utility for conditional classes
3. Defining reusable classes in the `@layer components` section of your CSS

```jsx
// Using cn() utility for conditional classes
import { cn } from '@repo/ui/lib/utils';

function Button({ className, variant = 'default', ...props }) {
  return (
    <button
      className={cn(
        'px-space-15 py-space-10 text-size-sm font-medium',
        variant === 'primary' && 'bg-sky text-white',
        variant === 'secondary' && 'bg-gray-2 text-mid-dark border border-line',
        className
      )}
      {...props}
    />
  );
}
```

By following these guidelines, we can maintain a consistent and maintainable codebase with predictable styling patterns.

## Common Mistakes to Avoid

### Mixing Design Systems

```jsx
// Bad: Mixing our design system with default Tailwind values
<div className="p-4 m-5 text-sm text-gray-600">...</div>

// Good: Using our design system consistently
<div className="p-space-15 m-space-20 text-size-sm text-mid-dark">...</div>
```

### Inconsistent Naming

```jsx
// Bad: Inconsistent naming for the same concept
<div className="mt-5 mb-space-20 ml-4 mr-space-15">...</div>

// Good: Consistent naming
<div className="mt-space-20 mb-space-20 ml-space-15 mr-space-15">...</div>
```

### Hardcoded Values

```jsx
// Bad: Hardcoded pixel values
<div className="w-[394px] h-[216px]">...</div>

// Good: Using design system variables
<div className="w-(--market-card-width) h-(--market-card-height)">...</div>
```

### Excessive Inline Styles

```jsx
// Bad: Using inline styles
<div style={{ width: '394px', height: '216px', padding: '15px' }}>...</div>

// Good: Using Tailwind classes
<div className="w-(--market-card-width) h-(--market-card-height) p-space-15">...</div>
```

## Accessibility Considerations

When applying Tailwind classes, keep accessibility in mind:

### Text Contrast

Ensure text has sufficient contrast against its background:

```jsx
// Good: High contrast
<p className="text-mid-dark bg-white">High contrast text</p>

// Bad: Low contrast
<p className="text-gray-3 bg-gray-2">Low contrast text</p>
```

### Focus States

Always include proper focus states for interactive elements:

```jsx
// Good: Includes focus state
<button className="bg-sky text-white focus:ring-2 focus:ring-sky/50 focus:outline-none">
  Accessible Button
</button>
```

### Screen Reader Support

Use appropriate classes for screen reader support:

```jsx
// Visually hidden but available to screen readers
<span className="sr-only">Additional information for screen readers</span>

// Hide from screen readers
<div aria-hidden="true" className="...">Decorative content</div>
```

## Performance Considerations

### Class Reuse

Reuse common class combinations to reduce CSS bundle size:

```jsx
// Define in @layer components
.card-base {
  @apply p-space-15 bg-gray-2 border border-line;
}

// Use in components
<div className="card-base">...</div>
```

### Conditional Classes

Use the `cn()` utility for conditional classes to keep your JSX clean:

```jsx
import { cn } from '@repo/ui/lib/utils';

<div
  className={cn(
    'base-classes-here',
    isActive && 'active-classes-here',
    variant === 'primary' ? 'primary-classes' : 'secondary-classes',
    className // Allow overrides via props
  )}
>
  ...
</div>
```

## Conclusion

This guide serves as a reference for all developers to ensure consistent styling throughout the application. By adhering to these conventions, we can maintain a cohesive design language, improve code readability, and streamline the development process.

Remember that consistency is key - when in doubt, look at existing components for guidance or consult with the design team to ensure your implementation aligns with our design system.

For any questions or suggestions regarding this guide, please reach out to the frontend team.
