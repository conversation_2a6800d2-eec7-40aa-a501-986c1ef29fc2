# Authentication System

This document describes the authentication system implementation using SIWE (Sign-In with Ethereum) and React Query for state management.

## Overview

The authentication system provides:
- **SIWE-based authentication** using wallet signatures
- **Reactive state management** with React Query
- **Type-safe hooks** for checking authentication status
- **Automatic caching** and background updates
- **Error handling** and loading states

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Components    │───▶│   useAuth Hook   │───▶│  SIWE Config    │
│                 │    │                  │    │                 │
│ - PageNav       │    │ - isLoggedIn     │    │ - getSession    │
│ - Protected     │    │ - userAddress    │    │ - verifyMessage │
│ - Conditional   │    │ - isLoading      │    │ - signOut       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                │                        │
                                ▼                        ▼
                       ┌──────────────────┐    ┌─────────────────┐
                       │  React Query     │    │   AuthApis      │
                       │                  │    │                 │
                       │ - Caching        │    │ - getSession    │
                       │ - Background     │    │ - verify        │
                       │ - Refetching     │    │ - signOut       │
                       └──────────────────┘    └─────────────────┘
                                                        │
                                                        ▼
                                               ┌─────────────────┐
                                               │   Backend API   │
                                               │                 │
                                               │ - /auth/session │
                                               │ - /auth/verify  │
                                               │ - /auth/signout │
                                               └─────────────────┘
```

## Core Components

### 1. SIWE Configuration (`lib/siwe.ts`)

The SIWE configuration handles the core authentication logic:

```typescript
export const siweConfig = createSIWEConfig({
  getSession: async () => {
    const session = await authApis.getSession();
    return session.siwe ? {
      address: session.siwe.address,
      chainId: session.siwe.chainId,
    } : null;
  },
  // ... other SIWE methods
});
```

### 2. Authentication Hook (`hooks/use-auth.ts`)

The main hook for checking authentication status:

```typescript
export function useAuth() {
  const { data: session, isLoading, error } = useQuery({
    queryKey: ['auth', 'session'],
    queryFn: () => siweConfig.getSession(),
    // ... query options
  });

  return {
    isLoggedIn: !!session?.address,
    userAddress: session?.address || null,
    chainId: session?.chainId || null,
    isAuthLoading: isLoading,
    // ... other properties
  };
}
```

### 3. Authentication APIs (`services/apis/auth.apis.ts`)

Backend API integration:

```typescript
export class AuthApis extends BaseApi {
  async getSession() {
    const res = await this.fetcher.get('auth/session').json();
    return AuthSessionSchema.parse(res).result;
  }
  
  async verify(params: { message: string; signature: string }) {
    return this.fetcher.post('auth/verify', { json: params });
  }
  
  async signOut() {
    return this.fetcher.post('auth/signout').json();
  }
}
```

## Usage Patterns

### Basic Authentication Check

```typescript
import { useAuth } from '@/hooks/use-auth';

function MyComponent() {
  const { isLoggedIn, userAddress, isAuthLoading } = useAuth();

  if (isAuthLoading) {
    return <div>Loading...</div>;
  }

  return (
    <div>
      {isLoggedIn ? (
        <p>Welcome, {userAddress}</p>
      ) : (
        <p>Please log in</p>
      )}
    </div>
  );
}
```

### Protected Components

```typescript
import { useRequireAuth } from '@/hooks/use-auth';

function ProtectedComponent() {
  const { userAddress } = useRequireAuth(); // Throws if not authenticated
  
  return <div>Protected content for {userAddress}</div>;
}
```

### Authentication Utilities

```typescript
import { useAuthUtils } from '@/hooks/use-auth';

function AuthManager() {
  const { clearAuthCache, refreshAuth } = useAuthUtils();
  
  const handleLogout = async () => {
    await siweConfig.signOut();
    clearAuthCache(); // Clear React Query cache
  };
  
  return (
    <button onClick={handleLogout}>
      Logout
    </button>
  );
}
```

## API Endpoints

### GET /auth/session

Returns the current user session:

```json
{
  "result": {
    "siwe": {
      "address": "0x1234...5678",
      "chainId": 1
    }
  }
}
```

### POST /auth/verify

Verifies a SIWE signature:

```json
{
  "message": "domain.com wants you to sign in...",
  "signature": "0xabcd..."
}
```

### POST /auth/signout

Clears the user session (returns empty response).

## Error Handling

The authentication system handles various error scenarios:

1. **Network Errors**: Gracefully handled by React Query
2. **Invalid Sessions**: Returns `null` for session data
3. **Authentication Required**: `useRequireAuth` throws descriptive errors
4. **Backend Unavailable**: Shows error states in components

## Caching Strategy

React Query configuration for authentication:

- **Stale Time**: 5 minutes (data considered fresh)
- **GC Time**: 10 minutes (cache retention)
- **Refetch on Focus**: Yes (check auth when user returns)
- **Refetch on Mount**: Yes (always check on component mount)
- **Retry**: Disabled (don't retry auth failures)

## Testing

### Storybook Stories

Authentication components include comprehensive Storybook stories:

- `hooks/use-auth.stories.tsx` - Hook demonstration
- `components/examples/auth-examples.stories.tsx` - Usage examples

### Test Scenarios

1. **Authenticated State**: User is logged in
2. **Unauthenticated State**: User is not logged in  
3. **Loading State**: Authentication check in progress
4. **Error State**: Authentication service unavailable
5. **Cache Management**: Manual refresh and cache clearing

## Best Practices

1. **Always handle loading states** when using `useAuth`
2. **Use `useRequireAuth`** for components that must have authentication
3. **Clear cache after logout** using `clearAuthCache()`
4. **Refresh auth after login** using `refreshAuth()`
5. **Handle errors gracefully** with proper error boundaries
6. **Use TypeScript** for type safety throughout

## Migration Guide

If migrating from hardcoded authentication:

1. Replace `const isLoggedIn = true` with `const { isLoggedIn } = useAuth()`
2. Add loading state handling: `if (isAuthLoading) return <Loading />`
3. Add error state handling: `if (authError) return <Error />`
4. Update protected routes to use `useRequireAuth`
5. Implement proper logout with cache clearing

## Security Considerations

- **Session cookies** are httpOnly and secure
- **SIWE signatures** prevent replay attacks with nonces
- **Address validation** ensures proper Ethereum address format
- **Chain ID validation** prevents cross-chain attacks
- **Error messages** don't leak sensitive information
