'use client';

import React, { useState } from 'react';
// import { useTracking } from '@/hooks/use-tracking';
// import { usePurchaseTracking } from '@/components/tracking/purchase-tracking';
// import { BaseButton } from '@/components/ui/base.button';
// import { Card } from '@repo/ui/components/card';

// 예시용 임시 타입 정의
interface TrackingHook {
  trackPurchase: (data: any) => void;
  trackMarketView: (data: any) => void;
  trackWalletConnect: (data: any) => void;
  trackUserLogin: (data: any) => void;
  identify: (userId: string, properties?: any) => void;
  setUserProperties: (properties: any) => void;
  isTrackingEnabled: boolean;
  consentState: any;
}

interface PurchaseTrackingHook {
  trackPurchaseEvent: (data: any) => void;
  trackMarketViewEvent: (data: any) => void;
}

// 임시 훅 구현 (예시용)
const useTracking = (): TrackingHook => ({
  trackPurchase: () => {},
  trackMarketView: () => {},
  trackWalletConnect: () => {},
  trackUserLogin: () => {},
  identify: () => {},
  setUserProperties: () => {},
  isTrackingEnabled: false,
  consentState: null,
});

const usePurchaseTracking = (): PurchaseTrackingHook => ({
  trackPurchaseEvent: () => {},
  trackMarketViewEvent: () => {},
});

// 임시 컴포넌트들
const BaseButton: React.FC<any> = ({ children, ...props }) => (
  <button {...props}>{children}</button>
);

const Card: React.FC<any> = ({ children, ...props }) => <div {...props}>{children}</div>;

export function TrackingUsageExample() {
  const [userId, setUserId] = useState('user-example-123');
  const [marketId] = useState('market-example-456');

  // 메인 트래킹 훅 사용
  const {
    trackPurchase,
    trackMarketView,
    trackWalletConnect,
    trackUserLogin,
    identify,
    setUserProperties,
    isTrackingEnabled,
    consentState,
  } = useTracking();

  // 구매 트래킹 전용 훅 사용
  const { trackPurchaseEvent, trackMarketViewEvent } = usePurchaseTracking();

  // 예시: 구매 이벤트 트래킹
  const handlePurchaseClick = () => {
    trackPurchase({
      marketId,
      outcomeId: 'outcome-yes',
      amount: '100',
      currency: 'USDC',
      price: '0.75',
      userId,
    });

    alert('구매 이벤트가 트래킹되었습니다!');
  };

  // 예시: 마켓 조회 이벤트 트래킹
  const handleMarketViewClick = () => {
    trackMarketView({
      marketId,
      marketTitle: 'Bitcoin will reach $100k by 2024',
      category: 'Cryptocurrency',
      userId,
    });

    alert('마켓 조회 이벤트가 트래킹되었습니다!');
  };

  // 예시: 월렛 연결 이벤트 트래킹
  const handleWalletConnectClick = () => {
    trackWalletConnect({
      walletType: 'MetaMask',
      userId,
    });

    alert('월렛 연결 이벤트가 트래킹되었습니다!');
  };

  // 예시: 사용자 로그인 이벤트 트래킹
  const handleUserLoginClick = () => {
    trackUserLogin({
      method: 'wallet',
      userId,
    });

    alert('사용자 로그인 이벤트가 트래킹되었습니다!');
  };

  // 예시: 사용자 식별
  const handleIdentifyClick = () => {
    identify(userId, {
      email: '<EMAIL>',
      plan: 'premium',
      registrationDate: new Date().toISOString(),
    });

    alert('사용자가 식별되었습니다!');
  };

  // 예시: 사용자 속성 설정
  const handleSetUserPropertiesClick = () => {
    setUserProperties({
      favoriteCategory: 'Cryptocurrency',
      totalPurchases: 5,
      lastActive: new Date().toISOString(),
    });

    alert('사용자 속성이 설정되었습니다!');
  };

  // 전용 훅을 사용한 트래킹
  const handlePurchaseTrackingHookClick = () => {
    trackPurchaseEvent({
      marketId,
      outcomeId: 'outcome-no',
      amount: '50',
      currency: 'USDC',
      price: '0.25',
      userId,
    });

    alert('전용 훅으로 구매 이벤트가 트래킹되었습니다!');
  };

  const handleMarketViewTrackingHookClick = () => {
    trackMarketViewEvent({
      marketId,
      marketTitle: 'Ethereum will reach $5k by 2024',
      category: 'Cryptocurrency',
      userId,
    });

    alert('전용 훅으로 마켓 조회 이벤트가 트래킹되었습니다!');
  };

  return (
    <div className="mx-auto max-w-4xl space-y-6 p-6">
      <h1 className="mb-8 text-center text-3xl font-bold">트래킹 시스템 사용 예시</h1>

      {/* 상태 정보 */}
      <Card className="p-6">
        <h2 className="mb-4 text-xl font-semibold">트래킹 상태</h2>
        <div className="space-y-2">
          <p>
            <strong>트래킹 활성화:</strong>{' '}
            <span className={isTrackingEnabled ? 'text-green-600' : 'text-red-600'}>
              {isTrackingEnabled ? '활성화됨' : '비활성화됨'}
            </span>
          </p>
          <p>
            <strong>Google Analytics 동의:</strong>{' '}
            <span className={consentState?.googleAnalytics ? 'text-green-600' : 'text-red-600'}>
              {consentState?.googleAnalytics ? '동의함' : '동의하지 않음'}
            </span>
          </p>
          <p>
            <strong>Microsoft Clarity 동의:</strong>{' '}
            <span className={consentState?.microsoftClarity ? 'text-green-600' : 'text-red-600'}>
              {consentState?.microsoftClarity ? '동의함' : '동의하지 않음'}
            </span>
          </p>
          <p>
            <strong>사용자 ID:</strong> {userId}
          </p>
          <p>
            <strong>마켓 ID:</strong> {marketId}
          </p>
        </div>
      </Card>

      {/* 기본 트래킹 이벤트들 */}
      <Card className="p-6">
        <h2 className="mb-4 text-xl font-semibold">기본 트래킹 이벤트</h2>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <BaseButton
            onClick={handlePurchaseClick}
            variant="info"
            size="lg"
            disabled={!isTrackingEnabled}
          >
            구매 이벤트 트래킹
          </BaseButton>

          <BaseButton
            onClick={handleMarketViewClick}
            variant="info"
            size="lg"
            disabled={!isTrackingEnabled}
          >
            마켓 조회 이벤트 트래킹
          </BaseButton>

          <BaseButton
            onClick={handleWalletConnectClick}
            variant="info"
            size="lg"
            disabled={!isTrackingEnabled}
          >
            월렛 연결 이벤트 트래킹
          </BaseButton>

          <BaseButton
            onClick={handleUserLoginClick}
            variant="info"
            size="lg"
            disabled={!isTrackingEnabled}
          >
            사용자 로그인 이벤트 트래킹
          </BaseButton>
        </div>
      </Card>

      {/* 사용자 관리 */}
      <Card className="p-6">
        <h2 className="mb-4 text-xl font-semibold">사용자 관리</h2>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <BaseButton
            onClick={handleIdentifyClick}
            variant="dark"
            size="lg"
            disabled={!isTrackingEnabled}
          >
            사용자 식별
          </BaseButton>

          <BaseButton
            onClick={handleSetUserPropertiesClick}
            variant="dark"
            size="lg"
            disabled={!isTrackingEnabled}
          >
            사용자 속성 설정
          </BaseButton>
        </div>
      </Card>

      {/* 전용 훅 사용 */}
      <Card className="p-6">
        <h2 className="mb-4 text-xl font-semibold">전용 훅 사용</h2>
        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <BaseButton
            onClick={handlePurchaseTrackingHookClick}
            variant="success"
            size="lg"
            disabled={!isTrackingEnabled}
          >
            전용 훅 - 구매 트래킹
          </BaseButton>

          <BaseButton
            onClick={handleMarketViewTrackingHookClick}
            variant="success"
            size="lg"
            disabled={!isTrackingEnabled}
          >
            전용 훅 - 마켓 조회 트래킹
          </BaseButton>
        </div>
      </Card>

      {/* 사용자 ID 변경 */}
      <Card className="p-6">
        <h2 className="mb-4 text-xl font-semibold">설정</h2>
        <div className="flex items-center gap-4">
          <label htmlFor="userId" className="font-medium">
            사용자 ID:
          </label>
          <input
            id="userId"
            type="text"
            value={userId}
            onChange={e => setUserId(e.target.value)}
            className="flex-1 rounded-md border border-gray-300 px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:outline-none"
            placeholder="사용자 ID를 입력하세요"
          />
        </div>
      </Card>

      {/* 주의사항 */}
      {!isTrackingEnabled && (
        <Card className="border-yellow-200 bg-yellow-50 p-6">
          <h2 className="mb-2 text-xl font-semibold text-yellow-800">주의사항</h2>
          <p className="text-yellow-700">
            트래킹이 비활성화되어 있습니다. 하단의 쿠키 동의 배너에서 동의를 해주세요.
          </p>
        </Card>
      )}
    </div>
  );
}
