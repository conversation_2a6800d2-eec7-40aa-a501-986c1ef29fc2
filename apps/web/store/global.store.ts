import type { SIWESession } from '@reown/appkit-siwe';
import type { SafeSmartAccount } from '@/lib/types';
import { create } from 'zustand';
import { combine } from 'zustand/middleware';

export const useGlobalStore = create(
  combine(
    {
      isSignedIn: false,
      session: null as SIWESession | null,
      safeSmartAccount: undefined as SafeSmartAccount | undefined,
      isAsideOpen: true,
    },
    (set, get) => ({
      setSession: async (session: SIWESession) => {
        set({ session, isSignedIn: !!session && !!get().safeSmartAccount });
      },
      clearSession: () => {
        set({ session: null, safeSmartAccount: undefined, isSignedIn: false });
      },
      setSafeSmartAccount: (safeSmartAccount: SafeSmartAccount | undefined) => {
        set({
          safeSmartAccount,
          isSignedIn: !!get().session && !!safeSmartAccount,
        });
      },
      getSession: () => {
        return get().session;
      },
      toggleAside: () => {
        set({ isAsideOpen: !get().isAsideOpen });
      },
      setAsideOpen: (isOpen: boolean) => {
        set({ isAsideOpen: isOpen });
      },
    })
  )
);

export const globalStore = useGlobalStore.getState();
