declare namespace NodeJS {
  interface ProcessEnv {
    // Public variables
    NEXT_PUBLIC_API_URL: string;
    // NEXT_PUBLIC_APP_NAME: string;
    NEXT_PUBLIC_GA_TRACKING_ID: string;
    NEXT_PUBLIC_APP_KIT_PROJECT_ID: string;
    NEXT_PUBLIC_ENV: string;
    NEXT_PUBLIC_USDC_ADDRESS: string;
    NEXT_PUBLIC_PREDICTGO_ADDRESS: string;
    NEXT_PUBLIC_GA_MEASUREMENT_ID?: string;
    NEXT_PUBLIC_CLARITY_PROJECT_ID?: string;

    // Private variables
    DATABASE_URL: string;
    JWT_SECRET: string;
  }
}
