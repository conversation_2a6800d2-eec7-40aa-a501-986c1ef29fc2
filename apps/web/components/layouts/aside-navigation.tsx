'use client';

import SvgIcon from '@/components/icons/svg-icon';
import { cn } from '@repo/ui/lib/utils';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import React from 'react';
import { useGlobalStore } from '@/store/global.store';
import { INNER_LINKS } from '@/lib/constants';

interface NavigationItem {
  label: string;
  href: string;
  icon: React.ReactNode;
  subItems?: {
    label: string;
    href: string;
  }[];
}

const NavigationItem: React.FC<{ item: NavigationItem; pathname: string }> = ({
  item,
  pathname,
}) => {
  const hasSubItems = !!item.subItems?.length;
  const isActive = pathname === item.href || pathname?.startsWith(`${item.href}/`);
  const isSubItemActive = item.subItems?.some(
    subItem => pathname === subItem.href || pathname?.startsWith(`${subItem.href}/`)
  );

  return (
    <div className="flex flex-col">
      <Link
        href={item.href}
        className={cn(
          'gap-space-10 px-space-15 py-space-12 text-size-sm flex items-center transition-colors',
          isActive || isSubItemActive
            ? 'bg-gray-1 text-dark font-semibold'
            : 'text-dark hover:bg-gray-1 font-medium'
        )}
      >
        <div className="flex-shrink-0">{item.icon}</div>
        <span className="text-size-sm">{item.label}</span>
      </Link>

      {hasSubItems && (
        <div className="flex flex-col">
          {item.subItems?.map(subItem => {
            const isSubItemActive =
              pathname === subItem.href || pathname?.startsWith(`${subItem.href}/`);
            return (
              <Link
                key={subItem.href}
                href={subItem.href}
                className={cn(
                  'px-space-15 py-space-10 hover:text-dark ml-8 transition-colors',
                  isSubItemActive ? 'text-dark font-semibold' : 'text-gray-3'
                )}
              >
                <span className="text-size-sm">{subItem.label}</span>
              </Link>
            );
          })}
        </div>
      )}
    </div>
  );
};

export const AsideNavigation: React.FC = () => {
  const pathname = usePathname();
  const { safeSmartAccount } = useGlobalStore();

  // Generate navigation items dynamically
  const NAVIGATION_ITEMS: NavigationItem[] = [
    {
      label: 'Positions',
      href: INNER_LINKS.PROFILE.POSITIONS,
      icon: <SvgIcon className="size-[18px]" name="PositionsIcon" />,
    },
    {
      label: 'Settings',
      href: INNER_LINKS.PROFILE.SETTINGS.PROFILE,
      icon: <SvgIcon className="size-[18px]" name="SettingsIcon" />,
      subItems: [
        { label: 'Profile Setting', href: INNER_LINKS.PROFILE.SETTINGS.PROFILE },
        { label: 'Export Private Key', href: INNER_LINKS.PROFILE.SETTINGS.EXPORT },
      ],
    },
    {
      label: 'Channel',
      href: INNER_LINKS.PROFILE.CHANNELS.SETTING,
      icon: <SvgIcon className="size-[18px]" name="ChannelIcon" />,
      subItems: [
        { label: 'Channel Setting', href: INNER_LINKS.PROFILE.CHANNELS.SETTING },
        { label: 'Predictions', href: INNER_LINKS.PROFILE.CHANNELS.PREDICTIONS },
        { label: 'Deposits', href: INNER_LINKS.PROFILE.CHANNELS.DEPOSITS },
        { label: 'Rewards', href: INNER_LINKS.PROFILE.CHANNELS.REWARDS },
      ],
    },
    {
      label: 'Referral',
      href: INNER_LINKS.PROFILE.REFERRAL,
      icon: <SvgIcon className="size-[18px]" name="ReferralIcon" />,
    },
    {
      label: 'Share Bonus',
      href: INNER_LINKS.PROFILE.SHARE_BONUS,
      icon: <SvgIcon className="size-[18px]" name="ShareBonusIcon" />,
    },
    {
      label: 'Subscription',
      href: INNER_LINKS.PROFILE.SUBSCRIPTIONS,
      icon: <SvgIcon className="size-[18px]" name="SubscriptionIcon" />,
    },
    {
      label: 'Support',
      href: INNER_LINKS.PROFILE.SUPPORT,
      icon: <SvgIcon className="size-[18px]" name="SupportIcon" />,
    },
    {
      label: 'Logout',
      href: '/logout',
      icon: <SvgIcon className="size-[18px]" name="LogoutIcon" />,
    },
  ];

  return (
    <div className="py-space-20 flex flex-col">
      <div className="flex flex-col">
        {NAVIGATION_ITEMS.map(item => (
          <NavigationItem key={item.href} item={item} pathname={pathname} />
        ))}
      </div>
    </div>
  );
};
