import React from 'react';
import PageNav from '../common/navigation/page-nav';
import { Footer } from './footer';
import { pxToRem } from '@repo/ui/lib/utils';
import { DebugEnvFloatingPanel } from '../common/debug-tool';

interface MainLayoutProps {
  children: React.ReactNode;
}

export function MainLayout({ children }: MainLayoutProps) {
  return (
    <div className="flex flex-col">
      <PageNav />
      <div
        style={{
          minHeight: `calc(100vh - ${pxToRem(109)})`,
        }}
        className="flex flex-1"
      >
        <main className="flex-1 overflow-y-auto">{children}</main>
      </div>
      <Footer />
      <DebugEnvFloatingPanel />
    </div>
  );
}
