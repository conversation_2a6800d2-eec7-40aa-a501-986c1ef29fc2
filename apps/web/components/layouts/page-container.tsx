import { cn } from '@repo/ui/lib/utils';

export default function PageContainer({
  children,
  title,
  style,
  className,
}: {
  children: React.ReactNode;
  title: string;
  style?: React.CSSProperties;
  className?: string;
}) {
  return (
    <div
      data-page={title}
      className={cn('py-space-30 flex h-full flex-col', className)}
      style={style}
    >
      <header className="mb-space-30">
        <h1 className="text-mid-dark text-size-lg font-bold">{title}</h1>
      </header>
      <div className="flex-1">{children}</div>
    </div>
  );
}
