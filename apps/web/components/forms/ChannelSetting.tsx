'use client';

import { useReducer, useCallback } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { BaseInput } from '@/components/ui/base.input';
import { BaseTextarea } from '@/components/ui/base.textarea';
import { BaseButton, DarkButton } from '@/components/ui/base.button';
import { pxToRem } from '@repo/ui/lib/utils';
import SvgIcon from '@/components/icons/svg-icon';
import { UpdateChannelRequestBody } from '@/lib/api/channel/channel.schema.server';
import { toast } from '@/components/ui/base.toast';
import { DEFAULT_MARKET_AVATAR_URL } from '@/lib/constants';
import { useUpdateChannel } from '@/hooks/query/channel';

// SNS 타입 정의
const SNS_TYPES = [
  'youtube',
  'twitter',
  'telegram',
  'facebook',
  'discord',
  'tiktok',
  'instagram',
  'abstract',
] as const;

// Form schema based on UpdateChannelReqDto
const channelFormSchema = z.object({
  name: z
    .string()
    .max(20, 'Channel name must be at most 20 characters.')
    .regex(/^[a-zA-Z0-9\s]+$/, 'Channel name must contain only letters, numbers, and spaces.')
    .optional(),
  description: z.string().max(255, 'Description must be at most 255 characters.').optional(),
  channelSns: z
    .array(
      z.object({
        snsType: z.enum(SNS_TYPES),
        snsUrl: z.union([z.string(), z.literal('')]),
      })
    )
    .optional(),
});

type ChannelFormValues = z.infer<typeof channelFormSchema>;

// 상태 타입 정의
interface FileState {
  bannerFile: File | null;
  bannerPreview: string;
  avatarFile: File | null;
  avatarPreview: string;
}

type FileAction =
  | { type: 'SET_BANNER'; file: File; preview: string }
  | { type: 'SET_AVATAR'; file: File; preview: string };

// 순수 함수들
const createInitialFileState = (initialData?: ChannelSettingProps['initialData']): FileState => ({
  bannerFile: null,
  bannerPreview: initialData?.bannerUrl || '',
  avatarFile: null,
  avatarPreview: initialData?.imageUrl || DEFAULT_MARKET_AVATAR_URL,
});

const fileReducer = (state: FileState, action: FileAction): FileState => {
  switch (action.type) {
    case 'SET_BANNER':
      return { ...state, bannerFile: action.file, bannerPreview: action.preview };
    case 'SET_AVATAR':
      return { ...state, avatarFile: action.file, avatarPreview: action.preview };
    default:
      return state;
  }
};

const validateImageFile = (file: File, maxSizeMB: number): string | null => {
  if (!file.type.startsWith('image/')) {
    return 'Please select an image file';
  }
  if (file.size > maxSizeMB * 1024 * 1024) {
    return `File size must be less than ${maxSizeMB}MB`;
  }
  return null;
};

const readFileAsDataURL = (file: File): Promise<string> =>
  new Promise((resolve, reject) => {
    const reader = new FileReader();
    reader.onload = e => resolve(e.target?.result as string);
    reader.onerror = reject;
    reader.readAsDataURL(file);
  });

const createInitialChannelSns = (initialData?: ChannelSettingProps['initialData']) =>
  SNS_TYPES.map(snsType => {
    const existingSns = initialData?.channelSns?.find(sns => sns.snsType === snsType);
    return {
      snsType,
      snsUrl: existingSns?.snsUrl || '',
    };
  });

const filterAndSortChannelSns = (channelSns: Array<{ snsType: string; snsUrl: string }>) =>
  channelSns
    .filter(sns => sns.snsUrl.trim() !== '')
    .map(sns => ({ snsType: sns.snsType, snsUrl: sns.snsUrl }))
    .sort((a, b) => a.snsType.localeCompare(b.snsType));

const hasFormChanges = (
  watchedValues: ChannelFormValues,
  fileState: FileState,
  initialData?: ChannelSettingProps['initialData']
): boolean => {
  // 이름 변경 확인
  if (watchedValues.name && watchedValues.name !== initialData?.name) {
    return true;
  }

  // 설명 변경 확인
  if (
    watchedValues.description !== undefined &&
    watchedValues.description !== initialData?.description
  ) {
    return true;
  }

  // 파일 업로드 확인
  if (fileState.avatarFile || fileState.bannerFile) {
    return true;
  }

  // SNS URL 변경 확인
  if (watchedValues.channelSns) {
    const currentChannelSns = filterAndSortChannelSns(watchedValues.channelSns);
    const initialChannelSns = filterAndSortChannelSns(initialData?.channelSns || []);

    return JSON.stringify(initialChannelSns) !== JSON.stringify(currentChannelSns);
  }

  return false;
};

const createUpdateData = (
  values: ChannelFormValues,
  fileState: FileState,
  initialData?: ChannelSettingProps['initialData']
): UpdateChannelRequestBody => {
  const updateData: UpdateChannelRequestBody = {};

  // 변경된 필드만 포함
  if (values.name && values.name !== initialData?.name) {
    updateData.name = values.name;
  }

  if (values.description !== undefined && values.description !== initialData?.description) {
    updateData.description = values.description;
  }

  if (fileState.avatarFile) {
    updateData.image = fileState.avatarFile;
  }

  if (fileState.bannerFile) {
    updateData.banner = fileState.bannerFile;
  }

  // SNS 링크 변경 확인
  if (values.channelSns) {
    const currentChannelSns = filterAndSortChannelSns(values.channelSns);
    const initialChannelSns = filterAndSortChannelSns(initialData?.channelSns || []);

    if (JSON.stringify(initialChannelSns) !== JSON.stringify(currentChannelSns)) {
      updateData.channelSns = currentChannelSns.map(sns => ({
        snsType: sns.snsType as any,
        snsUrl: sns.snsUrl,
      }));
    }
  }

  return updateData;
};

const PLACEHOLDER_TEXT = {
  channelName: 'Enter channel name (a-z, A-Z, 0-9, max 20 chars)',
  description:
    'Please write about your channel. Describe what your channel will focus on, your content, and what users can expect.',
};

const SOCIAL_MEDIA = {
  youtube: {
    key: 'youtube',
    icon: <SvgIcon name="YoutubeIcon" />,
    placeholder: 'Enter your YouTube channel URL',
  },
  twitter: {
    key: 'twitter',
    icon: <SvgIcon name="XIcon" />,
    placeholder: 'Enter your X (Twitter) URL',
  },
  facebook: {
    key: 'facebook',
    icon: <SvgIcon name="FacebookIcon" />,
    placeholder: 'Enter your Facebook page URL',
  },
  telegram: {
    key: 'telegram',
    icon: <SvgIcon name="TelegramIcon" />,
    placeholder: 'Enter your Telegram URL',
  },
  discord: {
    key: 'discord',
    icon: <SvgIcon name="DiscordIcon" />,
    placeholder: 'Enter your Discord URL',
  },
  tiktok: {
    key: 'tiktok',
    icon: <SvgIcon name="TiktokIcon" />,
    placeholder: 'Enter your TikTok URL',
  },
  instagram: {
    key: 'instagram',
    icon: <SvgIcon name="InstagramIcon" />,
    placeholder: 'Enter your Instagram URL',
  },
  abstract: {
    key: 'abstract',
    icon: <SvgIcon name="AbstractIcon" />,
    placeholder: 'Enter your Abstract URL',
  },
};

interface ChannelSettingProps {
  initialData?: {
    name?: string;
    description?: string;
    imageUrl?: string;
    bannerUrl?: string;
    channelSns?: Array<{
      snsType: string;
      snsUrl: string;
    }>;
  };
}

export default function ChannelSetting({ initialData }: ChannelSettingProps) {
  const [fileState, dispatch] = useReducer(fileReducer, createInitialFileState(initialData));
  const updateChannelMutation = useUpdateChannel();

  const form = useForm<ChannelFormValues>({
    resolver: zodResolver(channelFormSchema),
    defaultValues: {
      name: initialData?.name || '',
      description: initialData?.description || '',
      channelSns: createInitialChannelSns(initialData),
    },
  });

  const watchedValues = form.watch();

  const handleFileUpload = useCallback((accept: string, onFileSelect: (file: File) => void) => {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = accept;
    input.onchange = e => {
      const file = (e.target as HTMLInputElement).files?.[0];
      if (file) {
        onFileSelect(file);
      }
    };
    input.click();
  }, []);

  const handleBannerFileSelect = useCallback(async (file: File) => {
    const error = validateImageFile(file, 5);
    if (error) {
      toast.error(error);
      return;
    }

    try {
      const preview = await readFileAsDataURL(file);
      dispatch({ type: 'SET_BANNER', file, preview });
    } catch {
      toast.error('Failed to read file');
    }
  }, []);

  const handleAvatarFileSelect = useCallback(async (file: File) => {
    const error = validateImageFile(file, 1);
    if (error) {
      toast.error(error);
      return;
    }

    try {
      const preview = await readFileAsDataURL(file);
      dispatch({ type: 'SET_AVATAR', file, preview });
    } catch {
      toast.error('Failed to read file');
    }
  }, []);

  const handleBannerUpload = useCallback(() => {
    handleFileUpload('image/jpeg,image/png', handleBannerFileSelect);
  }, [handleFileUpload, handleBannerFileSelect]);

  const handleAvatarUpload = useCallback(() => {
    handleFileUpload('image/jpeg,image/png', handleAvatarFileSelect);
  }, [handleFileUpload, handleAvatarFileSelect]);

  const onSubmit = useCallback(
    async (values: ChannelFormValues) => {
      try {
        const updateData = createUpdateData(values, fileState, initialData);

        // Only call API if there are changes
        if (Object.keys(updateData).length > 0) {
          await updateChannelMutation.mutateAsync(updateData);
          toast.success('Channel updated successfully!');
        } else {
          console.log('No changes to save');
        }
      } catch (error) {
        console.error('Failed to update channel:', error);
      }
    },
    [fileState, initialData, updateChannelMutation]
  );

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="gap-space-30 flex flex-col">
        {/* Error Display */}
        {updateChannelMutation.error && (
          <div className="rounded-md border border-red-200 bg-red-50 p-4">
            <div className="flex">
              <div className="ml-3">
                <h3 className="text-sm font-medium text-red-800">Channel update failed</h3>
                <div className="mt-2 text-sm text-red-700">
                  {updateChannelMutation.error.message}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Banner Upload Section */}
        <section className="gap-space-20 flex flex-col">
          <p className="text-size-sm text-gray-3">
            Set a representative image for your channel. Applying it service-wide may take up to
            five minutes.
          </p>
          <div
            data-role="upload-banner-image"
            className="bg-gray-2 border-line relative h-(--banner-image-height) w-full overflow-hidden rounded-lg border"
          >
            <div
              className="h-full w-full"
              style={{
                // backgroundSize: 'cover',
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center',
                backgroundImage: `url('${fileState.bannerPreview}')`,
              }}
            ></div>
          </div>
          <div className="flex justify-end">
            <div className="gap-space-20 flex items-center">
              <p className="text-size-xs text-gray-3 text-center">
                At least <b className="text-mid-dark">1258px X 237px</b> recommended.
                <b className="text-mid-dark">JPG or PNG</b> is allowed. File size up to{' '}
                <b className="text-mid-dark">5MB</b>.
              </p>
              <BaseButton
                type="button"
                variant="info"
                aria-label="Upload banner image"
                onClick={handleBannerUpload}
              >
                <SvgIcon name="ImageFillIcon" />
                Upload new photo
              </BaseButton>
            </div>
          </div>
        </section>

        {/* Avatar Upload Section */}
        <section>
          <div data-role="upload-icon-image" className="gap-space-30 flex items-center">
            <button
              type="button"
              data-role="upload-image"
              className="relative size-[80px]"
              onClick={handleAvatarUpload}
            >
              <div
                className="bg-gray-2 border-line h-full w-full rounded-full border"
                style={{
                  backgroundSize: 'cover',
                  backgroundPosition: 'center',
                  backgroundImage: `url('${fileState.avatarPreview}')`,
                }}
              ></div>
              <div
                style={{
                  right: '0px',
                  bottom: '0px',
                }}
                className="bg-sky absolute flex size-[24px] items-center justify-center rounded-full"
              >
                <svg
                  width="10"
                  height="10"
                  viewBox="0 0 10 10"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M3.86229 9.5V0.5H6.13771V9.5H3.86229ZM0.5 6.13771V3.86229H9.5V6.13771H0.5Z"
                    fill="white"
                  />
                </svg>
              </div>
            </button>
            <div className="text-sm text-gray-500">
              <div>
                At least <b>80px X 80px</b> recommended.
                <br />
                <b>JPG or PNG</b> is allowed. File size up to <b>1MB</b>.
              </div>
            </div>
          </div>
        </section>

        {/* SNS Links Section */}
        <section>
          <div className="gap-space-10 flex flex-col">
            {SNS_TYPES.map((snsType, index) => {
              const snsInfo = SOCIAL_MEDIA[snsType];
              return (
                <FormField
                  key={snsType}
                  control={form.control}
                  name={`channelSns.${index}.snsUrl`}
                  render={({ field }) => (
                    <FormItem>
                      <div className="gap-space-15 flex items-center">
                        <div>{snsInfo.icon}</div>
                        <FormControl>
                          <BaseInput placeholder={snsInfo.placeholder} {...field} />
                        </FormControl>
                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              );
            })}
          </div>
        </section>

        {/* Channel Info Section */}
        <section className="gap-space-30 flex flex-col">
          <header className="text-size-base text-mid-dark font-bold">Channel Info</header>
          <div className="gap-space-30 flex flex-col md:flex-row">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem data-role="write-channel-name" className="space-y-space-15 flex-1">
                  <FormLabel className="text-size-sm text-mid-dark block font-semibold">
                    Channel Name
                  </FormLabel>
                  <FormControl>
                    <BaseInput placeholder={PLACEHOLDER_TEXT.channelName} {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem data-role="write-channel-description" className="space-y-space-15">
                <FormLabel className="text-size-sm text-mid-dark block font-semibold">
                  About Channel
                </FormLabel>
                <FormControl>
                  <BaseTextarea
                    style={{ height: pxToRem(224) }}
                    placeholder={PLACEHOLDER_TEXT.description}
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </section>

        <DarkButton
          type="submit"
          size="lg"
          fontSize="base"
          disabled={
            updateChannelMutation.isPending ||
            !hasFormChanges(watchedValues, fileState, initialData)
          }
          style={{
            width: pxToRem(148),
          }}
        >
          {updateChannelMutation.isPending ? 'Saving...' : 'Save changes'}
        </DarkButton>
      </form>
    </Form>
  );
}
