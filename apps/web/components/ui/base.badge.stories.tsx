import type { <PERSON>a, StoryObj } from '@storybook/react';
import { BaseBadge } from './base.badge';

const meta: Meta<typeof BaseBadge> = {
  title: 'Components/ui/BaseBadge',
  component: BaseBadge,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      control: { type: 'select' },
      options: [
        'yes',
        'no',
        'info',
        'dark',
        'outline',
        'warning',
        'graph1',
        'graph2',
        'graph3',
        'graph4',
        'graph5',
        'graph6',
        'graph7',
        'graph8',
        'graph9',
        'graph10',
        'graph11',
      ],
    },
    size: {
      control: { type: 'select' },
      options: ['sm'],
    },
  },
};

export default meta;

type Story = StoryObj<typeof BaseBadge>;

export const Default: Story = {
  render: args => <BaseBadge {...args} />,
  args: {
    children: 'Badge',
  },
};

export const Variants: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <BaseBadge variant="yes">Yes</BaseBadge>
      <BaseBadge variant="no">No</BaseBadge>
      <BaseBadge variant="info">Info</BaseBadge>
      <BaseBadge variant="dark">Dark</BaseBadge>
      <BaseBadge variant="outline">Outline</BaseBadge>
      <BaseBadge variant="warning">Warning</BaseBadge>
    </div>
  ),
};

export const GraphColorVariants: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <BaseBadge variant="graph1">Graph 1</BaseBadge>
      <BaseBadge variant="graph2">Graph 2</BaseBadge>
      <BaseBadge variant="graph3">Graph 3</BaseBadge>
      <BaseBadge variant="graph4">Graph 4</BaseBadge>
      <BaseBadge variant="graph5">Graph 5</BaseBadge>
      <BaseBadge variant="graph6">Graph 6</BaseBadge>
      <BaseBadge variant="graph7">Graph 7</BaseBadge>
      <BaseBadge variant="graph8">Graph 8</BaseBadge>
      <BaseBadge variant="graph9">Graph 9</BaseBadge>
      <BaseBadge variant="graph10">Graph 10</BaseBadge>
      <BaseBadge variant="graph11">Graph 11</BaseBadge>
    </div>
  ),
};

export const Sizes: Story = {
  render: () => (
    <div className="flex flex-wrap items-end gap-4">
      <BaseBadge size="sm" variant="info">
        SM
      </BaseBadge>
    </div>
  ),
};

export const WithIcons: Story = {
  render: () => (
    <div className="flex flex-wrap gap-4">
      <BaseBadge variant="yes" className="flex items-center gap-1">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="12"
          height="12"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M20 6 9 17l-5-5" />
        </svg>
        승인됨
      </BaseBadge>
      <BaseBadge variant="no" className="flex items-center gap-1">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="12"
          height="12"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M18 6 6 18" />
          <path d="m6 6 12 12" />
        </svg>
        거부됨
      </BaseBadge>
      <BaseBadge variant="info" className="flex items-center gap-1">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="12"
          height="12"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <circle cx="12" cy="12" r="10" />
          <path d="M12 16v-4" />
          <path d="M12 8h.01" />
        </svg>
        정보
      </BaseBadge>
    </div>
  ),
};
