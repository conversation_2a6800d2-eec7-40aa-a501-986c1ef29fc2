import CommonAvatar from './avatar-image';

export default function AvatarImageExample() {
  return (
    <div className="gap-space-20 p-space-20 flex flex-col">
      <h2 className="text-xl font-bold">Avatar Image Examples</h2>

      <div className="gap-space-10 flex items-center">
        <div className="gap-space-5 flex flex-col items-center">
          <CommonAvatar size="sm" imageUrl="https://i.pravatar.cc/150?img=1" alt="Small avatar" />
          <span className="text-size-xs">sm (24px)</span>
        </div>

        <div className="gap-space-5 flex flex-col items-center">
          <CommonAvatar size="md" imageUrl="https://i.pravatar.cc/150?img=2" alt="Medium avatar" />
          <span className="text-size-xs">md (40px)</span>
        </div>

        <div className="gap-space-5 flex flex-col items-center">
          <CommonAvatar
            size="md2"
            imageUrl="https://i.pravatar.cc/150?img=3"
            alt="Medium 2 avatar"
          />
          <span className="text-size-xs">md2 (48px)</span>
        </div>

        <div className="gap-space-5 flex flex-col items-center">
          <CommonAvatar size="lg" imageUrl="https://i.pravatar.cc/150?img=4" alt="Large avatar" />
          <span className="text-size-xs">lg (80px)</span>
        </div>

        <div className="gap-space-5 flex flex-col items-center">
          <CommonAvatar size="md" alt="Default image example" />
          <span className="text-size-xs">기본 이미지</span>
        </div>
      </div>

      <div className="mt-space-20">
        <h3 className="mb-space-10 text-lg font-semibold">사용 예시:</h3>
        <pre className="bg-gray-2 p-space-10 text-size-xs overflow-x-auto rounded-md">
          {`
// 기본 사용법
<UserAvatar 
  imageUrl="https://example.com/avatar.jpg" 
  size="md" 
  alt="User avatar" 
/>

// 기본 이미지 사용 (imageUrl 생략)
<UserAvatar size="sm" alt="Default avatar" />

// 추가 클래스 적용
<UserAvatar 
  imageUrl="https://example.com/avatar.jpg" 
  size="lg" 
  className="border-2 border-yes" 
/>
          `.trim()}
        </pre>
      </div>
    </div>
  );
}
