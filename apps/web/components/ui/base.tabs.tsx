import * as React from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger, <PERSON><PERSON><PERSON>ontent } from '@repo/ui/components/tabs';
import { cn } from '@repo/ui/lib/utils';

function BaseTabs({ className, ...props }: React.ComponentProps<typeof Tabs>) {
  return <Tabs className={cn('w-full', className)} {...props} />;
}

function BaseTabsList({ className, ...props }: React.ComponentProps<typeof TabsList>) {
  return (
    <TabsList
      className={cn('gap-space-10 border-line rounded-none border-b bg-transparent p-0', className)}
      {...props}
    />
  );
}

function BaseTabsTrigger({ className, ...props }: React.ComponentProps<typeof TabsTrigger>) {
  return (
    <TabsTrigger
      className={cn(
        'text-size-base py-space-10 text-gray-3 relative rounded-none px-0',
        'data-[state=active]:text-mid-dark data-[state=active]:mb-[-1px] data-[state=active]:border-0 data-[state=active]:border-b-2 data-[state=active]:border-b-sky-500 data-[state=active]:shadow-none',
        'min-w-(--tab-trigger-width)',
        className
      )}
      {...props}
    />
  );
}

function BaseTabsContent({ className, ...props }: React.ComponentProps<typeof TabsContent>) {
  return (
    <TabsContent className={cn('mt-space-20 focus-visible:outline-none', className)} {...props} />
  );
}

export { BaseTabs, BaseTabsList, BaseTabsTrigger, BaseTabsContent };
