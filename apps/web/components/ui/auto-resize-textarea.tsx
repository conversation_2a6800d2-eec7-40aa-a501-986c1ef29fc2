import React, { useRef, useEffect, ChangeEvent } from 'react';
import { Textarea } from '@repo/ui/components/textarea';
import { cn } from '@repo/ui/lib/utils';
import type { ComponentProps } from 'react';

interface AutoResizeTextareaProps extends ComponentProps<typeof Textarea> {
  minHeight?: string;
}

export function AutoResizeTextarea({
  className,
  minHeight = '40px',
  onChange,
  ...props
}: AutoResizeTextareaProps) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // 텍스트 내용에 따라 높이 자동 조절 함수
  const adjustHeight = () => {
    const textarea = textareaRef.current;
    if (!textarea) return;

    // 높이를 초기화하고 스크롤 높이에 맞게 조정
    textarea.style.height = minHeight;
    textarea.style.height = `${textarea.scrollHeight}px`;
  };

  // 컴포넌트 마운트 시 높이 조절
  useEffect(() => {
    adjustHeight();
  }, []);

  // 값이 변경될 때 높이 조절
  const handleChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    if (onChange) {
      onChange(e);
    }

    setTimeout(adjustHeight, 0);
  };
  return (
    <Textarea
      ref={textareaRef}
      className={cn(
        'placeholder:text-icon-gray py-space-12 text-size-xs px-space-12 flex max-w-full resize-none items-center overflow-hidden rounded-none border-none bg-transparent shadow-none outline-none focus:ring-0 focus:outline-none focus-visible:ring-0',
        className
      )}
      style={{ minHeight }}
      onChange={handleChange}
      {...props}
    />
  );
}
