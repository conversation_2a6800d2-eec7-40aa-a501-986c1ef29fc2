import type { Meta, StoryObj } from '@storybook/react';
import PredictionLabel from './prediction-label';

const meta: Meta<typeof PredictionLabel> = {
  title: 'Components/ui/PredictionLabel',
  component: PredictionLabel,
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof PredictionLabel>;

export const Default: Story = {
  args: {
    label: 'PREDICTION',
    colorName: 'yes',
  },
};

export const NoPrediction: Story = {
  args: {
    label: 'NO',
    colorName: 'no',
  },
};

export const CustomColor: Story = {
  args: {
    label: 'CUSTOM',
    color: '#8dc016',
  },
};

export const WithGraphColor: Story = {
  args: {
    label: 'GRAPH 3',
    colorName: 'graph-3',
  },
};

export const WithCustomClass: Story = {
  args: {
    label: 'CUSTOM CLASS',
    colorName: 'graph-7',
    className: 'uppercase tracking-wider',
  },
};

export const AllColors: Story = {
  render: () => (
    <div className="flex flex-wrap gap-2">
      <PredictionLabel label="YES" colorName="yes" />
      <PredictionLabel label="NO" colorName="no" />
      {[...Array(11)].map((_, i) => (
        <PredictionLabel
          key={`graph-${i + 1}`}
          label={`GRAPH ${i + 1}`}
          colorName={`graph-${i + 1}`}
        />
      ))}
    </div>
  ),
};
