'use client';

import React from 'react';
import { cn } from '@repo/ui/lib/utils';
import SvgIcon from '../icons/svg-icon';
import Image from 'next/image';
import { useBuildInfo } from '@/hooks/use-build-info';
import { EXTERNAL_LINKS } from '@/lib/constants';

const SOCIALS = {
  x: {
    icon: <SvgIcon name="XFooterIcon" />,
    href: 'https://twitter.com/your_handle',
  },
  medium: {
    icon: <SvgIcon name="MediumIcon" />,
    href: 'https://medium.com/your_blog',
  },
  discord: {
    icon: <SvgIcon name="DiscordFooterIcon" />,
    href: 'https://discord.gg/your_server',
  },
  telegram: {
    icon: <SvgIcon name="TelegramFooterIcon" />,
    href: 'https://github.com/your_repo',
  },
};

export function PageFooter() {
  const { buildInfo } = useBuildInfo();

  return (
    <footer
      className={cn(
        'bg-gray-2 border-line px-space-30 py-space-20 relative z-[50] h-(--footer-height) w-full border-t'
      )}
    >
      <div className="space-y-space-30 h-full">
        <div className="flex items-center justify-between">
          <div className="gap-space-15 flex items-center">
            {Object.entries(SOCIALS).map(([key, value]) => (
              <a key={key} href={value.href}>
                {value.icon}
              </a>
            ))}
          </div>

          {/* Upload icon on the right */}
          <div className="">
            <svg
              width="23"
              height="23"
              viewBox="0 0 23 23"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect x="0" y="0" width="23" height="23" rx="3.5" fill="white" stroke="#E3E3E3" />
              <path
                d="M1895.22 29.6207H1897.01V34.2247C1897.01 34.7193 1897.46 35.124 1898.01 35.124C1898.56 35.124 1899.01 34.7193 1899.01 34.2247V29.6207H1900.8C1901.25 29.6207 1901.47 29.1351 1901.15 28.8563L1898.36 26.3472C1898.16 26.1673 1897.85 26.1673 1897.65 26.3472L1894.86 28.8563C1894.55 29.1351 1894.77 29.6207 1895.22 29.6207ZM1893 24.0233C1893 24.518 1893.45 24.9227 1894 24.9227H1902C1902.55 24.9227 1903 24.518 1903 24.0233C1903 23.5287 1902.55 23.124 1902 23.124H1894C1893.45 23.124 1893 23.5287 1893 24.0233Z"
                fill="#8E8E93"
                transform="translate(-1886 -17.5)"
              />
            </svg>
          </div>
        </div>
        <div className="text-size-xxs text-gray-3 gap-space-15 flex items-center font-semibold">
          <div className="after:bg-gray-3 relative after:absolute after:top-[15%] after:right-[-7.5px] after:h-[70%] after:w-[1px] after:content-['']">
            <a href={EXTERNAL_LINKS.PRIVACY_POLICY} target="_blank">
              Privacy
            </a>
          </div>
          <div className="after:bg-gray-3 relative after:absolute after:top-[15%] after:right-[-7.5px] after:h-[70%] after:w-[1px] after:content-['']">
            <a href={EXTERNAL_LINKS.TERMS_OF_USE} target="_blank">
              Terms of Use
            </a>
          </div>
          <div>
            <a href={EXTERNAL_LINKS.DOCUMENTATION} target="_blank">
              Docs
            </a>
          </div>
        </div>

        {/* Footer links */}
        <div className="flex justify-between">
          <div className="text-size-xxs text-gray-3 font-semibold">
            <p>Copyright © 2025 NOVAFLOW LABS LTD. ALL RIGHTS RESERVED</p>
            {buildInfo && (
              <p className="text-size-xxxs text-gray-4 mt-1 opacity-70">
                Build: {buildInfo.buildDate} | v{buildInfo.version}
                {buildInfo.environment !== 'production' && ` | ${buildInfo.environment}`}
              </p>
            )}
          </div>
          <div>
            <Image
              width={300}
              height={23}
              src="/assets/images/footer-logos.svg"
              alt="Footer logos"
            />
          </div>
        </div>
      </div>
    </footer>
  );
}

export default PageFooter;
