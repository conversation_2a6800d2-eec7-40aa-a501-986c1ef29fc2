'use client';

import { useBanner } from '@/hooks/query/banner/use-banner';

interface MainPredictionBannerProps {
  imageUrl?: string;
}

export default function MainPredictionBanner({ imageUrl }: MainPredictionBannerProps) {
  const { data: bannerData, isLoading, error } = useBanner();

  if (isLoading) {
    return (
      <div className="h-(--main-banner-height) w-full animate-pulse bg-gray-200">
        <div className="px-space-50 flex h-full flex-col justify-center">
          <div className="mb-2 h-4 w-16 rounded bg-gray-300"></div>
          <div className="mb-2 h-12 w-3/4 rounded bg-gray-300"></div>
          <div className="h-4 w-24 rounded bg-gray-300"></div>
        </div>
      </div>
    );
  }

  if (error || !bannerData) {
    console.error('Failed to load banner data:', error);
    return null;
  }

  const finalImageUrl = imageUrl || bannerData.imageUrl;
  const bannerStyle = finalImageUrl
    ? {
        backgroundImage: `url(${finalImageUrl})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat',
      }
    : {};

  return (
    <div className="h-(--main-banner-height) w-full" style={bannerStyle}>
      <div className="px-space-50 flex h-full flex-col justify-center text-white">
        <div className="text-size-base font-semibold">{bannerData.status}</div>
        <div className="text-size-6xl font-bold">{bannerData.title}</div>
        <div className="text-size-base font-semibold">{bannerData.postCount}</div>
      </div>
    </div>
  );
}
