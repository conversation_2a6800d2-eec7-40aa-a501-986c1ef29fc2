import { cn } from '@repo/ui/lib/utils';
import { ReactNode } from 'react';

type DashboardCardProps = {
  variant?: 'info' | 'neutral';
  children: ReactNode;
  className?: string;
};

export function DashboardCard({ variant = 'neutral', children, className }: DashboardCardProps) {
  const baseStyles =
    'px-[25px] h-(--dashboard-card-height) flex flex-col justify-between py-space-30';

  const variantStyles = {
    info: 'bg-sky text-white',
    neutral: 'bg-gray-2',
  };

  return <div className={cn(baseStyles, variantStyles[variant], className)}>{children}</div>;
}
