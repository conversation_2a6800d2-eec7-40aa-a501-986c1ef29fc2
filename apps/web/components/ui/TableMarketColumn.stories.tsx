import type { Meta, StoryObj } from '@storybook/react';
import TableMarketColumn from './TableMarketColumn';

const meta: Meta<typeof TableMarketColumn> = {
  title: 'Components/ui/TableMarketColumn',
  component: TableMarketColumn,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof TableMarketColumn>;

export const Default: Story = {
  args: {
    avatarUrl: 'https://i1.sndcdn.com/artworks-gkg5jXzpEreGBoXu-cljIJA-t500x500.jpg',
    prediction: {
      label: 'YES',
      // colorName: "yes",
    },
    volume: '24.5K',
    title: 'Will the European Central Bank cut interest rates before the Federal Reserve in 2024?',
  },
};

export const NoVolume: Story = {
  args: {
    ...Default.args,
    volume: undefined,
  },
};

export const DifferentPrediction: Story = {
  args: {
    title: 'Will the European Central Bank cut interest rates before the Federal Reserve in 2024?',
    avatarUrl: 'https://i1.sndcdn.com/artworks-gkg5jXzpEreGBoXu-cljIJA-t500x500.jpg',
    prediction: {
      label: 'NO',
      // color: "#EF4444", // red-500
    },
    volume: '15.2K',
  },
};

export const LongMarketName: Story = {
  args: {
    ...Default.args,
    title: 'Will the European Central Bank cut interest rates before the Federal Reserve in 2024?',
  },
  render: args => (
    <div className="max-w-[400px]">
      <TableMarketColumn {...args} />
    </div>
  ),
};
