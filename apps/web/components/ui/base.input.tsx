import { cn } from '@repo/ui/lib/utils';
import type { ComponentPropsWithRef } from 'react';

interface BaseInputProps extends ComponentPropsWithRef<'input'> {}

export function BaseInput({ className, ref, ...props }: BaseInputProps) {
  return (
    <input
      ref={ref}
      className={cn(
        'border-line bg-gray-2 placeholder:text-icon-gray text-size-xs px-space-15 flex h-(--input-height-md) w-full items-center rounded-none border py-0 transition-all duration-200',
        className
      )}
      {...props}
    />
  );
}
