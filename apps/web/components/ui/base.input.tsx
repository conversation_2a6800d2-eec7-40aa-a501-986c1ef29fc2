import { Input } from '@repo/ui/components/input';
import { cn } from '@repo/ui/lib/utils';
import type { ComponentProps } from 'react';

interface BaseInputProps extends ComponentProps<typeof Input> {
  icon?: React.ReactNode;
  inputClassName?: string;
}

export function BaseInput({ className, icon, inputClassName, ...props }: BaseInputProps) {
  if (icon) {
    return (
      <div
        className={cn(
          'pr-space-10 flex items-center gap-2 rounded-md ring-0 outline-0 focus-within:ring-0 focus-within:outline-0',
          className
        )}
      >
        <Input
          className={cn(
            'h-full w-full rounded-none border-none shadow-none ring-0 outline-0 focus-within:ring-0 focus-within:outline-0',
            inputClassName
          )}
          {...props}
        />
        {icon}
      </div>
    );
  }
  return (
    <Input
      className={cn(
        'border-line bg-gray-2 placeholder:text-icon-gray text-size-xs px-space-15 focus:ring-opacity-50 flex h-(--input-height-md) items-center rounded-none py-0 transition-all duration-200 focus:border-gray-300 focus:ring-1 focus:ring-gray-300 focus:outline-none focus:ring-inset',
        className,
        inputClassName
      )}
      {...props}
    />
  );
}
