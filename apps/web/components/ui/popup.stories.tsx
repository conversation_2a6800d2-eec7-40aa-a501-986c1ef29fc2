import { But<PERSON> } from '@repo/ui/components/button';
import type { Meta, StoryObj } from '@storybook/react';
import * as React from 'react';
import { Popup } from './popup';
import ChooseUsernamePopup from './popup/choose-username-popup';
import ChooseEmailPopup from './popup/choose-email-popup';
import FundUsdcPopup from './popup/fund-usdc-popup';
import EnableWalletPopup from './popup/enable-wallet';
import DepositUsdcPopup from './popup/deposit-usdc-popup';
import { usePopupStore } from './popup/popup.state';

const meta: Meta<typeof Popup> = {
  title: 'Components/ui/Popup',
  component: Popup,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof Popup>;

// 기본 팝업 예제
export const Default: Story = {
  render: () => {
    const { openPopup } = usePopupStore();
    return (
      <>
        <Button
          onClick={() => {
            openPopup(<ChooseEmailPopup />);
          }}
        >
          팝업 열기
        </Button>
      </>
    );
  },
};

export const ChooseUsername: Story = {
  render: args => {
    const [isOpen, setIsOpen] = React.useState(false);

    return (
      <>
        <Button onClick={() => setIsOpen(true)}>팝업 열기</Button>
        <Popup {...args} isOpen={isOpen} onClose={() => setIsOpen(false)} />
      </>
    );
  },
  args: {
    children: (
      <ChooseUsernamePopup
        onClose={() => console.log('Close clicked')}
        onContinue={() => console.log('Continue clicked')}
      />
    ),
    showCloseButton: true,
  },
};

export const ChooseEmail: Story = {
  render: args => {
    const [isOpen, setIsOpen] = React.useState(false);

    return (
      <>
        <Button onClick={() => setIsOpen(true)}>팝업 열기</Button>
        <Popup {...args} isOpen={isOpen} onClose={() => setIsOpen(false)} />
      </>
    );
  },
  args: {
    children: <ChooseEmailPopup />,
    showCloseButton: true,
  },
};

export const FundUsdc: Story = {
  render: args => {
    const [isOpen, setIsOpen] = React.useState(false);

    return (
      <>
        <Button onClick={() => setIsOpen(true)}>팝업 열기</Button>
        <Popup {...args} isOpen={isOpen} onClose={() => setIsOpen(false)} />
      </>
    );
  },
  args: {
    children: <FundUsdcPopup />,
    showCloseButton: true,
  },
};

export const EnableWallet: Story = {
  render: args => {
    const [isOpen, setIsOpen] = React.useState(false);

    return (
      <>
        <Button onClick={() => setIsOpen(true)}>팝업 열기</Button>
        <Popup {...args} isOpen={isOpen} onClose={() => setIsOpen(false)} />
      </>
    );
  },
  args: {
    children: <EnableWalletPopup />,
    showCloseButton: true,
  },
};

export const DepositUsdc: Story = {
  render: args => {
    const [isOpen, setIsOpen] = React.useState(false);

    return (
      <>
        <Button onClick={() => setIsOpen(true)}>Deposit USDC 팝업 열기</Button>
        <Popup {...args} isOpen={isOpen} onClose={() => setIsOpen(false)} />
      </>
    );
  },
  args: {
    children: (
      <DepositUsdcPopup
        onContinue={() => console.log('Continue clicked')}
        depositAddress="******************************************"
      />
    ),
    showCloseButton: true,
  },
};

export const Destructive: Story = {
  render: args => {
    const [isOpen, setIsOpen] = React.useState(false);

    return (
      <>
        <Button onClick={() => setIsOpen(true)}>팝업 열기</Button>
        <Popup {...args} isOpen={isOpen} onClose={() => setIsOpen(false)} />
      </>
    );
  },
  args: {
    children: <p className="text-center">팝업 내용입니다.</p>,
    showCloseButton: true,
  },
};

// 기본 팝업 예제
export const WithMultipleButtons: Story = {
  render: args => {
    const [isOpen, setIsOpen] = React.useState(false);

    return (
      <>
        <Button onClick={() => setIsOpen(true)}>팝업 열기</Button>
        <Popup {...args} isOpen={isOpen} onClose={() => setIsOpen(false)} />
      </>
    );
  },
  args: {
    children: <p className="text-center">취소 또는 확인을 선택하세요.</p>,
    showCloseButton: true,
  },
};

// 닫기 버튼이 없는 팝업 예제
export const WithoutCloseButton: Story = {
  render: args => {
    const [isOpen, setIsOpen] = React.useState(false);

    return (
      <>
        <Button onClick={() => setIsOpen(true)}>닫기 버튼 없는 팝업 열기</Button>
        <Popup {...args} isOpen={isOpen} onClose={() => setIsOpen(false)} />
      </>
    );
  },
  args: {
    children: <p className="text-center">이 팝업에는 상단 닫기 버튼이 없습니다.</p>,
    showCloseButton: false,
  },
};

// 복잡한 내용이 있는 팝업 예제
export const WithComplexContent: Story = {
  render: args => {
    const [isOpen, setIsOpen] = React.useState(false);

    return (
      <>
        <Button onClick={() => setIsOpen(true)}>복잡한 내용이 있는 팝업 열기</Button>
        <Popup {...args} isOpen={isOpen} onClose={() => setIsOpen(false)} />
      </>
    );
  },
  args: {
    children: (
      <div className="flex w-full flex-col gap-4">
        <div className="w-full rounded-md border p-4">
          <h3 className="mb-2 font-medium">정보 섹션</h3>
          <p className="text-sm text-gray-500">여기에 중요한 정보를 표시할 수 있습니다.</p>
        </div>
        <div className="flex items-center gap-2">
          <input type="checkbox" id="terms" />
          <label htmlFor="terms" className="text-sm">
            이용약관에 동의합니다
          </label>
        </div>
      </div>
    ),
    showCloseButton: true,
  },
};

// 기본 팝업 예제
export const WithPopupButtons: Story = {
  render: args => {
    const [isOpen, setIsOpen] = React.useState(false);

    return (
      <>
        <Button onClick={() => setIsOpen(true)}>팝업 열기</Button>
        <Popup {...args} isOpen={isOpen} onClose={() => setIsOpen(false)} />
      </>
    );
  },
  args: {
    children: <p className="text-center">기본 팝업입니다.</p>,
    showCloseButton: true,
  },
};
