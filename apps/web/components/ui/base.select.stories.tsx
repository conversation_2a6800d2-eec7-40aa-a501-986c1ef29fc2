import * as React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import {
  BaseSelect,
  BaseSelectContent,
  BaseSelectGroup,
  BaseSelectItem,
  BaseSelectLabel,
  BaseSelectSeparator,
  BaseSelectTrigger,
  BaseSelectValue,
} from './base.select';

const meta: Meta<typeof BaseSelect> = {
  title: 'Components/ui/BaseSelect',
  component: BaseSelect,
  tags: ['autodocs'],
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    // You can define argTypes for props here if needed
  },
};

export default meta;

type Story = StoryObj<typeof BaseSelect>;

const selectItems = (
  <>
    <BaseSelectGroup>
      <BaseSelectLabel>Fruits</BaseSelectLabel>
      <BaseSelectItem value="apple">Apple</BaseSelectItem>
      <BaseSelectItem value="banana">Banana</BaseSelectItem>
      <BaseSelectItem value="blueberry">Blueberry</BaseSelectItem>
      <BaseSelectItem value="grapes">Grapes</BaseSelectItem>
      <BaseSelectItem value="pineapple">Pineapple</BaseSelectItem>
    </BaseSelectGroup>
    <BaseSelectSeparator />
    <BaseSelectGroup>
      <BaseSelectLabel>Vegetables</BaseSelectLabel>
      <BaseSelectItem value="aubergine">Aubergine</BaseSelectItem>
      <BaseSelectItem value="broccoli">Broccoli</BaseSelectItem>
      <BaseSelectItem value="carrot" disabled>
        Carrot (disabled)
      </BaseSelectItem>
      <BaseSelectItem value="courgette">Courgette</BaseSelectItem>
      <BaseSelectItem value="leek">Leek</BaseSelectItem>
    </BaseSelectGroup>
  </>
);

export const Default: Story = {
  render: args => (
    <BaseSelect {...args}>
      <BaseSelectTrigger className="w-[280px]">
        <BaseSelectValue placeholder="Select a fruit or vegetable" />
      </BaseSelectTrigger>
      <BaseSelectContent>{selectItems}</BaseSelectContent>
    </BaseSelect>
  ),
};

export const SmallTrigger: Story = {
  render: args => (
    <BaseSelect {...args}>
      <BaseSelectTrigger size="sm" className="w-[240px]">
        <BaseSelectValue placeholder="Select (small trigger)" />
      </BaseSelectTrigger>
      <BaseSelectContent>{selectItems}</BaseSelectContent>
    </BaseSelect>
  ),
};

export const Disabled: Story = {
  render: args => (
    <BaseSelect {...args} disabled>
      <BaseSelectTrigger className="w-[280px]">
        <BaseSelectValue placeholder="Select (disabled)" />
      </BaseSelectTrigger>
      <BaseSelectContent>{selectItems}</BaseSelectContent>
    </BaseSelect>
  ),
};

export const WithDefaultValue: Story = {
  render: args => (
    <BaseSelect {...args} defaultValue="banana">
      <BaseSelectTrigger className="w-[280px]">
        <BaseSelectValue placeholder="Select a fruit" />
      </BaseSelectTrigger>
      <BaseSelectContent>{selectItems}</BaseSelectContent>
    </BaseSelect>
  ),
};
