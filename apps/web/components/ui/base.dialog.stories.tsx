import type { Meta, StoryObj } from '@storybook/react';
import {
  BaseDialog,
  BaseDialogTrigger,
  BaseDialogContent,
  BaseDialogHeader,
  BaseDialogTitle,
  BaseDialogDescription,
  BaseDialogBody,
  BaseDialogFooter,
  BaseDialogClose,
} from './base.dialog';
import { Button } from '@repo/ui/components/button';
import { XlIcon } from '@/components/icons/xl-icon'; // Assuming XlIcon is accessible or adjust path
import { PopButton } from './pop.btn';

const meta: Meta<typeof BaseDialog> = {
  title: 'Components/ui/BaseDialog',
  component: BaseDialog,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof BaseDialog>;

export const Default: Story = {
  render: args => (
    <BaseDialog {...args}>
      <BaseDialogTrigger asChild>
        <Button variant="outline">Open Dialog</Button>
      </BaseDialogTrigger>
      <BaseDialogContent className="sm:max-w-[425px]">
        <BaseDialogClose className="hidden" />
        <BaseDialogBody>
          <XlIcon name="wallet" alt="Wallet Icon" />
          <BaseDialogTitle className="text-center">Enable Trading</BaseDialogTitle>
          <BaseDialogDescription className="text-center">
            Let's set up your wallet to trade on PredictGo
          </BaseDialogDescription>
        </BaseDialogBody>
        <BaseDialogFooter>
          <BaseDialogClose asChild>
            <PopButton variant="red">Confirm</PopButton>
          </BaseDialogClose>
          <BaseDialogClose asChild>
            <PopButton>Cancel</PopButton>
          </BaseDialogClose>
        </BaseDialogFooter>
      </BaseDialogContent>
    </BaseDialog>
  ),
  args: {},
};

export const WithSmallCloseButton: Story = {
  render: args => (
    <BaseDialog {...args}>
      <BaseDialogTrigger asChild>
        <Button variant="outline">Open Dialog</Button>
      </BaseDialogTrigger>
      <BaseDialogContent className="sm:max-w-[425px]" smallCloseBtn>
        <BaseDialogBody>
          <XlIcon name="wallet" alt="Wallet Icon" />
          <BaseDialogTitle className="text-center">Enable Trading</BaseDialogTitle>
          <BaseDialogDescription className="text-center">
            Let's set up your wallet to trade on PredictGo
          </BaseDialogDescription>
        </BaseDialogBody>
        <BaseDialogFooter>
          <BaseDialogClose asChild>
            <PopButton variant="red">Confirm</PopButton>
          </BaseDialogClose>
          <BaseDialogClose asChild>
            <PopButton>Cancel</PopButton>
          </BaseDialogClose>
        </BaseDialogFooter>
      </BaseDialogContent>
    </BaseDialog>
  ),
  args: {},
};
