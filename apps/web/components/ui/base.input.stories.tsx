import type { <PERSON>a, StoryObj } from '@storybook/react';
import { BaseInput } from './base.input'; // Assuming the component is exported as BaseInput

const meta: Meta<typeof BaseInput> = {
  title: 'Components/ui/BaseInput',
  component: BaseInput,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    // Define argTypes for props like type, placeholder, disabled, etc.
    // Example:
    // type: {
    //   control: { type: 'select' },
    //   options: ['text', 'password', 'email', 'number'],
    // },
    // placeholder: { control: 'text' },
    // disabled: { control: 'boolean' },
  },
};

export default meta;

type Story = StoryObj<typeof BaseInput>;

export const Default: Story = {
  render: args => <BaseInput {...args} />,
  args: {
    placeholder: 'Default Input',
  },
};

export const Disabled: Story = {
  render: args => <BaseInput {...args} />,
  args: {
    placeholder: 'Disabled Input',
    disabled: true,
  },
};

// Add more stories for different states and props as needed
// For example, a story for a specific type like 'email'
// export const EmailInput: Story = {
//   render: (args) => <BaseInput {...args} />,
//   args: {
//     type: 'email',
//     placeholder: 'Enter your email',
//   },
// };
