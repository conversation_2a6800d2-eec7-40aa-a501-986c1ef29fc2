import { cn, cva } from '@repo/ui/lib/utils';
import { HTMLAttributes } from 'react';

const customVariant = {
  yes: 'bg-yes-green/20 text-yes-green border border-yes/20',
  no: 'bg-no-red/20 text-no-red border border-no/20',
  info: 'bg-sky/20 text-sky border border-sky/20',
  dark: 'bg-dark/20 text-dark border border-dark/20',
  outline: 'border border-border text-foreground',
  warning: 'bg-warning/20 text-warning border border-warning/20',
  graph1: 'bg-graph-1/20 text-graph-1 border border-graph-1/20',
  graph2: 'bg-graph-2/20 text-graph-2 border border-graph-2/20',
  graph3: 'bg-graph-3/20 text-graph-3 border border-graph-3/20',
  graph4: 'bg-graph-4/20 text-graph-4 border border-graph-4/20',
  graph5: 'bg-graph-5/20 text-graph-5 border border-graph-5/20',
  graph6: 'bg-graph-6/20 text-graph-6 border border-graph-6/20',
  graph7: 'bg-graph-7/20 text-graph-7 border border-graph-7/20',
  graph8: 'bg-graph-8/20 text-graph-8 border border-graph-8/20',
  graph9: 'bg-graph-9/20 text-graph-9 border border-graph-9/20',
  graph10: 'bg-graph-10/20 text-graph-10 border border-graph-10/20',
  graph11: 'bg-graph-11/20 text-graph-11 border border-graph-11/20',
};

type CustomVariant = keyof typeof customVariant;

const customSize = {
  sm: 'h-[15px] text-[0.625rem] px-space-8',
};

type CustomSize = keyof typeof customSize;

type BaseBadgeProps = HTMLAttributes<HTMLDivElement> & {
  variant?: CustomVariant;
  size?: CustomSize;
};

const badgeVariants = cva('inline-flex items-center justify-center rounded-full font-medium', {
  variants: {
    size: customSize,
    variant: customVariant,
  },
  defaultVariants: {
    size: 'sm',
    variant: 'yes',
  },
});

export function BaseBadge({ className, variant, size, ...props }: BaseBadgeProps) {
  return <div className={cn(badgeVariants({ variant, size, className }))} {...props} />;
}
