import CommonAvatar from './avatar-image';
import PredictionLabel from './prediction-label';

interface TableMarketColumnProps {
  avatarUrl: string;
  outcome: {
    label: string;
    order: number;
  };
  volume?: string;
  title: string;
}

export default function TableMarketColumn({
  avatarUrl,
  outcome,
  volume,
  title,
}: TableMarketColumnProps) {
  return (
    <div className="gap-space-20 flex items-center">
      <CommonAvatar size="md2" imageUrl={avatarUrl} />
      <div className="flex flex-col">
        <div className="text-mid-dark text-size-sm font-semibold">{title}</div>
        <div className="gap-space-10 flex items-center">
          {outcome && <PredictionLabel label={outcome.label} colorName="yes" />}
          {volume && (
            <div className="text-size-xs gap-space-6 flex items-center">
              <span className="text-mid-dark font-semibold">{volume}</span>
              <span>Vol.</span>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
