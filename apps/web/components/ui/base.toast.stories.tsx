import React from 'react';
import type { Meta, StoryObj } from '@storybook/react';
import { BaseToast, toast } from './base.toast';
import { Button } from '@repo/ui/components/button';

const meta: Meta<typeof BaseToast> = {
  title: 'Components/ui/BaseToast',
  component: BaseToast,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    theme: {
      options: ['system', 'light', 'dark'],
      control: { type: 'select' },
    },
    position: {
      options: [
        'top-left',
        'top-center',
        'top-right',
        'bottom-left',
        'bottom-center',
        'bottom-right',
      ],
      control: { type: 'select' },
    },
    closeButton: {
      control: { type: 'boolean' },
    },
    richColors: {
      control: { type: 'boolean' },
    },
    expand: {
      control: { type: 'boolean' },
    },
  },
};

export default meta;

type Story = StoryObj<typeof BaseToast>;

export const Default: Story = {
  render: args => (
    <div className="flex flex-col items-center gap-4">
      <BaseToast {...args} />
      <Button
        onClick={() => {
          toast('Default toast notification');
        }}
      >
        Show Default Toast
      </Button>
    </div>
  ),
  args: {
    position: 'bottom-right',
    theme: 'system',
    closeButton: true,
  },
};

export const SuccessToast: Story = {
  render: args => (
    <div className="flex flex-col items-center gap-4">
      <BaseToast {...args} />
      <Button
        onClick={() => {
          toast.success('Operation completed successfully!');
        }}
      >
        Show Success Toast
      </Button>
    </div>
  ),
  args: {
    position: 'bottom-right',
    theme: 'system',
    closeButton: true,
    richColors: true,
  },
};

export const ErrorToast: Story = {
  render: args => (
    <div className="flex flex-col items-center gap-4">
      <BaseToast {...args} />
      <Button
        onClick={() => {
          toast.error('An error occurred. Please try again.');
        }}
      >
        Show Error Toast
      </Button>
    </div>
  ),
  args: {
    position: 'bottom-right',
    theme: 'system',
    closeButton: true,
    richColors: true,
  },
};

export const InfoToast: Story = {
  render: args => (
    <div className="flex flex-col items-center gap-4">
      <BaseToast {...args} />
      <Button
        onClick={() => {
          toast.info("Here's some useful information for you!");
        }}
      >
        Show Info Toast
      </Button>
    </div>
  ),
  args: {
    position: 'bottom-right',
    theme: 'system',
    closeButton: true,
    richColors: true,
  },
};

export const WarningToast: Story = {
  render: args => (
    <div className="flex flex-col items-center gap-4">
      <BaseToast {...args} />
      <Button
        onClick={() => {
          toast.warning('Warning: This action may not be reversible');
        }}
      >
        Show Warning Toast
      </Button>
    </div>
  ),
  args: {
    position: 'bottom-right',
    theme: 'system',
    closeButton: true,
    richColors: true,
  },
};

export const CustomDuration: Story = {
  render: args => (
    <div className="flex flex-col items-center gap-4">
      <BaseToast {...args} />
      <Button
        onClick={() => {
          toast('This toast will disappear in 10 seconds', {
            duration: 10000,
          });
        }}
      >
        Show Long Duration Toast (10s)
      </Button>
    </div>
  ),
  args: {
    position: 'bottom-right',
    theme: 'system',
    closeButton: true,
  },
};

export const CustomPosition: Story = {
  render: args => (
    <div className="flex flex-col items-center gap-4">
      <BaseToast {...args} />
      <div className="flex flex-wrap justify-center gap-2">
        <Button
          onClick={() => {
            toast('Top left toast', { position: 'top-left' });
          }}
        >
          Top Left
        </Button>
        <Button
          onClick={() => {
            toast('Top center toast', { position: 'top-center' });
          }}
        >
          Top Center
        </Button>
        <Button
          onClick={() => {
            toast('Top right toast', { position: 'top-right' });
          }}
        >
          Top Right
        </Button>
        <Button
          onClick={() => {
            toast('Bottom left toast', { position: 'bottom-left' });
          }}
        >
          Bottom Left
        </Button>
        <Button
          onClick={() => {
            toast('Bottom center toast', { position: 'bottom-center' });
          }}
        >
          Bottom Center
        </Button>
        <Button
          onClick={() => {
            toast('Bottom right toast', { position: 'bottom-right' });
          }}
        >
          Bottom Right
        </Button>
      </div>
    </div>
  ),
  args: {
    theme: 'system',
    closeButton: true,
  },
};

export const PromiseToast: Story = {
  render: args => (
    <div className="flex flex-col items-center gap-4">
      <BaseToast {...args} />
      <Button
        onClick={() => {
          const promise = new Promise((resolve, reject) => {
            setTimeout(() => {
              if (Math.random() > 0.5) {
                resolve('Data loaded successfully');
              } else {
                reject(new Error('Failed to load data'));
              }
            }, 2000);
          });

          toast.promise(promise, {
            loading: 'Loading data...',
            success: data => `${data}`,
            error: err => `${err.message}`,
          });
        }}
      >
        Show Promise Toast (Random Result)
      </Button>
    </div>
  ),
  args: {
    position: 'bottom-right',
    theme: 'system',
    closeButton: true,
    richColors: true,
  },
};

export const AllToastTypes: Story = {
  render: args => (
    <div className="flex flex-col items-center gap-4">
      <BaseToast {...args} />
      <div className="flex flex-wrap justify-center gap-2">
        <Button
          onClick={() => {
            toast('Default toast notification');
          }}
        >
          Default
        </Button>
        <Button
          onClick={() => {
            toast.success('Success toast notification');
          }}
        >
          Success
        </Button>
        <Button
          onClick={() => {
            toast.error('Error toast notification');
          }}
        >
          Error
        </Button>
        <Button
          onClick={() => {
            toast.info('Info toast notification');
          }}
        >
          Info
        </Button>
        <Button
          onClick={() => {
            toast.warning('Warning toast notification');
          }}
        >
          Warning
        </Button>
      </div>
    </div>
  ),
  args: {
    position: 'bottom-right',
    theme: 'system',
    closeButton: true,
    richColors: true,
  },
};
