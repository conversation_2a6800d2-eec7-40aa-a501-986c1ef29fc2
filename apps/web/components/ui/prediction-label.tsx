import { cn } from '@repo/ui/lib/utils';

const COLORS: Record<string, string> = {
  yes: '#8dc016',
  no: '#f76566',
  'graph-1': '#f19595',
  'graph-2': '#f1c695',
  'graph-3': '#f1eb8d',
  'graph-4': '#c9f18d',
  'graph-5': '#a9e9a3',
  'graph-6': '#a3e9df',
  'graph-7': '#b5d0f1',
  'graph-8': '#bcb5f1',
  'graph-9': '#ebc2ef',
  'graph-10': '#ffc3d7',
  'graph-11': '#dad2d5',
};

const getColorValue = (colorName?: string, fallbackColor?: string) => {
  if (colorName && colorName in COLORS) {
    return COLORS[colorName];
  }
  return fallbackColor || COLORS['graph-1']; // Default to graph-1 if no color specified
};

/**
 * Adds 20% opacity to a hex color
 */
const withOpacity = (hexColor: string, opacity: number = 0.2): string => {
  // Convert hex to RGB
  const hex = hexColor.replace('#', '');
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  return `rgba(${r}, ${g}, ${b}, ${opacity})`;
};

interface PredictionLabelProps {
  label: string;
  color?: string;
  colorName?: string;
  className?: string;
}

export default function PredictionLabel({
  label,
  color,
  colorName,
  className = '',
}: PredictionLabelProps) {
  const baseColor = color || getColorValue(colorName);
  const backgroundColor = withOpacity(baseColor ?? '');
  const textColor = colorName ? getColorValue(colorName) : baseColor;
  return (
    <div
      className={cn(
        'text-size-xxs10 px-space-8 rounded-round-md flex h-[20px] items-center font-semibold',
        className
      )}
      style={{
        backgroundColor,
        color: textColor,
        border: `1px solid ${textColor}20`, // 20% opacity border
      }}
    >
      {label}
    </div>
  );
}
