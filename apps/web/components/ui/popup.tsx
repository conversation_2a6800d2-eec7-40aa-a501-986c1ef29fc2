import * as React from 'react';

import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogClose,
  DialogPortal,
  DialogOverlay,
  DialogHeader,
  DialogTitle,
} from '@repo/ui/components/dialog';
import { cn } from '@repo/ui/lib/utils';
import { X } from 'lucide-react';

export interface PopupProps {
  isOpen: boolean;
  onClose: () => void;
  children: React.ReactNode;
  showCloseButton?: boolean;
}

function Popup({ isOpen, onClose, children, showCloseButton = true }: PopupProps) {
  return (
    <Dialog open={isOpen} onOpenChange={open => !open && onClose()}>
      <DialogPortal>
        <DialogOverlay className="bg-black/50" />
        <DialogTitle className="hidden" />
        <PopupContent>
          {showCloseButton && (
            <DialogClose asChild>
              <button
                onClick={onClose}
                className="absolute top-4 right-4 rounded-sm opacity-70 ring-offset-white transition-opacity hover:opacity-100 focus:outline-none"
              >
                <X className="h-4 w-4" />
                <span className="sr-only">Close</span>
              </button>
            </DialogClose>
          )}
          <PopupBody>{children}</PopupBody>
        </PopupContent>
      </DialogPortal>
    </Dialog>
  );
}

function PopupTrigger({ ...props }: React.ComponentProps<typeof DialogTrigger>) {
  return <DialogTrigger {...props} />;
}

function PopupContent({ className, ...props }: React.ComponentProps<typeof DialogContent>) {
  return (
    <DialogContent
      className={cn('max-w-(--popup-width) rounded-none border-none bg-white p-0', className)}
      {...props}
    />
  );
}

function PopupHeader({ className, ...props }: React.ComponentProps<typeof DialogHeader>) {
  return <DialogHeader className={cn('', className)} {...props} />;
}

function PopupBody({ className, ...props }: React.ComponentProps<'div'>) {
  return <div data-slot="popup-body" className={cn('flex flex-col', className)} {...props} />;
}

function PopupFooter({ className, ...props }: React.ComponentProps<'div'>) {
  return <footer className={cn('flex flex-row justify-center', className)} {...props} />;
}

export { Popup, PopupTrigger, PopupContent, PopupHeader, PopupBody, PopupFooter };
