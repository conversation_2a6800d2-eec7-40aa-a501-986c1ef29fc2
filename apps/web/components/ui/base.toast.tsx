import { Toaster, toast, type ToasterProps } from '@repo/ui/components/sonner';
import { cn, cva } from '@repo/ui/lib/utils';

const customVariant = {
  // Add custom variants here if needed
};

type CustomVariant = keyof typeof customVariant;

const customTheme = {
  // Add custom themes here if needed
};

type CustomTheme = keyof typeof customTheme;

type BaseToastProps = Omit<ToasterProps, 'theme'> & {
  variant?: CustomVariant;
  theme?: ToasterProps['theme'] | CustomTheme;
};

const extendedToastVariants = cva('', {
  variants: {
    variant: customVariant,
    theme: customTheme,
  },
});

export function BaseToast({ className, variant, theme, ...props }: BaseToastProps) {
  const overrideClassName = cn(
    extendedToastVariants({
      variant: variant as CustomVariant,
      theme: theme as CustomTheme,
    }),
    className
  );

  return (
    <Toaster className={overrideClassName} theme={theme as ToasterProps['theme']} {...props} />
  );
}

export { toast };
