import type { Meta, StoryObj } from '@storybook/react';
import FundUsdcPopup from './fund-usdc-popup';
import SvgIcon from '@/components/icons/svg-icon';

const meta: Meta<typeof FundUsdcPopup> = {
  title: 'UI/Popup/FundUsdcPopup',
  component: FundUsdcPopup,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    onClose: { action: 'closed' },
    onCreateAddress: { action: 'create address clicked' },
    onSkip: { action: 'skip clicked' },
  },
};

export default meta;
type Story = StoryObj<typeof FundUsdcPopup>;

export const Default: Story = {
  args: {},
  render: args => (
    <div className="rounded-lg bg-white p-6 shadow-lg">
      <FundUsdcPopup {...args} />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: '기본 Fund USDC 팝업 컴포넌트',
      },
    },
  },
};

export const WithDarkBackground: Story = {
  args: {},
  render: args => (
    <div className="bg-dark rounded-lg p-6 shadow-lg">
      <FundUsdcPopup {...args} className="rounded-lg bg-white p-6" />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: '어두운 배경에서의 Fund USDC 팝업 컴포넌트',
      },
    },
  },
};
