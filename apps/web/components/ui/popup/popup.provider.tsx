'use client';
import { useCallback } from 'react';
import { Popup } from '../popup';
import { usePopupStore } from './popup.state';

export default function PopupProvider({ children }: { children: React.ReactNode }) {
  const { jsx, isOpen, closePopup } = usePopupStore();

  const onClose = useCallback(() => {
    closePopup();
  }, [closePopup]);

  return (
    <>
      {children}
      <Popup onClose={onClose} isOpen={isOpen}>
        {jsx}
      </Popup>
    </>
  );
}
