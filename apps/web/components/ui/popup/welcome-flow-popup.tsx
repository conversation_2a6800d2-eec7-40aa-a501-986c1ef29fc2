import React, { useState } from 'react';
import WelcomePopup from './welcome-popup';
import ChooseUsernamePopup from './choose-username-popup';
import ChooseEmailPopup from './choose-email-popup';
import FundYourAccountPopup from './fund-your-account-popup';
import DepositPopup from './deposit-popup';

type WelcomeFlowStep = 'welcome' | 'choose-username' | 'choose-email' | 'fund-account' | 'deposit';

interface WelcomeFlowPopupProps {
  onClose: () => void;
}

export default function WelcomeFlowPopup({ onClose }: WelcomeFlowPopupProps) {
  const [step, setStep] = useState<WelcomeFlowStep>('welcome');

  const handleNextStep = (nextStep: WelcomeFlowStep) => {
    setStep(nextStep);
  };

  const renderStep = () => {
    switch (step) {
      case 'welcome':
        return <WelcomePopup onConfirm={() => handleNextStep('choose-username')} />;
      case 'choose-username':
        return (
          <ChooseUsernamePopup
            onClose={onClose}
            onContinue={() => handleNextStep('choose-email')}
          />
        );
      case 'choose-email':
        return (
          <ChooseEmailPopup
            onContinue={() => handleNextStep('fund-account')}
            onSkip={() => handleNextStep('fund-account')}
          />
        );
      case 'fund-account':
        return (
          <FundYourAccountPopup
            onDepositFunds={() => handleNextStep('deposit')}
            onSkipForNow={onClose}
          />
        );
      case 'deposit':
        return <DepositPopup onClose={onClose} />;
      default:
        return null;
    }
  };

  return <div className="w-full">{renderStep()}</div>;
}
