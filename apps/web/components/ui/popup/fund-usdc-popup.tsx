import { cn } from '@repo/ui/lib/utils';
import React from 'react';
import SvgIcon from '@/components/icons/svg-icon';

interface FundUsdcPopupProps {
  onSkip?: () => void;
}

export default function FundUsdcPopup({ onSkip }: FundUsdcPopupProps) {
  return (
    <div className={cn('flex w-full max-w-(--popup-width) flex-col items-center')}>
      <div className="mb-space-40">
        <SvgIcon name="UsdcIcon" />
      </div>

      <div className="mb-space-20 text-center">
        <h1 className="text-size-sm text-dark font-bold">Fund Your Account</h1>
      </div>

      <div className="text-size-xs text-gray-3 mb-space-20">Create deposit address</div>

      <div className="self-end">
        <button
          onClick={onSkip}
          className="text-no-red text-size-xxs font-medium underline hover:underline"
        >
          Skip for now
        </button>
      </div>
    </div>
  );
}
