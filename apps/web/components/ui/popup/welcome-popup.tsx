import Image from 'next/image';
import { InfoButton } from '@/components/ui/base.button';

interface WelcomePopupProps {
  onConfirm: () => void;
}

export default function WelcomePopup({ onConfirm }: WelcomePopupProps) {
  return (
    <div className="bg-gray-2 w-full">
      {/* Header */}
      <div className="py-space-50 gap-space-40 flex flex-col items-center justify-center text-center">
        <div className="relative">
          <Image src="/icons/check.svg" alt="Welcome" width={50} height={50} />
        </div>
        <div className="gap-space-20 flex flex-col">
          <h1 className="text-size-sm font-semibold">Welcome!</h1>
          <p className="text-size-xs text-gray-3">
            환영합니다! PredictGo에 오신 것을 축하드립니다.
          </p>
        </div>
      </div>

      {/* Action Button */}
      <div className="flex">
        <InfoButton onClick={onConfirm} size="lg" fontSize="xs" rounded="none" className="flex-1">
          시작하기
        </InfoButton>
      </div>
    </div>
  );
}
