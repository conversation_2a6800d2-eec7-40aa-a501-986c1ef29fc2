import React, { useState } from 'react';
import { Popup } from '../popup';
import DepositUsdcPopup from './deposit-usdc-popup';
import { InfoButton } from '../base.button';

/**
 * 사용 예시: Deposit USDC 팝업을 트리거하는 컴포넌트
 *
 * 실제 사용할 때는 이 컴포넌트를 참고하여 구현하세요.
 */
export default function DepositUsdcPopupExample() {
  const [isDepositPopupOpen, setIsDepositPopupOpen] = useState(false);

  // 팝업 열기
  const handleOpenDepositPopup = () => {
    setIsDepositPopupOpen(true);
  };

  // 팝업 닫기
  const handleCloseDepositPopup = () => {
    setIsDepositPopupOpen(false);
  };

  // Continue 버튼 클릭 시 실행될 함수
  const handleDepositContinue = () => {
    console.log('Deposit process continuing...');
    // 여기에 실제 로직을 구현합니다.
    // 예: 디포짓 프로세스 계속 진행, 다른 페이지로 이동 등
    setIsDepositPopupOpen(false);
  };

  return (
    <div className="p-8">
      <h2 className="mb-4 text-lg font-semibold">Deposit USDC 팝업 사용 예시</h2>

      {/* 팝업을 트리거하는 버튼 */}
      <InfoButton onClick={handleOpenDepositPopup}>Deposit USDC</InfoButton>

      {/* Deposit USDC Popup */}
      <Popup isOpen={isDepositPopupOpen} onClose={handleCloseDepositPopup} showCloseButton={true}>
        <DepositUsdcPopup
          onContinue={handleDepositContinue}
          depositAddress="0x334ad74A28231572BCd988F6A9e2Bf80355F5D5E"
        />
      </Popup>

      <div className="mt-8 rounded-lg bg-gray-100 p-4">
        <h3 className="mb-2 font-semibold">사용법:</h3>
        <pre className="text-sm">
          {`// 1. Popup과 DepositUsdcPopup import
import { Popup } from '@/components/ui/popup';
import DepositUsdcPopup from '@/components/ui/popup/deposit-usdc-popup';

// 2. 상태 관리
const [isDepositPopupOpen, setIsDepositPopupOpen] = useState(false);

// 3. JSX에서 사용
<Popup 
  isOpen={isDepositPopupOpen} 
  onClose={() => setIsDepositPopupOpen(false)} 
  showCloseButton={true}
>
  <DepositUsdcPopup 
    onContinue={() => {
      // Continue 버튼 클릭 시 실행할 로직
      console.log('Continuing deposit process...');
      setIsDepositPopupOpen(false);
    }}
    depositAddress="your-deposit-address-here"
  />
</Popup>`}
        </pre>
      </div>
    </div>
  );
}
