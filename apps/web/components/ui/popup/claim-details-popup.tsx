import React from 'react';
import { cn } from '@repo/ui/lib/utils';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/ui/components/table';

// 날짜를 'March 31, 2025' 형식으로 포맷팅하는 함수
const formatDate = (dateString: string): string => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
  });
};

interface ClaimDetail {
  id: string;
  time: string; // ISO 형식의 날짜 문자열 (예: '2023-12-01T14:30:25')
  amount: string;
}

interface ClaimDetailsPopupProps {
  className?: string;
  details?: ClaimDetail[];
}

// 샘플 데이터
const sampleClaimDetails: ClaimDetail[] = [
  { id: '1', time: '2023-12-01T14:30:25', amount: '$120.50' },
  { id: '2', time: '2023-12-05T09:15:10', amount: '$85.75' },
  { id: '3', time: '2023-12-10T16:45:30', amount: '$210.25' },
  { id: '4', time: '2023-12-15T11:20:45', amount: '$45.00' },
  { id: '5', time: '2023-12-20T13:10:15', amount: '$150.30' },
  { id: '6', time: '2023-12-25T10:05:50', amount: '$95.60' },
  { id: '7', time: '2023-12-30T15:40:20', amount: '$180.15' },
  { id: '8', time: '2024-01-05T12:25:35', amount: '$65.90' },
  { id: '9', time: '2024-01-10T08:50:40', amount: '$130.45' },
  { id: '10', time: '2024-01-15T17:30:55', amount: '$200.80' },
];

export default function ClaimDetailsPopup({
  className,
  details = sampleClaimDetails,
}: ClaimDetailsPopupProps) {
  return (
    <div className={cn('flex w-full max-w-(--popup-width) flex-col', className)}>
      <div className="mb-space-20 text-center">
        <h2 className="text-size-lg text-mid-dark font-semibold">Claim Details</h2>
      </div>

      <div>
        <Table>
          <TableHeader className="">
            <TableRow className="">
              <TableHead className="text-size-sm text-gray-3 w-1/2 font-semibold">Time</TableHead>
              <TableHead className="text-size-sm text-gray-3 w-1/2 text-right font-semibold">
                Amount
              </TableHead>
            </TableRow>
          </TableHeader>
        </Table>

        <div className="max-h-[300px] overflow-y-auto">
          <Table>
            <TableBody>
              {details.map(detail => (
                <TableRow key={detail.id} className="border-0">
                  <TableCell className="text-size-xs text-mid-dark py-space-15">
                    {formatDate(detail.time)}
                  </TableCell>
                  <TableCell className="text-size-xs text-mid-dark py-space-15 text-right font-semibold">
                    {detail.amount}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  );
}
