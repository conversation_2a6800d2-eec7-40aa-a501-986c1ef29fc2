import SvgIcon from '@/components/icons/svg-icon';
import { InfoButton } from '../base.button';

export default function CheckEmailPopup() {
  return (
    <>
      <div className="px-space-20 py-space-30 space-y-[40px]">
        <header className="gap-space-30 flex flex-col items-center justify-center">
          <div className="mb-space-20">
            <SvgIcon name="WalletIcon" />
          </div>
          <h1 className="text-size-base font-bold">Check your email for the verification link.</h1>
          <p className="text-size-xs text-gray-3 text-center">
            Didn't get the email? <button className="text-no-red">Resend.</button>
          </p>
        </header>
      </div>
      <InfoButton className="w-full" rounded="none">
        Confirm
      </InfoButton>
    </>
  );
}
