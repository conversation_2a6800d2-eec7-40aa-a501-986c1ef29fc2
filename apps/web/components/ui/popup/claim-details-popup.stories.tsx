import type { Meta, StoryObj } from '@storybook/react';
import ClaimDetailsPopup from './claim-details-popup';

const meta: Meta<typeof ClaimDetailsPopup> = {
  title: 'Components/ui/ClaimDetailsPopup',
  component: ClaimDetailsPopup,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    className: {
      control: 'text',
      description: '추가 클래스명',
    },
    details: {
      control: 'object',
      description: '청구 상세 내역 데이터',
    },
  },
};

export default meta;
type Story = StoryObj<typeof ClaimDetailsPopup>;

// 기본 스토리
export const Default: Story = {
  args: {},
  render: args => (
    <div className="bg-white p-6" style={{ width: '500px' }}>
      <ClaimDetailsPopup {...args} />
    </div>
  ),
};

// 적은 데이터 스토리
export const FewItems: Story = {
  args: {
    details: [
      { id: '1', time: '2024-01-15T17:30:55', amount: '$200.80' },
      { id: '2', time: '2024-01-10T08:50:40', amount: '$130.45' },
      { id: '3', time: '2024-01-05T12:25:35', amount: '$65.90' },
    ],
  },
  render: args => (
    <div className="bg-white p-6" style={{ width: '500px' }}>
      <ClaimDetailsPopup {...args} />
    </div>
  ),
};

// 많은 데이터 스토리 (스크롤 테스트)
export const ManyItems: Story = {
  args: {
    details: Array.from({ length: 20 }, (_, i) => ({
      id: `${i + 1}`,
      time: `2024-${String(Math.floor(i / 3) + 1).padStart(2, '0')}-${String((i % 28) + 1).padStart(2, '0')}T${String(Math.floor(Math.random() * 24)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}:${String(Math.floor(Math.random() * 60)).padStart(2, '0')}`,
      amount: `$${(Math.random() * 500 + 50).toFixed(2)}`,
    })),
  },
  render: args => (
    <div className="bg-white p-6" style={{ width: '500px' }}>
      <ClaimDetailsPopup {...args} />
    </div>
  ),
};

// 커스텀 스타일 스토리
export const CustomStyle: Story = {
  args: {
    className: 'border border-sky p-4 rounded-lg',
  },
  render: args => (
    <div className="bg-white p-6" style={{ width: '500px' }}>
      <ClaimDetailsPopup {...args} />
    </div>
  ),
};
