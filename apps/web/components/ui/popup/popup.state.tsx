import { create } from 'zustand';
import { combine } from 'zustand/middleware';
import EnableWalletPopup from './enable-wallet';

export type PopupContentName = 'ENABLE_WALLET' | 'CHOOSE_USERNAME';

export type PopupButtonConfig = {
  key: string;
  children: React.ReactNode;
  onClick: () => void;
  variant?: 'default' | 'secondary';
};

export type PopupConfig = {
  content: PopupContentName;
  title?: string;
  description?: string;
  buttons: PopupButtonConfig[];
};

export const usePopupStore = create(
  combine(
    {
      jsx: null as React.ReactNode | null,
      isOpen: false,
    },
    set => ({
      openPopup: (jsx: React.ReactNode) => {
        set({ jsx, isOpen: true });
      },
      closePopup: () => {
        set({ jsx: null, isOpen: false });
      },
    })
  )
);

export const POPUP_CONTENT_MAP: Record<PopupContentName, React.ComponentType<any>> = {
  ENABLE_WALLET: EnableWalletPopup,
  CHOOSE_USERNAME: () => (
    <div className="text-center">
      <h3 className="mb-2 text-lg font-semibold">사용자명을 선택하세요</h3>
      <p className="text-gray-600">고유한 사용자명을 입력해주세요.</p>
    </div>
  ),
};

// Predefined popup configurations
export const POPUP_CONFIGS = {
  ENABLE_WALLET: {
    content: 'ENABLE_WALLET' as PopupContentName,
    buttons: [
      {
        key: 'enable',
        children: '지갑 연결',
        onClick: () => {
          console.log('지갑 연결 시도');
          usePopupStore.getState().closePopup();
        },
        variant: 'default' as const,
      },
      {
        key: 'cancel',
        children: '취소',
        onClick: () => {
          usePopupStore.getState().closePopup();
        },
        variant: 'secondary' as const,
      },
    ],
  },
  CHOOSE_USERNAME: {
    content: 'CHOOSE_USERNAME' as PopupContentName,
    buttons: [
      {
        key: 'confirm',
        children: '확인',
        onClick: () => {
          // 사용자명 설정 로직
          console.log('사용자명 설정');
          usePopupStore.getState().closePopup();
        },
        variant: 'default' as const,
      },
      {
        key: 'cancel',
        children: '취소',
        onClick: () => {
          usePopupStore.getState().closePopup();
        },
        variant: 'secondary' as const,
      },
    ],
  },
} as const;

export const usePopupActions = () => {
  const { openPopup, closePopup } = usePopupStore();

  const openEnableWalletPopup = () => {
    openPopup(<EnableWalletPopup />);
  };

  const openChooseUsernamePopup = () => {
    const ChooseUsernameComponent = POPUP_CONTENT_MAP.CHOOSE_USERNAME;
    openPopup(<ChooseUsernameComponent />);
  };

  return {
    openPopup,
    closePopup,
    openEnableWalletPopup,
    openChooseUsernamePopup,
  };
};
