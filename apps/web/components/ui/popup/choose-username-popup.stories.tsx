import type { Meta, StoryObj } from '@storybook/react';
import ChooseUsernamePopup from './choose-username-popup';

const meta: Meta<typeof ChooseUsernamePopup> = {
  title: 'Popup/ChooseUsernamePopup',
  component: ChooseUsernamePopup,
  parameters: {
    layout: 'centered',
  },
};

export default meta;
type Story = StoryObj<typeof ChooseUsernamePopup>;

export const Default: Story = {
  args: {},
};

// export const WithPrefilledUsername: Story = {
//   args: {},
//   render: () => (
//     <div className="bg-white p-6 rounded-lg shadow-lg max-w-md">
//       <ChooseUsernamePopup />
//     </div>
//   ),
//   parameters: {
//     docs: {
//       description: {
//         story: "Username popup rendered with a container for better visibility",
//       },
//     },
//   },
// };

// export const Mobile: Story = {
//   args: {},
//   parameters: {
//     viewport: {
//       defaultViewport: "mobile1",
//     },
//     docs: {
//       description: {
//         story: "Username popup displayed on a mobile viewport",
//       },
//     },
//   },
// };
