import type { Meta, StoryObj } from '@storybook/react';
import EnableWalletPopup from './enable-wallet';

const meta: Meta<typeof EnableWalletPopup> = {
  title: 'UI/Popup/EnableWalletPopup',
  component: EnableWalletPopup,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof EnableWalletPopup>;

export const Default: Story = {
  args: {},
  render: () => (
    <div className="max-w-md rounded-lg bg-white p-6 shadow-lg">
      <EnableWalletPopup />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Enable Wallet popup rendered with a container for better visibility',
      },
    },
  },
};

export const WithoutContainer: Story = {
  args: {},
  render: () => <EnableWalletPopup />,
  parameters: {
    docs: {
      description: {
        story: 'Enable Wallet popup without container styling',
      },
    },
  },
};
