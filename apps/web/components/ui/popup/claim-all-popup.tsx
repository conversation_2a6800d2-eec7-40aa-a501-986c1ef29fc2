import { DarkButton, InfoButton } from '@/components/ui/base.button';
import { formatCurrency } from '@/lib/format';
import Image from 'next/image';
import { cn } from '@repo/ui/lib/utils';

export enum ClaimStage {
  Initial = 'initial',
  InProgress = 'inProgress',
  Completed = 'completed',
}

interface MarketData {
  title: string;
  imageUrl: string;
  value: number;
}

interface ClaimAllPopupProps {
  claimStage: ClaimStage;
  // Props for ClaimPopup
  totalClaimAmount?: number;
  markets?: MarketData[];
  onConfirm?: () => void;
  onCancel?: () => void;
  // Props for ClaimInProgressPopup
  submittedTransactions?: number;
  totalTransactions?: number;
  // Props for ClaimCompletedPopup
  onShareWinnings?: () => void;
}

export default function ClaimAllPopup({
  claimStage,
  totalClaimAmount,
  markets,
  onConfirm,
  onCancel,
  submittedTransactions,
  totalTransactions,
  onShareWinnings,
}: ClaimAllPopupProps) {
  switch (claimStage) {
    case ClaimStage.Initial:
      if (totalClaimAmount === undefined || !markets || !onConfirm || !onCancel) {
        console.error(
          'ClaimPopup requires totalClaimAmount, markets, onConfirm, and onCancel props.'
        );
        return null;
      }
      return (
        <div className="bg-gray-2 w-full">
          {/* Header */}
          <div className="py-space-50 gap-space-40 flex flex-col items-center justify-center text-center">
            <div className="gap-space-20 flex flex-col">
              <h1 className="text-size-xl font-semibold">Claim</h1>
              <p className="text-size-sm text-gray-3">
                Complete the claim process to receive {formatCurrency(totalClaimAmount)}.
              </p>
            </div>
          </div>

          {/* Market List */}
          <div className="px-space-20 pb-space-20 flex flex-col">
            <div className="text-size-xs text-gray-3 mb-space-10 gap-space-15 flex justify-between">
              <div className="gap-space-10 flex min-w-0 flex-1 items-center">
                <span>{markets.length} Markets</span>
              </div>
              <span className="flex-shrink-0">Value</span>
            </div>
            <div className="pr-space-5 max-h-60 overflow-y-auto">
              {markets.map((market, index) => (
                <div
                  key={index}
                  className="py-space-10 gap-space-15 flex items-center justify-between"
                >
                  <div className="gap-space-10 flex min-w-0 flex-1 items-center">
                    {market.imageUrl && (
                      <Image
                        src={market.imageUrl}
                        alt={market.title}
                        width={38}
                        height={38}
                        className="size-[38px] rounded-full"
                      />
                    )}
                    <span className="text-size-xs text-mid-dark max-w-48 truncate font-semibold">
                      {market.title}
                    </span>
                  </div>
                  <span className="text-size-sm text-mid-dark flex-shrink-0 font-medium">
                    {formatCurrency(market.value)}
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex">
            <InfoButton
              onClick={onConfirm}
              size="lg"
              fontSize="xs"
              rounded="none"
              className="flex-1"
            >
              Confirm
            </InfoButton>
          </div>
        </div>
      );
    case ClaimStage.InProgress:
      if (submittedTransactions === undefined || totalTransactions === undefined) {
        console.error(
          'ClaimInProgressPopup requires submittedTransactions and totalTransactions props.'
        );
        return null;
      }
      return (
        <div className="bg-gray-2 w-full">
          {/* Header */}
          <div className="py-space-50 gap-space-40 flex flex-col items-center justify-center text-center">
            <div className="gap-space-20 flex flex-col">
              <h1 className="text-size-xl font-semibold">Claim in Progress</h1>
              <div className="relative h-20 w-20">
                {/* Loading Spinner - using a placeholder div, ideally an SVG or component for actual spinner */}
                <div className="h-full w-full animate-spin rounded-full border-4 border-blue-500 border-t-transparent"></div>
              </div>
              <p className="text-size-sm text-gray-3">Loading</p>
            </div>
          </div>

          {/* Footer */}
          <div
            className={cn(
              'py-space-20',
              'px-space-20',
              'flex',
              'justify-center',
              'items-center',
              'bg-gray-3',
              'text-size-sm',
              'text-white',
              'font-semibold'
            )}
          >
            Submitted {submittedTransactions} of {totalTransactions} transactions.
          </div>
        </div>
      );
    case ClaimStage.Completed:
      if (!onShareWinnings) {
        console.error('ClaimCompletedPopup requires onShareWinnings prop.');
        return null;
      }
      return (
        <div className="bg-gray-2 w-full">
          {/* Header */}
          <div className="py-space-50 gap-space-40 flex flex-col items-center justify-center text-center">
            <svg
              width="50"
              height="50"
              viewBox="0 0 50 50"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <rect width="50" height="50" rx="25" fill="#34C759" />
              {/* Green color for success */}
              <path
                d="M21 34.5L14.5 28L16.25 26.25L21 31L33.75 18.25L35.5 20L21 34.5Z"
                fill="white"
              />
            </svg>
            <div className="gap-space-20 flex flex-col">
              <h1 className="text-size-xl font-semibold">Claim Completed</h1>
              <p className="text-size-sm text-gray-3">Claim completed successfully.</p>
              <p className="text-size-xs text-gray-3">
                Share your achievements to claim bonus reward!
              </p>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex">
            <InfoButton
              onClick={onShareWinnings}
              size="lg"
              fontSize="xs"
              rounded="none"
              className="flex-1"
            >
              Share my Winnings
            </InfoButton>
          </div>
        </div>
      );
    default:
      return null;
  }
}
