import type { Meta, StoryObj } from '@storybook/react';
import DepositPopup from './deposit-popup';

const meta: Meta<typeof DepositPopup> = {
  title: 'UI/Popup/DepositPopup',
  component: DepositPopup,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    cryptoAddress: { control: 'text' },
    onClose: { action: 'closed' },
    onCopy: { action: 'copied address' },
    onShowQR: { action: 'show QR code' },
    onBuyCrypto: { action: 'buy crypto clicked' },
  },
};

export default meta;
type Story = StoryObj<typeof DepositPopup>;

export const Default: Story = {
  args: {
    cryptoAddress: '0×334ad74A28231572BCd988F8Ab9e2Bf8055F5D5E',
  },
  render: args => (
    <div className="rounded-lg bg-white p-6 shadow-lg">
      <DepositPopup {...args} />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: '기본 입금 팝업 컴포넌트',
      },
    },
  },
};

export const WithDifferentAddress: Story = {
  args: {
    cryptoAddress: '0xabc123def456ghi789jkl012mno345pqr678stu',
  },
  render: args => (
    <div className="rounded-lg bg-white p-6 shadow-lg">
      <DepositPopup {...args} />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: '다른 주소값을 가진 입금 팝업',
      },
    },
  },
};

export const WithDarkBackground: Story = {
  args: {
    cryptoAddress: '0×334ad74A28231572BCd988F8Ab9e2Bf8055F5D5E',
  },
  render: args => (
    <div className="bg-dark rounded-lg p-6 shadow-lg">
      <DepositPopup {...args} className="rounded-lg bg-white p-6" />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: '어두운 배경에서의 입금 팝업 컴포넌트',
      },
    },
  },
};
