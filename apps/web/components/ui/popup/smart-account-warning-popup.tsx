import { InfoButton } from '../base.button';
import { logout } from '@/lib/auth';
import { useAppKit, useAppKitAccount } from '@reown/appkit/react';
import { useState, useEffect } from 'react';
import { Popup } from '../popup';

export function SmartAccountWarningPopup() {
  const { address, embeddedWalletInfo } = useAppKitAccount({ namespace: 'eip155' });
  const { close } = useAppKit();
  const [isSmartAccountWarningOpen, setIsSmartAccountWarningOpen] = useState(false);

  const isUserSmartAccount =
    address && embeddedWalletInfo && embeddedWalletInfo?.accountType !== 'eoa';

  const handleClosePopup = () => {
    // 스마트 계정일 때는 닫을 수 없음
    if (!isUserSmartAccount) {
      setIsSmartAccountWarningOpen(false);
    }
  };

  // 스마트 계정 경고 팝업 관리
  useEffect(() => {
    if (isUserSmartAccount) {
      close();
      setIsSmartAccountWarningOpen(true);
    } else {
      setIsSmartAccountWarningOpen(false);
    }
  }, [isUserSmartAccount, embeddedWalletInfo]);

  //embeddedWalletInfo
  useEffect(() => {
    console.log('🔍 [SmartAccountWarningPopup] embeddedWalletInfo:', embeddedWalletInfo);
  }, [embeddedWalletInfo]);

  return (
    <>
      {isUserSmartAccount && (
        <Popup isOpen={isSmartAccountWarningOpen} onClose={handleClosePopup} zIndex={20000}>
          <div className="px-space-20 py-space-30 space-y-[40px]">
            <header className="gap-space-30 flex flex-col items-center justify-center">
              <div className="mb-space-20"></div>
              <h1 className="text-size-base font-bold">Only EOA accounts supported</h1>
              <p className="text-size-xs text-gray-3 text-center">
                You are currently connected with a smart account.
                <br />
                PredictGo only supports EOA accounts.
                <br />
                Please disconnect and use an EOA account.
              </p>
            </header>
          </div>
          <InfoButton
            className="w-full"
            rounded="none"
            onClick={() => {
              logout();
              setTimeout(() => {
                window.location.reload();
              }, 1000);
            }}
          >
            Disconnect
          </InfoButton>
        </Popup>
      )}
    </>
  );
}
