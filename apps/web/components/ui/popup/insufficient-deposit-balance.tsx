import { DarkButton, InfoButton } from '@/components/ui/base.button';
import { ICON_PATH } from '@/lib/constants';
import Image from 'next/image';

interface InsufficientDepositBalanceProps {
  requiredAmount?: string;
  onAddDeposit: () => void;
  onCancel: () => void;
}

export default function InsufficientDepositBalance({
  requiredAmount = '$50',
  onAddDeposit,
  onCancel,
}: InsufficientDepositBalanceProps) {
  return (
    <div className="bg-gray-2 w-full">
      {/* Header */}
      <div className="py-space-50 gap-space-30 flex flex-col items-center justify-center text-center">
        {/* Warning Icon */}
        <Image src={ICON_PATH.ALERT_RED_BIG} alt="warning" width={50} height={50} />

        {/* Text Content */}
        <div className="gap-space-20 flex flex-col">
          <h1 className="text-size-sm text-mid-dark font-semibold">Insufficient Deposit Balance</h1>
          <div className="text-size-xs text-gray-3">
            <p>To create a prediction,</p>
            <p>you must have at least {requiredAmount} in available deposit.</p>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex">
        <DarkButton onClick={onCancel} size="lg" fontSize="xs" rounded="none" className="flex-1">
          Cancel
        </DarkButton>
        <InfoButton
          onClick={onAddDeposit}
          size="lg"
          fontSize="xs"
          rounded="none"
          className="flex-1"
        >
          Add Deposit
        </InfoButton>
      </div>
    </div>
  );
}
