import { Label } from '@repo/ui/components/label';
import { BaseInput } from '../base.input';
import { Checkbox } from '@repo/ui/components/checkbox';
import { useState } from 'react';
import { InfoButton } from '../base.button';
import { useUpdateUserProfile } from '@/hooks/query/user';
import { toast } from '@repo/ui/components/sonner';

interface ChooseUsernamePopupProps {
  onClose: () => void;
  onContinue: () => void;
}

export default function ChooseUsernamePopup({ onClose, onContinue }: ChooseUsernamePopupProps) {
  const [username, setUsername] = useState('');
  const [isAgree, setIsAgree] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const updateProfile = useUpdateUserProfile();

  const isValidUsername = (username: string) => {
    // a-z, A-Z, 0-9 allowed, up to 42 chars
    const usernameRegex = /^[a-zA-Z0-9]*$/;
    return username.length >= 2 && username.length <= 42 && usernameRegex.test(username);
  };

  const canContinue = username.trim() && isValidUsername(username) && isAgree && !isSubmitting;

  const handleContinue = async () => {
    if (!canContinue) return;

    try {
      setIsSubmitting(true);
      const formData = new FormData();
      formData.append('nickname', username);

      await updateProfile.mutateAsync(formData);
      toast.success('Username saved successfully.');
      onContinue();
    } catch (error) {
      console.error('Failed to save username:', error);
      toast.error('Failed to save username.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="space-y-[40px]">
      <header className="gap-space-30 flex flex-col items-center justify-center">
        <h1 className="text-size-base font-bold">Choose a username</h1>
        <p className="text-size-xs text-gray-3">You can update this later</p>
      </header>
      <div className="space-y-space-30">
        <div>
          <Label className="text-size-xs mb-space-10 font-semibold">Username</Label>
          <BaseInput
            value={username}
            onChange={e => setUsername(e.target.value)}
            placeholder="@username"
            className="mb-space-15"
            disabled={isSubmitting}
          />
          <p className="text-size-xxs text-gray-3 font-medium">
            a-z, A-Z, 0-9 allowed, up to 42 chars
          </p>
        </div>
        <div>
          <label className="gap-space-6 flex items-start">
            <Checkbox
              checked={isAgree}
              onCheckedChange={checked => setIsAgree(!!checked)}
              disabled={isSubmitting}
            />
            <div className="text-size-xxs text-gray-3 font-semibold">
              Legal requirements differ by the regions, and certain types of transactions may be
              prohibited in some regions. Thus, users must comply with local laws and
              regulations.Before using the service, I have read and agree to the PredictGo Terms of
              Use.
            </div>
          </label>
        </div>
      </div>
      <InfoButton
        className="w-full"
        rounded="none"
        onClick={handleContinue}
        disabled={!canContinue}
      >
        {isSubmitting ? 'Saving...' : 'Continue'}
      </InfoButton>
    </div>
  );
}
