import { DarkButton, InfoButton } from '@/components/ui/base.button';

interface ConfirmUnsubscribePopupProps {
  onConfirm: () => void;
  onCancel: () => void;
  channelName?: string;
}

export default function ConfirmUnsubscribePopup({
  onConfirm,
  onCancel,
  channelName = 'Channel Name',
}: ConfirmUnsubscribePopupProps) {
  return (
    <div className="bg-gray-2 w-full">
      {/* Header */}
      <div className="py-space-50 gap-space-40 flex flex-col items-center justify-center text-center">
        <svg
          width="50"
          height="50"
          viewBox="0 0 50 50"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect width="50" height="50" rx="25" fill="#F76566" />
          <path
            d="M25 50C19.4772 50 14.1215 47.8398 10.1508 43.8691C6.18013 39.8985 4.02 34.5428 4.02 29.02C4.02 23.4972 6.18013 18.1415 10.1508 14.1708C14.1215 10.2002 19.4772 8.04 25 8.04C30.5228 8.04 35.8785 10.2002 39.8492 14.1708C43.8199 18.1415 45.98 23.4972 45.98 29.02C45.98 34.5428 43.8199 39.8985 39.8492 43.8691C35.8785 47.8398 30.5228 50 25 50ZM25 38.3471C26.387 38.3471 27.5 37.234 27.5 35.847C27.5 34.46 26.387 33.347 25 33.347C23.613 33.347 22.5 34.46 22.5 35.847C22.5 37.234 23.613 38.3471 25 38.3471ZM25 29.5929C26.387 29.5929 27.5 28.4799 27.5 27.0929V14.5929C27.5 13.2059 26.387 12.0929 25 12.0929C23.613 12.0929 22.5 13.2059 22.5 14.5929V27.0929C22.5 28.4799 23.613 29.5929 25 29.5929Z"
            fill="white"
          />
        </svg>
        <div className="gap-space-20 flex flex-col">
          <h1 className="text-size-sm font-semibold">Confirm Unsubscribe</h1>
          <p className="text-size-xs text-gray-3">
            Would you like to unsubscribe from "{channelName}"?
          </p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex">
        <DarkButton onClick={onCancel} size="lg" fontSize="sm" rounded="none" className="flex-1">
          Cancel
        </DarkButton>
        <InfoButton onClick={onConfirm} size="lg" fontSize="sm" rounded="none" className="flex-1">
          Unsubscribe
        </InfoButton>
      </div>
    </div>
  );
}
