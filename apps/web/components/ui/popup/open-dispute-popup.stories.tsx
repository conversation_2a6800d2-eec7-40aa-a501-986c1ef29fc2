import { <PERSON><PERSON> } from '@repo/ui/components/button';
import type { Meta, StoryObj } from '@storybook/react';
import * as React from 'react';
import { Popup } from '../popup';
import OpenDisputePopupBody from './open-dispute-popup';
import { WagmiContextProvider } from '../../providers/wagmi-provider';
import { TanstackQueryClientProvider } from '../../providers/tanstack-query-client-provider';

const meta: Meta<typeof OpenDisputePopupBody> = {
  title: 'Components/ui/Popup/OpenDisputePopup',
  component: OpenDisputePopupBody,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  decorators: [
    Story => (
      <WagmiContextProvider cookies={null}>
        <TanstackQueryClientProvider>
          <Story />
        </TanstackQueryClientProvider>
      </WagmiContextProvider>
    ),
  ],
  argTypes: {
    disputeAmount: {
      control: 'text',
      description: '분쟁 최소 금액',
    },
    isPending: {
      control: 'boolean',
      description: '로딩 상태',
    },
    onSubmit: {
      action: 'submitted',
      description: '폼 제출 핸들러',
    },
    onClose: {
      action: 'closed',
      description: '팝업 닫기 핸들러',
    },
  },
};

export default meta;

type Story = StoryObj<typeof OpenDisputePopupBody>;

// 기본 분쟁 팝업
export const Default: Story = {
  render: args => {
    const [isOpen, setIsOpen] = React.useState(false);

    return (
      <>
        <Button onClick={() => setIsOpen(true)}>분쟁 팝업 열기</Button>
        <Popup isOpen={isOpen} onClose={() => setIsOpen(false)} showCloseButton={true}>
          <OpenDisputePopupBody
            {...args}
            onClose={() => setIsOpen(false)}
            onSubmit={data => {
              console.log('Dispute submitted:', data);
              args.onSubmit?.(data);
            }}
          />
        </Popup>
      </>
    );
  },
  args: {
    disputeAmount: '10.00',
    isPending: false,
  },
};

// 로딩 상태
export const Loading: Story = {
  render: args => {
    const [isOpen, setIsOpen] = React.useState(false);

    return (
      <>
        <Button onClick={() => setIsOpen(true)}>로딩 상태 분쟁 팝업 열기</Button>
        <Popup isOpen={isOpen} onClose={() => setIsOpen(false)} showCloseButton={true}>
          <OpenDisputePopupBody
            {...args}
            onClose={() => setIsOpen(false)}
            onSubmit={data => {
              console.log('Dispute submitted:', data);
              args.onSubmit?.(data);
            }}
          />
        </Popup>
      </>
    );
  },
  args: {
    disputeAmount: '10.00',
    isPending: true,
  },
};

// 높은 분쟁 금액
export const HighDisputeAmount: Story = {
  render: args => {
    const [isOpen, setIsOpen] = React.useState(false);

    return (
      <>
        <Button onClick={() => setIsOpen(true)}>높은 금액 분쟁 팝업 열기</Button>
        <Popup isOpen={isOpen} onClose={() => setIsOpen(false)} showCloseButton={true}>
          <OpenDisputePopupBody
            {...args}
            onClose={() => setIsOpen(false)}
            onSubmit={data => {
              console.log('Dispute submitted:', data);
              args.onSubmit?.(data);
            }}
          />
        </Popup>
      </>
    );
  },
  args: {
    disputeAmount: '100.00',
    isPending: false,
  },
};

// 낮은 분쟁 금액
export const LowDisputeAmount: Story = {
  render: args => {
    const [isOpen, setIsOpen] = React.useState(false);

    return (
      <>
        <Button onClick={() => setIsOpen(true)}>낮은 금액 분쟁 팝업 열기</Button>
        <Popup isOpen={isOpen} onClose={() => setIsOpen(false)} showCloseButton={true}>
          <OpenDisputePopupBody
            {...args}
            onClose={() => setIsOpen(false)}
            onSubmit={data => {
              console.log('Dispute submitted:', data);
              args.onSubmit?.(data);
            }}
          />
        </Popup>
      </>
    );
  },
  args: {
    disputeAmount: '5.00',
    isPending: false,
  },
};

// 닫기 버튼 없는 버전
export const WithoutCloseButton: Story = {
  render: args => {
    const [isOpen, setIsOpen] = React.useState(false);

    return (
      <>
        <Button onClick={() => setIsOpen(true)}>닫기 버튼 없는 분쟁 팝업 열기</Button>
        <Popup isOpen={isOpen} onClose={() => setIsOpen(false)} showCloseButton={false}>
          <OpenDisputePopupBody
            {...args}
            onClose={() => setIsOpen(false)}
            onSubmit={data => {
              console.log('Dispute submitted:', data);
              args.onSubmit?.(data);
            }}
          />
        </Popup>
      </>
    );
  },
  args: {
    disputeAmount: '10.00',
    isPending: false,
  },
};

// 컴포넌트만 표시 (팝업 없이)
export const ComponentOnly: Story = {
  render: args => (
    <div className="max-w-md">
      <OpenDisputePopupBody
        {...args}
        onSubmit={data => {
          console.log('Dispute submitted:', data);
          args.onSubmit?.(data);
        }}
      />
    </div>
  ),
  args: {
    disputeAmount: '10.00',
    isPending: false,
  },
  parameters: {
    layout: 'padded',
  },
};

// 로딩 상태 컴포넌트만 표시
export const ComponentOnlyLoading: Story = {
  render: args => (
    <div className="max-w-md">
      <OpenDisputePopupBody
        {...args}
        onSubmit={data => {
          console.log('Dispute submitted:', data);
          args.onSubmit?.(data);
        }}
      />
    </div>
  ),
  args: {
    disputeAmount: '10.00',
    isPending: true,
  },
  parameters: {
    layout: 'padded',
  },
};
