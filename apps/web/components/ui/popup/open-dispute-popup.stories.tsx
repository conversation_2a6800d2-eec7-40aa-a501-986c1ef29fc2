import type { Meta, StoryObj } from '@storybook/react';
import OpenDisputePopupBody from './open-dispute-popup';

const meta: Meta<typeof OpenDisputePopupBody> = {
  title: 'Popup/OpenDisputePopup',
  component: OpenDisputePopupBody,
  parameters: {
    layout: 'centered',
  },
};

export default meta;
type Story = StoryObj<typeof OpenDisputePopupBody>;

export const Default: Story = {
  args: {
    minDeposit: 10,
    maxDeposit: 1000,
    balance: 2500,
  },
  render: args => (
    <div className="bg-white p-6">
      <OpenDisputePopupBody {...args} />
    </div>
  ),
};

export const LowBalance: Story = {
  args: {
    minDeposit: 10,
    maxDeposit: 1000,
    balance: 50,
  },
  render: args => (
    <div className="bg-white p-6">
      <OpenDisputePopupBody {...args} />
    </div>
  ),
};

export const HighMinimumDeposit: Story = {
  args: {
    minDeposit: 100,
    maxDeposit: 1000,
    balance: 2500,
  },
  render: args => (
    <div className="bg-white p-6">
      <OpenDisputePopupBody {...args} />
    </div>
  ),
};
