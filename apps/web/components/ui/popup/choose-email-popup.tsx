import { Label } from '@repo/ui/components/label';
import { BaseInput } from '../base.input';
import { InfoButton } from '../base.button';

export default function ChooseEmailPopup() {
  return (
    <>
      <div className="px-space-20 py-space-30 space-y-[40px]">
        <header className="gap-space-30 flex flex-col items-center justify-center">
          <h1 className="text-size-base font-bold">What's your email?</h1>
          <p className="text-size-xs text-gray-3">
            Add your email to receive market and trading notifications
          </p>
        </header>

        <div className="space-y-space-30">
          <div>
            <Label className="text-size-xs mb-space-10 font-semibold">User E-mail</Label>
            <BaseInput placeholder="<EMAIL>" className="mb-space-15" />
            <p className="text-size-xxs text-gray-3 font-medium">
              We'll send important notifications to this email
            </p>
          </div>
          <div className="flex justify-end">
            <button className="text-size-xs text-no-red font-medium underline">
              Do this later
            </button>
          </div>
        </div>
      </div>
      <InfoButton className="w-full" rounded="none">
        Continue
      </InfoButton>
    </>
  );
}
