import { DarkButton, InfoButton } from '@/components/ui/base.button';
import { ICON_PATH } from '@/lib/constants';
import Image from 'next/image';

interface InsufficientDepositBalancePopupProps {
  onConfirm: () => void;
  onCancel: () => void;
}

export default function InsufficientDepositBalancePopup({
  onConfirm,
  onCancel,
}: InsufficientDepositBalancePopupProps) {
  return (
    <div className="bg-gray-2 w-full">
      {/* Header */}
      <div className="py-space-50 gap-space-40 flex flex-col items-center justify-center text-center">
        <Image src={ICON_PATH.ALERT_RED_BIG} alt="warning" width={50} height={50} />
        <div className="gap-space-20 flex flex-col">
          <h1 className="text-size-sm font-semibold">Insufficient Deposit Balance</h1>
          <p className="text-size-xs text-gray-3">
            To create a prediction,
            <br />
            you must have at least $50 in available deposit.
          </p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex">
        <DarkButton onClick={onCancel} size="lg" fontSize="sm" rounded="none" className="flex-1">
          Cancel
        </DarkButton>
        <InfoButton onClick={onConfirm} size="lg" fontSize="sm" rounded="none" className="flex-1">
          Add Deposit
        </InfoButton>
      </div>
    </div>
  );
}
