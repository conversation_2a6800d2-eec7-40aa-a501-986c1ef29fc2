import SvgIcon from '@/components/icons/svg-icon';
import { cn } from '@repo/ui/lib/utils';
import React from 'react';

interface DepositPopupProps {
  cryptoAddress?: string;
  onClose?: () => void;
  onCopy?: () => void;
  onShowQR?: () => void;
  onBuyCrypto?: () => void;
}

export default function DepositPopup({
  cryptoAddress = '0×334ad74A28231572BCd988F8Ab9e2Bf8055F5D5E',
  onCopy,
  onShowQR,
  onBuyCrypto,
}: DepositPopupProps) {
  return (
    <div className={cn('w-full')}>
      {/* 헤더 */}
      <div className="mb-space-30 text-center">
        <h1 className="text-size-base font-bold">Deposit USDC</h1>
      </div>

      {/* 메인 컨텐츠 */}
      <div className="gap-space-30 flex flex-col">
        {/* 주소 입력 필드 */}
        <div className="gap-space-10 flex flex-col">
          <div>
            <p className="text-size-xs font-semibold">
              Only deposit <span className="text-sky">Base USDC</span> to this address.
            </p>
          </div>
          <div className="border-line rounded-round-sm px-space-12 flex h-(--input-height-md) items-center gap-[14px] border bg-white">
            <span className="text-size-xxs text-gray-3 w-full">{cryptoAddress}</span>
            <div className="gap-space-6 flex items-center">
              <button
                onClick={onShowQR}
                className="hover:bg-gray-2 transition-colors"
                aria-label="Show QR code"
              >
                <SvgIcon name="QrcodeIcon" />
              </button>
              <button
                onClick={onCopy}
                className="hover:bg-gray-2 transition-colors"
                aria-label="Copy address"
              >
                <SvgIcon name="CopyIcon" />
              </button>
            </div>
          </div>
        </div>

        {/* Buy Crypto 섹션 */}
        <div onClick={onBuyCrypto} className="gap-space-15 flex items-center">
          <div className="flex h-10 w-10 items-center justify-center">
            <svg
              width="28"
              height="28"
              viewBox="0 0 28 28"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M4.66669 9.33317H22.1667C22.9031 9.33317 23.5 9.93008 23.5 10.6665V21.0332C23.5 21.7696 22.9031 22.3665 22.1667 22.3665H5.83336C5.09691 22.3665 4.5 21.7696 4.5 21.0332V9.33317H4.66669Z"
                stroke="#3B424B"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M23.3333 14H4.66667"
                stroke="#3B424B"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M8.16669 18.6668H11.6667"
                stroke="#3B424B"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
              <path
                d="M7 5.6665L14 5.6665"
                stroke="#3B424B"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <div>
            <h3 className="text-size-sm font-bold">Buy Crypto</h3>
            <p className="text-size-xs text-gray-3">Easy with card or bank account</p>
          </div>
        </div>
      </div>
    </div>
  );
}
