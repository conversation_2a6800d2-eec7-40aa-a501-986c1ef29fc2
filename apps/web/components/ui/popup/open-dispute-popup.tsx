import SvgIcon from '@/components/icons/svg-icon';
import { InfoButton, NeutralButton } from '@/components/ui/base.button';
import { BaseInput } from '@/components/ui/base.input';
import { BaseTextarea } from '@/components/ui/base.textarea';
import { useMyUSDCBalance } from '@/hooks/use-usdc-balance';
import { zodResolver } from '@hookform/resolvers/zod';
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from '@repo/ui/components/form';
import { pxToRem } from '@repo/ui/lib/utils';
import { useState } from 'react';
import { useForm } from 'react-hook-form';
import { z } from 'zod';

const DisputeFormSchema = z.object({
  deposit: z.number().min(1, 'Deposit amount is required'),
  description: z.string().optional(),
  referenceUrl: z.string().optional(),
});

type DisputeFormValues = z.infer<typeof DisputeFormSchema>;

interface FileUploadProps {
  onFileSelect: (files: File[]) => void;
  multiple?: boolean;
  accept?: string;
  disabled?: boolean;
}

function FileUpload({
  onFileSelect,
  multiple = true,
  accept = 'image/*,.pdf,.doc,.docx',
  disabled = false,
}: FileUploadProps) {
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      const fileArray = Array.from(e.target.files);
      setSelectedFiles(prev => [...prev, ...fileArray]);
      onFileSelect(fileArray);
    }
  };

  const handleRemoveFile = (index: number) => {
    setSelectedFiles(prev => {
      const newFiles = [...prev];
      newFiles.splice(index, 1);
      return newFiles;
    });
  };

  return (
    <div>
      <div className="">
        <label className="gap-space-10 flex cursor-pointer items-center">
          <NeutralButton
            size="xs"
            fontSize="xs"
            type="button"
            className="px-space-15 font-semibold"
            onClick={() => document.getElementById('file-upload')?.click()}
            disabled={disabled}
          >
            Browse Files
          </NeutralButton>
          <input
            type="file"
            id="file-upload"
            className="hidden"
            onChange={handleFileChange}
            multiple={multiple}
            accept={accept}
            disabled={disabled}
          />
        </label>

        {selectedFiles.length > 0 && (
          <div className="gap-space-5 flex flex-col">
            {selectedFiles.map((file, index) => (
              <div
                key={index}
                className="gap-space-10 p-space-10 flex items-center justify-between bg-white"
              >
                <div className="text-size-xs truncate">{file.name}</div>
                <button
                  onClick={() => handleRemoveFile(index)}
                  className="text-gray-3 hover:text-no-red"
                  disabled={disabled}
                >
                  <SvgIcon name="XIcon" className="size-4" />
                </button>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

interface OpenDisputePopupProps {
  disputeAmount: string;
  isPending?: boolean;
  onSubmit?: (data: {
    amount: number;
    description?: string;
    referenceUrl?: string;
    files?: File[];
  }) => void;
  onClose?: () => void;
}

export default function OpenDisputePopupBody({
  disputeAmount,
  isPending = false,
  onSubmit,
  onClose,
}: OpenDisputePopupProps) {
  const [files, setFiles] = useState<File[]>([]);
  const { balance } = useMyUSDCBalance();

  const form = useForm<DisputeFormValues>({
    resolver: zodResolver(DisputeFormSchema),
    defaultValues: {
      deposit: 1,
      description: '',
      referenceUrl: '',
    },
  });

  const { watch, setValue } = form;
  const deposit = watch('deposit');

  const handleDepositChange = (value: number[]) => {
    if (value && value.length > 0) {
      setValue('deposit', value[0] as number);
    }
  };

  const handleAddAmount = (amount: number) => {
    setValue('deposit', Math.min(deposit + amount, 10));
  };

  const handleSubtractAmount = (amount: number) => {
    setValue('deposit', Math.max(deposit - amount, 1));
  };

  const handleFileSelect = (newFiles: File[]) => {
    setFiles(prev => [...prev, ...newFiles]);
  };

  const onFormSubmit = (values: DisputeFormValues) => {
    if (onSubmit) {
      onSubmit({
        amount: values.deposit,
        description: values.description,
        referenceUrl: values.referenceUrl,
        files,
      });
    }
  };

  return (
    <>
      <div className={'bg-gray-1 pt-space-40 w-full'}>
        {/* Header */}
        <div className="mb-space-30 text-center">
          <h1 className="text-size-base font-bold">Open a Dispute</h1>
          <p className="text-size-xs text-gray-3 mt-space-10">
            If a dispute regarding the results is upheld,
            <br />
            PredictGo will conduct a final review and confirm the results.
          </p>
        </div>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onFormSubmit)}>
            {/* Deposit */}
            <div className="gap-space-30 px-space-25 flex flex-col">
              <FormField
                control={form.control}
                name="deposit"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-size-xs font-semibold">
                      Deposit
                      <span className="text-no-red text-size-xs">(Required)</span>
                    </FormLabel>
                    <div className="gap-space-15 flex flex-col">
                      <div className="flex items-center justify-between">
                        <div className="text-size-xs text-gray-3">Amount</div>
                        <div className="text-size-xs text-gray-3">My Balance ${balance}</div>
                      </div>

                      <div className="gap-space-10 h-space-40 flex">
                        <div className="border-line px-space-12 flex flex-1 items-center justify-between bg-white">
                          <span className="text-size-xxs text-icon-gray">
                            Minimum - ${disputeAmount}
                          </span>
                          <span className="text-size-base text-mid-dark font-bold">
                            ${field.value * parseInt(disputeAmount)}
                          </span>
                        </div>
                        <div className="flex w-[30px] flex-col">
                          <NeutralButton
                            className="flex-1 font-bold"
                            fontSize="xxs10"
                            rounded="sm"
                            size="xxs"
                            type="button"
                            onClick={() => handleAddAmount(1)}
                            disabled={isPending}
                          >
                            +
                          </NeutralButton>
                          <NeutralButton
                            className="flex-1 font-bold"
                            fontSize="xxs10"
                            rounded="sm"
                            size="xxs"
                            type="button"
                            onClick={() => handleSubtractAmount(1)}
                            disabled={isPending}
                          >
                            -
                          </NeutralButton>
                        </div>
                      </div>
                    </div>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Additional Description */}
              <FormField
                control={form.control}
                name="description"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-size-xs font-semibold">
                      Additional Description <span className="text-yes-green">(Optional)</span>
                    </FormLabel>
                    <FormControl>
                      <BaseTextarea
                        placeholder="Please write any additional content you would like to explain. (max. 1,000 characters)"
                        {...field}
                        disabled={isPending}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Reference URL */}
              <FormField
                control={form.control}
                name="referenceUrl"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-size-xs font-semibold">
                      Reference URL
                      <span className="text-yes-green">(Optional)</span>
                    </FormLabel>
                    <FormControl>
                      <BaseInput
                        placeholder="Please enter a URL related to the prediction."
                        {...field}
                        disabled={isPending}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Upload Files */}
              <div className="mb-space-40">
                <h2 className="text-size-xs mb-space-15 font-semibold">
                  Upload Files &nbsp;
                  <span className="text-yes-green">(Optional)</span>
                </h2>
                <div className="text-size-xs border-line bg-gray-2 text-gray-3 py-space-20 flex flex-col items-center border font-semibold">
                  <p className="mb-space-10">Choose a file or drag and drop it here</p>
                  <p className="mb-space-20">JPG, PNG and MP4 formats, up to 50MB</p>
                  <FileUpload onFileSelect={handleFileSelect} disabled={isPending} />
                </div>
                <div className="gap-space-5 mt-space-10 flex items-start">
                  <svg
                    width="14"
                    height="14"
                    viewBox="0 0 14 14"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                  >
                    <path
                      d="M7 0C3.13404 0 0 3.1339 0 7C0 10.8661 3.13404 14 7 14C10.866 14 14 10.8661 14 7C14 3.1339 10.866 0 7 0ZM7 3.26667C7.38661 3.26667 7.7 3.58027 7.7 3.96667C7.7 4.35307 7.38661 4.66667 7 4.66667C6.61339 4.66667 6.3 4.35307 6.3 3.96667C6.3 3.58027 6.61339 3.26667 7 3.26667ZM7 5.71667C7.38661 5.71667 7.7 6.03027 7.7 6.41667V9.91667C7.7 10.3031 7.38661 10.6167 7 10.6167C6.61339 10.6167 6.3 10.3031 6.3 9.91667V6.41667C6.3 6.03027 6.61339 5.71667 7 5.71667Z"
                      fill="#8DC016"
                    />
                  </svg>

                  <p className="text-size-xxs text-yes-green">
                    The submitted information is for reference only and may not affect the final
                    outcome.
                  </p>
                </div>
              </div>
            </div>
            <div className="flex">
              <InfoButton
                style={{
                  height: pxToRem(46),
                }}
                rounded="none"
                className="flex-1"
                disabled={isPending}
                type="submit"
              >
                {isPending ? 'Opening Dispute...' : 'Open Dispute'}
              </InfoButton>
            </div>
          </form>
        </Form>
      </div>
    </>
  );
}
