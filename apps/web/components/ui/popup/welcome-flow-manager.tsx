'use client';

import { useEffect } from 'react';
import { useCurrentUser } from '@/hooks/query/user';
import { usePopupStore } from './popup.state';
import WelcomeFlowPopup from './welcome-flow-popup';
import { createUserAndRefetch } from '@/hooks/query/user/use-user-query';
import { useGlobalStore } from '@/store/global.store';
import { queryClient } from '@/components/providers/tanstack-query-client-provider';
import { userKeys } from '@/hooks/query/query-keys';

const FORCE_SHOW_WELCOME_POPUP = (() => {
  if (typeof window !== 'undefined') {
    const urlParams = new URLSearchParams(window.location.search);
    return urlParams.get('showWelcome') === 'true';
  }
  return false;
})();

export function WelcomeFlowManager() {
  const { data: user, isLoading, error, isSignedIn } = useCurrentUser();
  const { safeSmartAccount } = useGlobalStore();
  const { openPopup, closePopup, isOpen } = usePopupStore();

  useEffect(() => {
    // 테스트 모드일 때는 조건 무시하고 바로 팝업 표시
    if (FORCE_SHOW_WELCOME_POPUP) {
      if (!isOpen) {
        openPopup(
          <WelcomeFlowPopup
            onClose={async () => {
              try {
                // 환영 플로우 완료 후 사용자 생성
                if (safeSmartAccount?.address) {
                  await createUserAndRefetch(safeSmartAccount.address);

                  // 쿼리 캐시 무효화하여 사용자 정보 다시 로드
                  queryClient.invalidateQueries({
                    queryKey: userKeys.user(safeSmartAccount.address),
                  });
                }
              } catch (error) {
                console.error('사용자 생성 중 오류 발생:', error);
              } finally {
                closePopup();
              }
            }}
          />
        );
      }
      return;
    }

    // 원래 로직: 로그인 상태이고 로딩이 끝났을 때만 체크
    if (!isSignedIn || isLoading) return;

    // 이미 팝업이 열려있으면 중복 실행 방지
    if (isOpen) return;

    // 404 에러가 발생한 경우 (사용자가 등록되지 않음)
    if (error && error.message.includes('404')) {
      openPopup(
        <WelcomeFlowPopup
          onClose={async () => {
            try {
              // 환영 플로우 완료 후 사용자 생성
              if (safeSmartAccount?.address) {
                await createUserAndRefetch(safeSmartAccount.address);

                // 쿼리 캐시 무효화하여 사용자 정보 다시 로드
                queryClient.invalidateQueries({
                  queryKey: userKeys.user(safeSmartAccount.address),
                });
              }
            } catch (error) {
              console.error('사용자 생성 중 오류 발생:', error);
            } finally {
              closePopup();
            }
          }}
        />
      );
    }
  }, [isSignedIn, isLoading, error, user, openPopup, closePopup, isOpen, safeSmartAccount]);

  return null; // 렌더링할 UI 없음
}
