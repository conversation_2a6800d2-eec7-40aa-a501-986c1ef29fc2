import { DarkButton, InfoButton } from '@/components/ui/base.button';

interface ChannelUnsubscribePopupProps {
  channelName: string;
  onConfirm: () => void;
  onCancel: () => void;
}

export default function ChannelUnsubscribePopup({
  channelName,
  onConfirm,
  onCancel,
}: ChannelUnsubscribePopupProps) {
  return (
    <div className="bg-gray-2 w-full">
      {/* Header */}
      <div className="py-space-50 gap-space-30 flex flex-col items-center justify-center text-center">
        {/* Check Icon */}
        <div className="bg-icon-dark flex h-[50px] w-[50px] items-center justify-center rounded-full">
          <svg
            width="30"
            height="30"
            viewBox="0 0 30 30"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M10.935 21.435L5.55 16.05C5.215 15.715 5.215 15.18 5.55 14.845C5.885 14.51 6.42 14.51 6.755 14.845L12 20.085L22.32 9.765C22.655 9.43 23.19 9.43 23.525 9.765C23.86 10.1 23.86 10.635 23.525 10.97L13.05 21.435C12.72 21.765 12.27 21.765 10.935 21.435Z"
              fill="white"
            />
          </svg>
        </div>

        {/* Text Content */}
        <div className="gap-space-20 flex flex-col">
          <h1 className="text-size-sm text-mid-dark font-semibold">Confirm Unsubscribe</h1>
          <p className="text-size-xs text-gray-3">
            Would you like to unsubscribe from "{channelName}"?
          </p>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex">
        <DarkButton onClick={onCancel} size="lg" fontSize="xs" rounded="none" className="flex-1">
          Cancel
        </DarkButton>
        <InfoButton onClick={onConfirm} size="lg" fontSize="xs" rounded="none" className="flex-1">
          Unsubscribe
        </InfoButton>
      </div>
    </div>
  );
}
