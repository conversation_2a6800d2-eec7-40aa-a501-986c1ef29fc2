// BasePopupProvider
//

import SvgIcon from '@/components/icons/svg-icon';

// openPopup({})

// const {openPopup, closePopup} = usePopup()

// TODO: design openPopup props

interface OpenPopupProps {
  children?: React.ReactNode;
  buttons?: {} | [{}, {}];
}

const EnableWallet = {
  children: (
    <div className="flex flex-col items-center">
      <div>
        <SvgIcon name="WalletIcon" />
      </div>
      <div>Enable Wallet</div>
      <div></div>
    </div>
  ),
  buttons: {
    text: 'Enable Wallet',
    onClick: () => {},
    disabled: false,
  },
};

const CheckYourEmail = {
  children: <></>,
  buttons: {
    text: 'Check Your Email',
    onClick: () => {},
    disabled: false,
  },
};



// openPopup(<>, {})
