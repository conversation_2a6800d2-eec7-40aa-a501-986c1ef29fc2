import { DarkButton } from '@/components/ui/base.button';
import React from 'react';

interface FundYourAccountPopupProps {
  onDepositFunds: () => void;
  onSkipForNow: () => void;
}

export default function FundYourAccountPopup({
  onDepositFunds,
  onSkipForNow,
}: FundYourAccountPopupProps) {
  return (
    <div className="w-full bg-white">
      {/* Header */}
      <div className="py-space-50 gap-space-40 flex flex-col items-center justify-center text-center">
        {/* Dollar Icon */}
        <div className="bg-sky flex h-16 w-16 items-center justify-center rounded-full">
          <svg
            width="32"
            height="32"
            viewBox="0 0 32 32"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M16 2.66666C8.63621 2.66666 2.66666 8.63621 2.66666 16C2.66666 23.3638 8.63621 29.3333 16 29.3333C23.3638 29.3333 29.3333 23.3638 29.3333 16C29.3333 8.63621 23.3638 2.66666 16 2.66666Z"
              stroke="white"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M16 8V24"
              stroke="white"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            <path
              d="M20 12H14C13.4696 12 12.9609 12.2107 12.5858 12.5858C12.2107 12.9609 12 13.4696 12 14C12 14.5304 12.2107 15.0391 12.5858 15.4142C12.9609 15.7893 13.4696 16 14 16H18C18.5304 16 19.0391 16.2107 19.4142 16.5858C19.7893 16.9609 20 17.4696 20 18C20 18.5304 19.7893 19.0391 19.4142 19.4142C19.0391 19.7893 18.5304 20 18 20H12"
              stroke="white"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>

        <div className="gap-space-20 flex flex-col">
          <h1 className="text-size-base font-bold">Fund Your Account</h1>
          <p className="text-size-sm text-gray-3">Create deposit address</p>
        </div>
      </div>

      {/* Skip for now link */}
      <div className="mb-space-30 flex justify-center">
        <button
          onClick={onSkipForNow}
          className="text-size-xs text-sky hover:text-sky-dark transition-colors"
        >
          Skip for now
        </button>
      </div>

      {/* Action Button */}
      <div className="flex">
        <DarkButton
          onClick={onDepositFunds}
          size="lg"
          fontSize="sm"
          rounded="none"
          className="bg-sky hover:bg-sky-dark flex-1 text-white"
        >
          Deposit Funds
        </DarkButton>
      </div>
    </div>
  );
}
