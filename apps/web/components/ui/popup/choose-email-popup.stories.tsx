import type { Meta, StoryObj } from '@storybook/react';
import ChooseEmailPopup from './choose-email-popup';

const meta: Meta<typeof ChooseEmailPopup> = {
  title: 'UI/Popup/ChooseEmailPopup',
  component: ChooseEmailPopup,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;
type Story = StoryObj<typeof ChooseEmailPopup>;

export const Default: Story = {
  args: {},
  render: () => (
    <div className="max-w-md rounded-lg bg-white p-6 shadow-lg">
      <ChooseEmailPopup />
    </div>
  ),
  parameters: {
    docs: {
      description: {
        story: 'Email popup rendered with a container for better visibility',
      },
    },
  },
};
