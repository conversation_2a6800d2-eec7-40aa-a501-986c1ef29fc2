import * as React from 'react';

import {
  Dialog,
  DialogTrigger,
  DialogContent,
  DialogClose,
  DialogPortal,
  DialogOverlay,
  DialogHeader,
  DialogTitle,
  DialogDescription,
  DialogFooter,
} from '@repo/ui/components/dialog';

import { cn } from '@repo/ui/lib/utils';

function BaseDialog({ ...props }: React.ComponentProps<typeof Dialog>) {
  return <Dialog {...props} />;
}

function BaseDialogTrigger({ ...props }: React.ComponentProps<typeof DialogTrigger>) {
  return <DialogTrigger {...props} />;
}

function BaseDialogPortal({ ...props }: React.ComponentProps<typeof DialogPortal>) {
  return <DialogPortal {...props} />;
}

function BaseDialogClose({ ...props }: React.ComponentProps<typeof DialogClose>) {
  return <DialogClose {...props} />;
}

function BaseDialogOverlay({ ...props }: React.ComponentProps<typeof DialogOverlay>) {
  return <DialogOverlay {...props} />;
}

function BaseDialogContent({
  className,
  smallCloseBtn = false,
  ...props
}: React.ComponentProps<typeof DialogContent> & {
  smallCloseBtn?: boolean;
}) {
  return (
    <DialogContent
      className={cn(
        'max-w-[425px] border-none p-0 sm:rounded-none md:rounded-none',
        smallCloseBtn || '[&>button:last-child]:hidden',
        className
      )}
      {...props}
    />
  );
}

function BaseDialogHeader({ ...props }: React.ComponentProps<typeof DialogHeader>) {
  return <DialogHeader {...props} />;
}

function BaseDialogTitle({ className, ...props }: React.ComponentProps<typeof DialogTitle>) {
  return (
    <DialogTitle
      className={cn(
        'self-stretch text-center text-[length:var(--font-size-sm,14px)] leading-[var(--spacing-20,20px)] font-[number:var(--font-weight-semibold,600)] text-[color:var(--color-mid-dark,#23252B)]',
        className
      )}
      {...props}
    />
  );
}

function BaseDialogBody({ className, ...props }: React.ComponentProps<'div'>) {
  return (
    <div
      data-slot="dialog-body"
      className={cn(
        'flex flex-col items-center gap-[30px] self-stretch px-[25px] py-[30px]',
        className
      )}
      {...props}
    />
  );
}

function BaseDialogDescription({
  className,
  ...props
}: React.ComponentProps<typeof DialogDescription>) {
  return (
    <DialogDescription
      className={cn(
        'cn self-stretch text-center [font-family:Inter] text-[length:var(--font-size-xs,12px)] leading-[var(--spacing-16,16px)] font-normal text-[color:var(--color-gray-3,#8E8E93)] [leading-trim:both] [text-edge:cap]',
        className
      )}
      {...props}
    />
  );
}

function BaseDialogFooter({ className, ...props }: React.ComponentProps<typeof DialogFooter>) {
  return <DialogFooter className={cn('gap-0', className)} {...props} />;
}

export {
  BaseDialog,
  BaseDialogTrigger,
  BaseDialogContent,
  BaseDialogPortal,
  BaseDialogClose,
  BaseDialogOverlay,
  BaseDialogHeader,
  BaseDialogTitle,
  BaseDialogBody,
  BaseDialogDescription,
  BaseDialogFooter,
};
