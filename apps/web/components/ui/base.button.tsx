import { TEXT_SIZE_CSS_VARS } from '@/lib/constants';
import { Button } from '@repo/ui/components/button';
import { cn, cva, VariantProps } from '@repo/ui/lib/utils';
import type { ComponentProps } from 'react';

const customVariant = {
  yes: 'bg-yes-green/25 hover:bg-yes-green text-yes-green hover:text-white font-medium',
  no: 'bg-no-red/25 hover:bg-no-red text-no-red hover:text-white font-medium',
  dark: 'bg-dark hover:bg-dark/90 text-white font-medium',
  neutral: 'bg-gray-2 text-dark font-medium border border-line',
  info: 'bg-sky hover:bg-sky/90 font-medium text-white',
};

export type ButtonCustomVariant = keyof typeof customVariant;

const customSize = {
  xxs: 'h-(--button-height-xxs)', // 18px
  xxs2: 'h-(--button-height-xxs-2)', // 20px
  xs: 'h-(--button-height-xs)', // 24px
  sm30: 'h-(--button-height-sm30)', // 30px
  sm: 'h-(--button-height-sm-1)', // 34px
  sm2: 'h-(--button-height-sm-2)', // 36px
  md: 'h-(--button-height-md)', // 40px
  lg: 'h-(--button-height-lg)', // 46px
  xl1: 'h-(--button-height-xl-1)', // 54px
  xl2: 'h-(--button-height-xl-2)', // 56px
};

const roundedSizeClasses = {
  none: 'rounded-none',
  sm: 'rounded-round-sm',
  md: 'rounded-round-md',
  lg: 'rounded-round-lg',
};

export type ButtonCustomSize = keyof typeof customSize;
type BaseButtonProps = Omit<ComponentProps<typeof Button>, 'variant' | 'size'> & {
  loading?: boolean;
  variant?: ButtonCustomVariant | VariantProps<typeof Button>['variant'];
  size?: VariantProps<typeof Button>['size'] | ButtonCustomSize;
  width?: string;
  fontSize?: keyof typeof TEXT_SIZE_CSS_VARS;
  rounded?: keyof typeof roundedSizeClasses;
};

const extendedButtonVariants = cva('cursor-pointer', {
  variants: {
    variant: customVariant,
    size: customSize,
    rounded: roundedSizeClasses,
  },
});

export function BaseButton({
  className,
  variant,
  size,
  loading = false,
  width,
  fontSize,
  rounded,
  ...props
}: BaseButtonProps) {
  const overrideClassName = cn(
    extendedButtonVariants({
      variant: variant as ButtonCustomVariant,
      size: size as ButtonCustomSize,
      rounded: rounded as keyof typeof roundedSizeClasses,
    }),
    className
  );

  const getStyles = () => {
    const merged: React.CSSProperties = {};
    if (width) {
      merged['width'] = width;
    }
    if (fontSize) {
      merged['fontSize'] = TEXT_SIZE_CSS_VARS[fontSize];
    }
    return merged;
  };

  return (
    <Button
      style={getStyles()}
      className={overrideClassName}
      variant={variant as VariantProps<typeof Button>['variant']}
      size={size as VariantProps<typeof Button>['size']}
      disabled={loading || props.disabled}
      {...props}
    />
  );
}

export const InfoButton = (props: BaseButtonProps) => <BaseButton variant="info" {...props} />;
export const GreenButton = (props: BaseButtonProps) => <BaseButton variant="yes" {...props} />;
export const RedButton = (props: BaseButtonProps) => <BaseButton variant="no" {...props} />;
export const NeutralButton = (props: BaseButtonProps) => (
  <BaseButton variant="neutral" {...props} />
);
export const DarkButton = (props: BaseButtonProps) => <BaseButton variant="dark" {...props} />;

export const GhostButton = (props: BaseButtonProps) => <BaseButton variant="ghost" {...props} />;
