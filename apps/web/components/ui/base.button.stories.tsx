import type { Meta, StoryObj } from '@storybook/react';
import { BaseButton } from './base.button';

const meta: Meta<typeof BaseButton> = {
  title: 'Components/ui/BaseButton',
  component: BaseButton,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    variant: {
      options: [
        'default',
        'destructive',
        'outline',
        'secondary',
        'ghost',
        'link',
        'yes',
        'no',
        'info',
        'dark',
        'neutral',
        'sky',
      ],
      control: { type: 'select' },
    },
    size: {
      options: ['xxs', 'xs', 'sm', 'sm2', 'md', 'lg', 'xl1', 'xl2'],
      control: { type: 'select' },
    },
    loading: {
      control: { type: 'boolean' },
    },
    disabled: {
      control: { type: 'boolean' },
    },
    children: {
      control: { type: 'text' },
    },
  },
};

export default meta;

type Story = StoryObj<typeof BaseButton>;

export const Default: Story = {
  args: {
    children: '버튼',
    variant: 'default',
    size: 'default',
  },
};

export const Destructive: Story = {
  args: {
    children: '삭제',
    variant: 'destructive',
  },
};

export const Outline: Story = {
  args: {
    children: '아웃라인',
    variant: 'outline',
  },
};

export const Secondary: Story = {
  args: {
    children: '보조',
    variant: 'secondary',
  },
};

export const Ghost: Story = {
  args: {
    children: '고스트',
    variant: 'ghost',
  },
};

export const Link: Story = {
  args: {
    children: '링크',
    variant: 'link',
  },
};

export const Small: Story = {
  args: {
    children: '작은 버튼',
    size: 'sm',
  },
};

export const Large: Story = {
  args: {
    children: '큰 버튼',
    size: 'lg',
  },
};

export const Icon: Story = {
  args: {
    children: '🔍',
    size: 'icon',
  },
};

export const Loading: Story = {
  args: {
    children: '로딩 중',
    loading: true,
  },
};

export const Disabled: Story = {
  args: {
    children: '비활성화',
    disabled: true,
  },
};

export const Yes: Story = {
  args: {
    children: '예',
    variant: 'yes',
  },
};

export const No: Story = {
  args: {
    children: '아니오',
    variant: 'no',
  },
};

export const AllVariants: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <div className="flex flex-wrap gap-4">
        <BaseButton variant="default">기본</BaseButton>
        <BaseButton variant="destructive">삭제</BaseButton>
        <BaseButton variant="outline">아웃라인</BaseButton>
        <BaseButton variant="secondary">보조</BaseButton>
        <BaseButton variant="ghost">고스트</BaseButton>
        <BaseButton variant="link">링크</BaseButton>
      </div>
      <div className="flex flex-wrap gap-4">
        <BaseButton variant="yes">예</BaseButton>
        <BaseButton variant="no">아니오</BaseButton>
      </div>
      <div className="flex flex-wrap gap-4">
        <BaseButton size="sm">작은 버튼</BaseButton>
        <BaseButton size="default">기본 버튼</BaseButton>
        <BaseButton size="lg">큰 버튼</BaseButton>
        <BaseButton size="icon">🔍</BaseButton>
      </div>
      <div className="flex flex-wrap gap-4">
        <BaseButton loading>로딩 중</BaseButton>
        <BaseButton disabled>비활성화</BaseButton>
      </div>
    </div>
  ),
};
