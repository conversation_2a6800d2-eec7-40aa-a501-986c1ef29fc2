import * as React from 'react';
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectScrollDownButton,
  SelectScrollUpButton,
  SelectSeparator,
  SelectTrigger,
  SelectValue,
} from '@repo/ui/components/select';
import { cn } from '@repo/ui/lib/utils';

function BaseSelect({ ...props }: React.ComponentProps<typeof Select>) {
  return <Select {...props} />;
}

function BaseSelectGroup({ ...props }: React.ComponentProps<typeof SelectGroup>) {
  return <SelectGroup {...props} />;
}

function BaseSelectValue({ className, ...props }: React.ComponentProps<typeof SelectValue>) {
  return <SelectValue className={cn('data-[slot=select-value]', className)} {...props} />;
}

function BaseSelectTrigger({
  className,
  size = 'default',
  children,
  ...props
}: React.ComponentProps<typeof SelectTrigger> & {
  size?: 'sm' | 'default';
}) {
  return (
    <SelectTrigger data-size={size} className={cn('rounded-none', className)} {...props}>
      {children}
    </SelectTrigger>
  );
}

function BaseSelectContent({
  className,
  children,
  position = 'popper',
  ...props
}: React.ComponentProps<typeof SelectContent>) {
  return (
    <SelectContent
      className={cn(
        'bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-none border shadow-md',
        position === 'popper' &&
          'data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1',
        'data-[slot=select-content]',
        className
      )}
      position={position}
      {...props}
    >
      {children}
    </SelectContent>
  );
}

function BaseSelectLabel({ className, ...props }: React.ComponentProps<typeof SelectLabel>) {
  return (
    <SelectLabel
      className={cn(
        'text-muted-foreground data-[slot=select-label] px-2 py-1.5 text-xs',
        className
      )}
      {...props}
    />
  );
}

function BaseSelectItem({
  className,
  children,
  ...props
}: React.ComponentProps<typeof SelectItem>) {
  return (
    <SelectItem
      className={cn(
        "focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-none py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2",
        'data-[slot=select-item]',
        className
      )}
      {...props}
    >
      {children}
    </SelectItem>
  );
}

function BaseSelectSeparator({
  className,
  ...props
}: React.ComponentProps<typeof SelectSeparator>) {
  return (
    <SelectSeparator
      className={cn(
        'bg-border data-[slot=select-separator] pointer-events-none -mx-1 my-1 h-px',
        className
      )}
      {...props}
    />
  );
}

function BaseSelectScrollUpButton({
  className,
  ...props
}: React.ComponentProps<typeof SelectScrollUpButton>) {
  return (
    <SelectScrollUpButton
      className={cn(
        'data-[slot=select-scroll-up-button] flex cursor-default items-center justify-center py-1',
        className
      )}
      {...props}
    />
  );
}

function BaseSelectScrollDownButton({
  className,
  ...props
}: React.ComponentProps<typeof SelectScrollDownButton>) {
  return (
    <SelectScrollDownButton
      className={cn(
        'data-[slot=select-scroll-down-button] flex cursor-default items-center justify-center py-1',
        className
      )}
      {...props}
    />
  );
}

export {
  BaseSelect,
  BaseSelectContent,
  BaseSelectGroup,
  BaseSelectItem,
  BaseSelectLabel,
  BaseSelectScrollDownButton,
  BaseSelectScrollUpButton,
  BaseSelectSeparator,
  BaseSelectTrigger,
  BaseSelectValue,
};
