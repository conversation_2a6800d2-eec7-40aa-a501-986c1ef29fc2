import * as React from 'react';
import { cn, cva, type VariantProps } from '@repo/ui/lib/utils';
import { Loader2 } from 'lucide-react';
const PopButtonVariants = cva(
  "h-[46px] inline-flex flex-1 items-center justify-center gap-2 whitespace-nowrap text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",
  {
    variants: {
      variant: {
        default: `bg-[var(--color-point-3)] text-white text-md hover:underline underline-offset-2`,
        red: `bg-no-red text-white text-md hover:underline underline-offset-2`,
      },
    },
    defaultVariants: {
      variant: 'default',
    },
  }
);

type ButtonProps = React.ComponentProps<'button'> &
  VariantProps<typeof PopButtonVariants> & {
    loading?: boolean;
  };
function PopButton({ className, variant, loading = false, ...props }: ButtonProps) {
  return (
    <button disabled={loading} className={cn(PopButtonVariants({ variant, className }))} {...props}>
      {loading && <Loader2 className="h-4 w-4 animate-spin" />}
      {props.children}
    </button>
  );
}

export { PopButton, PopButtonVariants };
