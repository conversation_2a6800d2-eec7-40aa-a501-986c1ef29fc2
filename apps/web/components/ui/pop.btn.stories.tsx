import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { PopButton } from './pop.btn';

const meta: Meta<typeof PopButton> = {
  title: 'Components/ui/PopButton',
  component: PopButton,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    // Define argTypes for props like variant, loading, etc. if they exist
    // Example:
    // variant: {
    //   control: { type: 'select' },
    //   options: ['default', 'red', 'outline'], // Add actual variants
    // },
    // loading: { control: 'boolean' },
  },
};

export default meta;

type Story = StoryObj<typeof PopButton>;

export const Default: Story = {
  render: args => <PopButton {...args}>Default PopButton</PopButton>,
  args: {
    // Default props here
  },
};

export const RedVariant: Story = {
  render: args => (
    <PopButton {...args} variant="red">
      Red Variant PopButton
    </PopButton>
  ),
  args: {
    variant: 'red',
  },
};

export const Loading: Story = {
  render: args => (
    <PopButton {...args} loading>
      Loading PopButton
    </PopButton>
  ),
  args: {
    loading: true,
  },
};
