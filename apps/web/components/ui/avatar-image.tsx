import { Avatar, AvatarImage } from '@repo/ui/components/avatar';
import { cn } from '@repo/ui/lib/utils';
import Link from 'next/link';

export type AvatarSize =
  | 'sm' // 24px
  | 'md' // 40px
  | 'md2' // 48px
  | 'md3' // 60px
  | 'lg'; // 80px

interface AvatarImageProps {
  imageUrl?: string;
  size?: AvatarSize;
  alt?: string;
  className?: string;
  href?: string;
  style?: React.CSSProperties;
}

/**
 * 재사용 가능한 아바타 이미지 컴포넌트
 * @param imageUrl 이미지 URL (없을 경우 기본 이미지 사용)
 * @param size 아바타 크기 (기본값: "md")
 * @param alt 이미지 대체 텍스트
 * @param className 추가 클래스명
 * @param href 링크 URL (제공된 경우 아바타를 링크로 렌더링)
 */
export default function CommonAvatar({
  imageUrl,
  size = 'md',
  alt = 'Avatar',
  className,
  href,
  style,
}: AvatarImageProps) {
  const avatarComponent = (
    <Avatar
      style={{
        width: `var(--avatar-size-${size})`,
        height: `var(--avatar-size-${size})`,
        ...style,
      }}
      className={cn(`rounded-full`, className)}
    >
      <AvatarImage className="object-cover object-center" src={imageUrl} alt={alt} />
    </Avatar>
  );

  if (href) {
    return <Link href={href}>{avatarComponent}</Link>;
  }

  return avatarComponent;
}
