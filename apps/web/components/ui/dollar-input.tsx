import { cn } from '@repo/ui/lib/utils';
import React, { useCallback, useState, useEffect } from 'react';
import type { ComponentProps } from 'react';
import { BaseInput } from './base.input';

interface DollarInputProps extends Omit<ComponentProps<typeof BaseInput>, 'value' | 'onChange'> {
  value?: number;
  onChange?: (value: number) => void;
  maxValue?: number;
  minValue?: number;
}

const formatCurrency = (value: number): string => {
  return `$${value.toLocaleString('en-US', { minimumFractionDigits: 0, maximumFractionDigits: 2 })}`;
};

const formatInputForEditing = (value: number): string => {
  if (value === 0) return '$';
  return `$${value}`;
};

export function DollarInput({
  className,
  value = 0,
  onChange,
  maxValue,
  minValue = 0,
  placeholder = '$0',
  ...props
}: DollarInputProps) {
  const [inputValue, setInputValue] = useState<string>('');
  const [isFocused, setIsFocused] = useState(false);

  // value가 변경될 때 inputValue 업데이트
  useEffect(() => {
    if (!isFocused) {
      if (value === 0) {
        setInputValue('');
      } else {
        setInputValue(formatCurrency(value));
      }
    }
  }, [value, isFocused]);

  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      let inputVal = e.target.value;

      // 달러 사인이 없으면 추가
      if (!inputVal.startsWith('$')) {
        inputVal = '$' + inputVal;
      }

      // 달러 사인 제거하고 숫자 부분만 추출
      const rawValue = inputVal.replace(/^\$/, '').replace(/,/g, '');

      // 소수점 이하 2자리까지만 허용
      const decimalMatch = rawValue.match(/^\d*\.?\d{0,2}$/);
      if (!decimalMatch && rawValue !== '') {
        return;
      }

      const numericValue = parseFloat(rawValue) || 0;

      // MAX 값 실시간 체크 - 입력 중에 max를 넘으면 입력 차단
      if (maxValue !== undefined && numericValue > maxValue) {
        // max 값으로 설정하고 입력 필드도 max 값으로 업데이트
        const maxInputValue = `$${maxValue}`;
        setInputValue(maxInputValue);
        onChange?.(maxValue);
        return;
      }

      // 입력값에 달러 사인 유지
      setInputValue(inputVal);

      // 소수점 이하 2자리로 반올림
      const roundedValue = Math.round(numericValue * 100) / 100;

      // Apply validation constraints
      let validatedValue = roundedValue;

      if (validatedValue < minValue) {
        validatedValue = minValue;
      }

      onChange?.(validatedValue);
    },
    [onChange, maxValue, minValue]
  );

  const handleFocus = useCallback(() => {
    setIsFocused(true);
    // 포커스 시에도 달러 사인 유지
    if (value === 0) {
      setInputValue('$');
    } else {
      setInputValue(formatInputForEditing(value));
    }
  }, [value]);

  const handleBlur = useCallback(() => {
    setIsFocused(false);
    // 포커스 아웃 시 포맷팅된 값으로 표시
    if (value === 0) {
      setInputValue('');
    } else {
      setInputValue(formatCurrency(value));
    }
  }, [value]);

  return (
    <BaseInput
      className={cn(className)}
      value={inputValue}
      onChange={handleInputChange}
      onFocus={handleFocus}
      onBlur={handleBlur}
      placeholder={placeholder}
      type="text"
      {...props}
    />
  );
}
