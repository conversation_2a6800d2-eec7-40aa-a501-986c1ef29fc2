import { useState, useRef, DragEvent } from 'react';
import { Avatar, AvatarImage } from '@repo/ui/components/avatar';
import { toast } from '@repo/ui/components/sonner';
import { cn } from '@repo/ui/lib/utils';

interface AvatarImageUploaderProps {
  /** 현재 이미지 URL */
  imageUrl?: string;
  /** 파일 선택 시 호출되는 콜백 */
  onFileSelect: (file: File | null) => void;
  /** 아바타 크기 (기본값: 80px) */
  size?: number;
  /** 허용되는 파일 타입 (기본값: image/jpeg,image/png) */
  acceptedTypes?: string[];
  /** 최대 파일 크기 (기본값: 5MB) */
  maxSize?: number;
  /** 비활성화 여부 */
  disabled?: boolean;
  /** 추가 CSS 클래스 */
  className?: string;
}

export function AvatarImageUploader({
  imageUrl,
  onFileSelect,
  size = 80,
  acceptedTypes = ['image/jpeg', 'image/png'],
  maxSize = 5 * 1024 * 1024, // 5MB
  disabled = false,
  className,
}: AvatarImageUploaderProps) {
  const [isDragging, setIsDragging] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): boolean => {
    // 파일 타입 검증
    if (!acceptedTypes.some(type => file.type === type)) {
      toast.error(
        `허용되는 파일 형식: ${acceptedTypes
          .join(', ')
          .replace(/image\//g, '')
          .toUpperCase()}`
      );
      return false;
    }

    // 파일 크기 검증
    if (file.size > maxSize) {
      const maxSizeMB = Math.round(maxSize / (1024 * 1024));
      toast.error(`파일 크기는 ${maxSizeMB}MB 이하여야 합니다`);
      return false;
    }

    return true;
  };

  const handleFileSelect = (file: File) => {
    if (validateFile(file)) {
      onFileSelect(file);
    }
  };

  const handleClick = () => {
    if (disabled) return;
    fileInputRef.current?.click();
  };

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const handleDragOver = (e: DragEvent<HTMLButtonElement>) => {
    e.preventDefault();
    if (!disabled) {
      setIsDragging(true);
    }
  };

  const handleDragLeave = (e: DragEvent<HTMLButtonElement>) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e: DragEvent<HTMLButtonElement>) => {
    e.preventDefault();
    setIsDragging(false);

    if (disabled) return;

    const file = e.dataTransfer.files?.[0];
    if (file) {
      handleFileSelect(file);
    }
  };

  const editIconSize = Math.max(10, size * 0.125); // 아바타 크기의 12.5%
  const overlaySize = Math.max(20, size * 0.3); // 아바타 크기의 30%

  return (
    <div className={cn('relative inline-block', className)}>
      <input
        ref={fileInputRef}
        type="file"
        accept={acceptedTypes.join(',')}
        onChange={handleFileInputChange}
        className="hidden"
        disabled={disabled}
      />

      <button
        onClick={handleClick}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        className={cn(
          'relative transition-all duration-200',
          // 'focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none',
          !disabled && 'cursor-pointer hover:scale-105',
          isDragging && 'scale-105',
          disabled && 'cursor-not-allowed opacity-50'
        )}
        style={{ width: size, height: size }}
        type="button"
        aria-label="아바타 이미지 업로드"
        disabled={disabled}
      >
        <Avatar className="h-full w-full">
          <AvatarImage src={imageUrl} className="object-cover" />
        </Avatar>

        {/* 편집 오버레이 */}
        <div
          style={{
            right: '0px',
            bottom: '0px',
            width: overlaySize,
            height: overlaySize,
          }}
          className={cn(
            'bg-sky absolute flex items-center justify-center rounded-full',
            'transition-all duration-200',
            isDragging && 'bg-blue-600'
          )}
        >
          <svg
            width={editIconSize}
            height={editIconSize}
            viewBox="0 0 10 10"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M3.86229 9.5V0.5H6.13771V9.5H3.86229ZM0.5 6.13771V3.86229H9.5V6.13771H0.5Z"
              fill="white"
            />
          </svg>
        </div>

        {/* {isDragging && (
          <div className="absolute inset-0 flex items-center justify-center rounded-full bg-blue-500/20">
            <div className="text-xs font-medium text-blue-600">Drop to upload</div>
          </div>
        )} */}
      </button>
    </div>
  );
}
