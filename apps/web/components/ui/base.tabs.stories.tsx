import type { <PERSON><PERSON>, StoryObj } from '@storybook/react';
import { BaseTabs, BaseTabsList, BaseTabsTrigger, BaseTabsContent } from './base.tabs';

const meta: Meta<typeof BaseTabs> = {
  title: 'Components/ui/BaseTabs',
  component: BaseTabs,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof BaseTabs>;

export const Default: Story = {
  render: () => (
    <div className="w-[500px]">
      <BaseTabs defaultValue="account">
        <BaseTabsList>
          <BaseTabsTrigger value="account">Account</BaseTabsTrigger>
          <BaseTabsTrigger value="password">Password</BaseTabsTrigger>
          <BaseTabsTrigger value="notifications">Notifications</BaseTabsTrigger>
        </BaseTabsList>
        <BaseTabsContent value="account">
          <div className="p-4">Make changes to your account here.</div>
        </BaseTabsContent>
        <BaseTabsContent value="password">
          <div className="p-4">Change your password here.</div>
        </BaseTabsContent>
        <BaseTabsContent value="notifications">
          <div className="p-4">Manage your notifications here.</div>
        </BaseTabsContent>
      </BaseTabs>
    </div>
  ),
};

export const WithCustomContent: Story = {
  render: () => (
    <div className="w-[600px]">
      <BaseTabs defaultValue="profile">
        <BaseTabsList>
          <BaseTabsTrigger value="profile">Profile</BaseTabsTrigger>
          <BaseTabsTrigger value="settings">Settings</BaseTabsTrigger>
          <BaseTabsTrigger value="billing">Billing</BaseTabsTrigger>
        </BaseTabsList>
        <BaseTabsContent value="profile">
          <div className="space-y-4 p-6">
            <h3 className="text-lg font-semibold">Profile Information</h3>
            <p>Update your profile information and preferences.</p>
          </div>
        </BaseTabsContent>
        <BaseTabsContent value="settings">
          <div className="space-y-4 p-6">
            <h3 className="text-lg font-semibold">Account Settings</h3>
            <p>Configure your account settings and preferences.</p>
          </div>
        </BaseTabsContent>
        <BaseTabsContent value="billing">
          <div className="space-y-4 p-6">
            <h3 className="text-lg font-semibold">Billing Information</h3>
            <p>View and manage your subscription and payment methods.</p>
          </div>
        </BaseTabsContent>
      </BaseTabs>
    </div>
  ),
};
