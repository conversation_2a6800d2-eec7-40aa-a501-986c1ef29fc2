import type { <PERSON>a, StoryObj } from '@storybook/react';
import MainPredictionBanner from './main-prediction-banner';

const meta = {
  title: 'main-prediction-banner',
  component: MainPredictionBanner,
} satisfies Meta<typeof MainPredictionBanner>;

export default meta;

type Story = StoryObj<typeof MainPredictionBanner>;

export const Primary: Story = {
  args: {
    imageUrl:
      'https://preview.redd.it/new-banner-for-the-squid-game-twitter-page-v0-86x4b695i9ge1.jpeg?auto=webp&s=de60ca2d531aa45239f081ea59becbc4dfb2934c',
  },
};
