import { cn, pxToRem } from '@repo/ui/lib/utils';

interface DashboardBoxProps {
  children: React.ReactNode;
  className?: string;
}

export default function DashboardBox({ children, className }: DashboardBoxProps) {
  return (
    <div
      style={
        {
          '--box-height': pxToRem(165),
        } as React.CSSProperties
      }
      className={cn(
        'border-line px-space-25 py-space-30 flex h-(--box-height) flex-1 flex-col justify-between border',
        className
      )}
    >
      {children}
    </div>
  );
}
