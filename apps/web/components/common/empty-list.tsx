import { cn } from '@repo/ui/lib/utils';
import Image from 'next/image';

interface EmptyListProps {
  className?: string;
}
export default function EmptyMarketList({ className }: EmptyListProps) {
  const size = 37;
  return (
    <div className={cn('gap-space-10 flex flex-col items-center justify-center', className)}>
      <Image src="/icons/empty_logo.svg" alt="Empty List" width={size} height={size} />
      <p className="text-size-base font-bold">No markets available</p>
    </div>
  );
}
