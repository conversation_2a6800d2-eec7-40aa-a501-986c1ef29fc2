# 트래킹 동의 시스템

사용자 동의를 받아 Google Analytics와 Microsoft Clarity를 초기화하는 시스템입니다.

## 구성 요소

### 1. `tracking-consent-banner.tsx`

- 하단 고정 형태의 트래킹 동의 배너
- Google Analytics와 Microsoft Clarity를 개별적으로 선택 가능
- 접기/펼치기로 세부 설정 제공
- 모든 동의, 선택 동의, 모두 거부 옵션 제공

### 2. `microsoft-clarity.tsx`

- `@microsoft/clarity` 공식 패키지를 사용하여 Clarity 초기화
- `@next/third-parties/google`와 유사한 패턴으로 구현

## 사용법

### 환경 변수 설정

```env
# Google Analytics 측정 ID (GA4 형식)
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Microsoft Clarity 프로젝트 ID
NEXT_PUBLIC_CLARITY_PROJECT_ID=your-clarity-project-id
```

### 동작 방식

1. **첫 방문**: 하단에 트래킹 동의 배너 표시
2. **동의**: 선택한 트래킹 서비스 초기화 및 상태 저장
3. **거부**: 트래킹 서비스 미초기화, 하루 후 다시 배너 표시
4. **브라우저 리프레시**: 저장된 동의 상태에 따라 트래킹 서비스 자동 초기화

### 동의 상태별 동작

- **GA 동의 + Clarity 동의**: 두 스크립트 모두 로드
- **GA만 동의**: GA 스크립트만 로드
- **Clarity만 동의**: Clarity 스크립트만 로드
- **모두 거부**: 트래킹 스크립트 로드 안 함, 24시간 후 다시 배너 표시

### UI 특징

- **하단 고정**: 페이지 하단에 고정되어 스크롤과 무관하게 표시
- **화이트 배경**: 깔끔한 화이트 배경의 카드 형태
- **반응형**: 모바일과 데스크톱에서 최적화된 레이아웃
- **접기/펼치기**: "Cookies policy" 클릭 시 세부 설정 표시/숨김

### 중복 초기화 방지

- Google Analytics: `@next/third-parties/google`의 내장 중복 방지 기능 사용
- Microsoft Clarity: `window.clarity` 객체 존재 여부로 중복 초기화 방지

## 사용된 라이브러리

- `@next/third-parties/google`: Google Analytics 최적화 로딩
- `@microsoft/clarity`: Microsoft Clarity 공식 패키지
- `@repo/ui/components/*`: shadcn/ui 기반 UI 컴포넌트
- `lucide-react`: 아이콘
- `zod`: 환경 변수 검증

## 패키지 설치

```bash
# 필요한 패키지 설치
pnpm add @next/third-parties @microsoft/clarity
```
