'use client';

import { toast } from '@/components/ui/base.toast';
import { useMarketsWithAll } from '@/hooks/query/market';
import { ApiError } from '@/lib/api/base-api.error';
import {
  GetMarketFilterWithAllEnum,
  GetMarketsRequestOptions,
} from '@/lib/api/market/market.schema.server';
import { useEffect } from 'react';
import { PredictionMarketCard, PredictionMarketCardSkeleton } from './prediction-market-card';
import EmptyMarketList from './empty-list';

interface MarketListProps extends Omit<GetMarketsRequestOptions, 'page' | 'limit'> {
  page?: number;
  limit?: number;
  filter?: GetMarketFilterWithAllEnum;
}

export function MarketList({ page = 0, limit = 50, filter, order = 'VOLUME' }: MarketListProps) {
  const { data, isLoading, error, isError } = useMarketsWithAll({
    page,
    limit,
    order,
    filter,
  });

  useEffect(() => {
    if (isError && error) {
      let errorMessage = 'Failed to load markets. Please try again.';

      if (ApiError.isApiError(error)) {
        errorMessage = error.getDisplayMessage();
      } else if (error instanceof Error) {
        errorMessage = error.message;
      }

      toast.error(errorMessage);
    }
  }, [isError, error]);

  if (isLoading || !data) {
    return (
      <div className="flex-1 px-[calc(30px-var(--market-card-border-width))] py-10">
        <div className="grid grid-cols-[repeat(auto-fill,minmax(var(--market-card-width),1fr))] gap-x-7 gap-y-14 overflow-hidden p-[var(--market-card-border-width)]">
          {Array.from({ length: 10 }).map((_, index) => (
            <div key={index} className="relative w-full">
              <PredictionMarketCardSkeleton />
              <span className="absolute -top-7 right-0 left-0 h-[1px] [background:var(--color-line,#E3E3E3)]" />
            </div>
          ))}
        </div>
      </div>
    );
  }

  // 에러 상태
  if (isError) {
    return (
      <div className="flex flex-1 items-center justify-center p-6">
        <div className="text-center">
          <div className="mb-2 text-lg font-semibold text-gray-700">Failed to load markets</div>
          <div className="text-sm text-gray-500">Please refresh the page or try again later.</div>
        </div>
      </div>
    );
  }

  // 데이터가 없는 경우
  if (data.markets.length === 0) {
    return (
      <div className="flex flex-1 items-center justify-center p-6">
        <EmptyMarketList className="w-full" />
      </div>
    );
  }

  return (
    <div className="flex-1 px-[calc(30px-var(--market-card-border-width))] py-10">
      <div className="grid grid-cols-[repeat(auto-fill,minmax(var(--market-card-width),1fr))] gap-x-7 gap-y-14 overflow-hidden p-[var(--market-card-border-width)]">
        {data.markets.map((item, index) => (
          <div key={item.marketId || index} className="relative w-full">
            <PredictionMarketCard item={item} />
            <span className="absolute -top-7 right-0 left-0 h-[1px] [background:var(--color-line,#E3E3E3)]" />
          </div>
        ))}
      </div>
    </div>
  );
}
