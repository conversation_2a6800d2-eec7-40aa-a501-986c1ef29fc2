type MarketTableRowPositionItem = {
  avatarUrl?: string;
  title: string;
  prediction: string;
  volume: number;
};

type MarketTableRowActivityItem = {
  avatarUrl?: string;
  title: string;
  prediction: string;
};

interface MarketTableRowPositionProps {
  market: MarketTableRowPositionItem;
  type: 'position';
}

interface MarketTableRowActivityProps {
  market: MarketTableRowActivityItem;
  type: 'activity';
}

// Union 타입으로 결합 - type 값에 따라 market의 타입이 자동으로 결정됨
type MarketTableRowProps = MarketTableRowPositionProps | MarketTableRowActivityProps;

export default function MarketTableRow({ market, type }: MarketTableRowProps) {
  // type에 따라 다른 렌더링 로직 구현
  return <div />;
}
