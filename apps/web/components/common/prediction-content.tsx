import { cn } from '@repo/ui/lib/utils';
import { GreenButton } from '../ui/base.button';
import { getGraphVar } from '@/lib/styles';
import { MarketOutcome } from '@/lib/api/market/market.transform';

export interface PredictionOption {
  order: number;
  marketId: string;
  text: string;
  percentage: string;
  volume?: string;
}

interface PredictionContentProps {
  outcomes: MarketOutcome[];
  totalVolume: string;
  handleClick: (selection: MarketOutcome) => void;
}

export default function PredictionOutcomes({
  outcomes,
  totalVolume,
  handleClick,
}: PredictionContentProps) {
  const calculatePercentage = (outcomeVolume: string, total: string) => {
    const outcomeNum = Number(outcomeVolume);
    const totalNum = Number(total);

    if (outcomeNum === 0 || totalNum === 0) {
      return 0;
    }

    const percentage = (outcomeNum / totalNum) * 100;
    return isNaN(percentage) ? 0 : Math.floor(percentage);
  };

  return (
    <div className="p-space-10">
      {/* Options list */}
      <div className="gap-space-5 flex flex-col">
        {outcomes.map(outcome => {
          const percentage = calculatePercentage(outcome.formattedVolume, totalVolume);

          return (
            <div key={outcome.outcome} className="gap-space-10 flex items-center">
              {/* Option Text */}
              <div className="text-gray-3 text-size-xs w-[90px] truncate font-semibold">
                {outcome.outcome}
              </div>

              {/* Progress */}
              <div className="gap-space-10 flex flex-1 flex-row items-center justify-between">
                <div
                  data-name="prediction-progress"
                  className="relative h-2 w-full overflow-hidden"
                >
                  <div
                    data-name="prediction-progress-bar"
                    className={cn('absolute top-0 left-0 h-full rounded-none')}
                    style={{
                      backgroundColor: getGraphVar(outcome.order),
                      width: percentage + '%',
                    }}
                  />
                </div>
                <div>
                  <span className="text-size-xs text-mid-dark font-semibold whitespace-nowrap">
                    {percentage}%
                  </span>
                </div>
              </div>
              {/* Button  */}
              <GreenButton
                rounded="sm"
                fontSize="xxs"
                onClick={() => handleClick(outcome)}
                className="px-space-6"
                size="xs"
              >
                Predict
              </GreenButton>
            </div>
          );
        })}
      </div>
    </div>
  );
}
