import { useComments } from '@/hooks/query/board/use-comments';
import CommonAvatar from '@/components/ui/avatar-image';
import SvgIcon from '@/components/icons/svg-icon';
import { toRelativeTime } from '@/lib/utils';

interface CommentRepliesProps {
  commentId: string;
}

export default function CommentReplies({ commentId }: CommentRepliesProps) {
  const { data: repliesData, isLoading } = useComments(commentId, {
    page: 0,
    limit: 50,
    order: 'latest',
  });

  if (isLoading) {
    return <div className="mt-space-20 ml-space-40 text-gray-3 text-sm">Loading replies...</div>;
  }

  const replies = repliesData?.comments || [];

  if (replies.length === 0) {
    return null;
  }

  return (
    <div className="mt-space-20 ml-space-40">
      <div className="gap-space-20 flex flex-col">
        {replies.map(reply => (
          <div key={reply.id} className="gap-space-15 flex">
            <CommonAvatar size="sm" imageUrl={reply.author.imageUrl} alt={reply.author.nickname} />
            <div className="flex-1">
              <div className="gap-space-10 mb-space-6 flex items-center">
                <span className="text-mid-dark text-sm font-semibold">{reply.author.nickname}</span>
                <span className="text-size-xs text-gray-3">
                  {toRelativeTime(reply.createdAt)} ago
                </span>
              </div>
              <p className="text-mid-dark mb-space-10 text-sm whitespace-pre-wrap">
                {reply.content}
              </p>
              <div className="text-size-sm text-gray-3 flex items-center">
                <button className="gap-space-6 hover:text-mid-dark flex items-center">
                  <SvgIcon name="HeartIcon" className="h-3 w-3" />
                  <span className="text-xs">{reply.likes}</span>
                </button>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
