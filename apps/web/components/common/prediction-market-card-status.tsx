import { MarketStatusEnum } from '@/lib/api/market/market.schema.server';
import { ReactElement } from 'react';

interface PredictionMarketCardStatusProps {
  status: MarketStatusEnum;
}

const StatusIcon = () => (
  <svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path
      d="M7 0C3.13425 0 0 3.13425 0 7C0 10.8658 3.13425 14 7 14C10.8658 14 14 10.8658 14 7C14 3.13425 10.8658 0 7 0ZM7 3.5C7.483 3.5 7.875 3.892 7.875 4.375V6.61675L9.37912 8.12088C9.72037 8.46301 9.72037 9.03699 9.37912 9.37912C9.03699 9.72037 8.46301 9.72037 8.12088 9.37912L6.37088 7.62912C6.20725 7.46462 6.125 7.23188 6.125 7V4.375C6.125 3.892 6.517 3.5 7 3.5Z"
      fill="currentColor"
    />
  </svg>
);

const statusConfig: Record<
  MarketStatusEnum,
  { color: string; icon: () => ReactElement; label: string }
> = {
  OPEN: { color: 'text-yes-green', icon: StatusIcon, label: 'Live' },
  REVIEWING: { color: 'text-yellow-500', icon: StatusIcon, label: 'Awaiting Result' },
  DISPUTABLE: { color: 'text-orange-500', icon: StatusIcon, label: 'Disputable' },
  DISPUTED: { color: 'text-blue-500', icon: StatusIcon, label: 'Under Review' },
  CLOSED_WITHOUT_DISPUTE: { color: 'text-sky', icon: StatusIcon, label: 'Ended' },
  CLOSED_WITH_DISPUTE_ACCEPTED: { color: 'text-gray-600', icon: StatusIcon, label: 'Closed' },
  CLOSED_WITH_DISPUTE_REJECTED: { color: 'text-gray-600', icon: StatusIcon, label: 'Closed' },
  CANCELLED_WITH_UNMET: { color: 'text-gray-3', icon: StatusIcon, label: 'Ended' },
  CANCELLED_WITH_INVALID: { color: 'text-gray-3', icon: StatusIcon, label: 'Ended' },
  CANCELLED_WITH_OUTCOME_NOT_PROPOSED: { color: 'text-gray-3', icon: StatusIcon, label: 'Ended' },
};

export default function PredictionMarketCardStatus({ status }: PredictionMarketCardStatusProps) {
  const config = statusConfig[status];

  if (!config) {
    return null;
  }

  const IconComponent = config.icon;

  return (
    <div className="gap-space-6 flex items-center">
      <span className={`text-size-xxs10 ${config.color}`}>{config.label}</span>
      <div className={`${config.color}`}>
        <IconComponent />
      </div>
    </div>
  );
}
