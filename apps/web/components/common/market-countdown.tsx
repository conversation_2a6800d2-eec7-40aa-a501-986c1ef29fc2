import { useCountdown } from '@/hooks/useCountdown';
import { cn } from '@repo/ui/lib/utils';

interface MarketCountdownProps {
  endTime: Date | string | number | null | undefined;
  onComplete?: () => void;
  className?: string;
  showFull?: boolean;
}

interface CountdownItemProps {
  value: number;
  label: string;
  isLast?: boolean;
}

interface CountdownContainerProps {
  children: React.ReactNode;
  className?: string;
}

const padNumber = (num: number) => num.toString().padStart(2, '0');

const CountdownContainer = ({ children, className }: CountdownContainerProps) => (
  <div
    className={cn(
      'bg-gray-2 text-size-xxs border-line rounded-round-sm flex h-[26px] border',
      className
    )}
  >
    {children}
  </div>
);

const CountdownItem = ({ value, label, isLast = false }: CountdownItemProps) => (
  <div
    className={cn(
      'flex w-[35px] flex-col items-center justify-between',
      !isLast && 'border-r-line border-r'
    )}
  >
    <span className="leading-[14px] font-medium">{padNumber(value)}</span>
    <span className="text-size-xxs8 text-muted-foreground leading-[10px]">
      {value === 1 ? label.slice(0, -1) : label}
    </span>
  </div>
);

export const MarketCountdown = ({
  endTime,
  onComplete,
  className,
  showFull = false,
}: MarketCountdownProps) => {
  // endTime이 nullish인 경우 0으로 표시
  if (!endTime) {
    const zeroTime = { days: 0, hours: 0, minutes: 0, seconds: 0 };
    const { days, hours, minutes, seconds } = zeroTime;
    const showDays = days > 0;

    if (showFull) {
      return (
        <CountdownContainer className={className}>
          <CountdownItem value={days} label="days" />
          <CountdownItem value={hours} label="hours" />
          <CountdownItem value={minutes} label="mins" />
          <CountdownItem value={seconds} label="secs" isLast />
        </CountdownContainer>
      );
    }

    const items = [
      ...(showDays ? [{ value: days, label: 'days' }] : []),
      { value: hours, label: 'hours' },
      { value: minutes, label: 'mins' },
      ...(!showDays ? [{ value: seconds, label: 'secs' }] : []),
    ];

    return (
      <CountdownContainer className={className}>
        {items.map((item, index) => (
          <CountdownItem
            key={item.label}
            value={item.value}
            label={item.label}
            isLast={index === items.length - 1}
          />
        ))}
      </CountdownContainer>
    );
  }

  const { remainingTime } = useCountdown(endTime, { onComplete });

  if (!remainingTime) {
    return null;
  }

  const { days, hours, minutes, seconds } = remainingTime;
  const showDays = days > 0;

  if (showFull) {
    return (
      <CountdownContainer className={className}>
        <CountdownItem value={days} label="days" />
        <CountdownItem value={hours} label="hours" />
        <CountdownItem value={minutes} label="mins" />
        <CountdownItem value={seconds} label="secs" isLast />
      </CountdownContainer>
    );
  }

  const items = [
    ...(showDays ? [{ value: days, label: 'days' }] : []),
    { value: hours, label: 'hours' },
    { value: minutes, label: 'mins' },
    ...(!showDays ? [{ value: seconds, label: 'secs' }] : []),
  ];

  return (
    <CountdownContainer className={className}>
      {items.map((item, index) => (
        <CountdownItem
          key={item.label}
          value={item.value}
          label={item.label}
          isLast={index === items.length - 1}
        />
      ))}
    </CountdownContainer>
  );
};

export default MarketCountdown;
