'use client';

import CommonAvatar from '@/components/ui/avatar-image';
import PredictionLabel from '@/components/ui/prediction-label';
import { UserPostionItem, useUserPositions } from '@/hooks/query/user/use-user-positions';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/ui/components/table';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import * as React from 'react';
import { Address } from 'viem';

export const columns: ColumnDef<UserPostionItem>[] = [
  {
    accessorKey: 'market',
    header: 'Market',
    size: 60,
    cell: ({ row }) => {
      const { marketTitle, marketImageUrl, outcome, marketVolume, estimatedOdds } = row.original;
      return (
        <div className="gap-space-20 flex items-center">
          <CommonAvatar size="md2" imageUrl={marketImageUrl} />
          <div className="flex flex-col">
            <div className="text-mid-dark text-size-sm font-semibold">{marketTitle}</div>
            <div className="gap-space-10 flex items-center">
              {outcome && <PredictionLabel label={outcome} colorName="yes" />}
              {estimatedOdds && (
                <div className="text-size-xs gap-space-6 flex items-center">
                  <span className="text-mid-dark font-semibold">${marketVolume}</span>
                  <span>Vol.</span>
                </div>
              )}
            </div>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'estimatedOdds',
    header: () => <div className="text-center">Est. Odds</div>,
    id: 'Est. Odds',
    size: 20,
    cell: ({ row }) => (
      <div className="text-mid-dark text-size-sm text-center font-medium">
        {row.original.estimatedOdds}
      </div>
    ),
  },
  {
    accessorKey: 'estimatedWin',
    header: () => <div className="text-right">Est. Win</div>,
    id: 'Est. Win',
    size: 20,
    cell: ({ row }) => (
      <div className="text-mid-dark text-size-sm text-right font-semibold">
        ${row.original.estimatedWin}
      </div>
    ),
  },
];

interface PositionsTabProps {
  userAddress: Address;
}

export default function UserPositionsTab({ userAddress }: PositionsTabProps) {
  const [rowSelection, setRowSelection] = React.useState({});

  const {
    data: positionsResponse,
    isLoading,
    error,
  } = useUserPositions(userAddress, {
    page: 0,
    limit: 50,
  });

  const positions = positionsResponse?.positions || [];

  const table = useReactTable<UserPostionItem>({
    data: positions,
    columns: columns as ColumnDef<UserPostionItem>[],
    getCoreRowModel: getCoreRowModel(),
    onRowSelectionChange: setRowSelection,
    state: {
      rowSelection,
    },
  });

  // Loading state
  if (isLoading) {
    return (
      <div className="w-full">
        <div className="px-space-30">
          <div className="flex h-64 items-center justify-center">
            <div className="border-primary h-8 w-8 animate-spin rounded-full border-b-2"></div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="w-full">
        <div className="px-space-30">
          <div className="flex h-64 items-center justify-center">
            <div className="text-red-500">Error loading positions: {error.message}</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="px-space-30">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id} className="border-b-line border-b-0">
                {headerGroup.headers.map(header => (
                  <TableHead
                    style={{
                      width: header.column.columnDef.size + '%',
                    }}
                    className="text-size-sm text-gray-3 pb-space-30 font-semibold"
                    key={header.id}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow key={row.id} className="border-0">
                  {row.getVisibleCells().map(cell => (
                    <TableCell style={{ width: cell.column.columnDef.size + '%' }} key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow className="border-0">
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No positions found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
