'use client';

import { useSafeSmartAccount } from '@/hooks/query/use-safe-smart-account';
import { SmartAccountWarningPopup } from '@/components/ui/popup/smart-account-warning-popup';
import { usePopupStore } from '../ui/popup/popup.state';
import { useEffect } from 'react';
import { ICON_PATH } from '@/lib/constants';
import Image from 'next/image';
import { InfoButton } from '@/components/ui/base.button';
import { logoutAndReload } from '@/lib/auth';

/**
 * 세션 관리 컴포넌트
 * 앱 전체에서 세션 상태를 관리하고 지갑 연결 상태와 동기화
 * SafeSmartAccount도 세션 주소 기반으로 관리
 */
export const SessionManager = () => {
  const { error } = useSafeSmartAccount();
  const { openPopup } = usePopupStore();

  useEffect(() => {
    if (error) {
      openPopup(<SessionErrorPopup error={error} />);
    }
  }, [error, openPopup]);

  return <SmartAccountWarningPopup />;
};

const SessionErrorPopup = ({ error }: { error: Error }) => {
  return (
    <div className="bg-gray-2 w-full">
      {/* Header */}
      <div className="py-space-50 gap-space-40 flex flex-col items-center justify-center text-center">
        <Image src={ICON_PATH.ALERT_RED_BIG} alt="Error" width={50} height={50} />
        <div className="gap-space-20 flex flex-col">
          <h1 className="text-size-sm font-semibold">Session Error</h1>
          <p className="text-size-xs text-gray-3">
            An error occurred while managing your session.
            <br />
            {error.message}
          </p>
        </div>
      </div>

      {/* Action Button */}
      <div className="flex">
        <InfoButton
          onClick={() => {
            logoutAndReload();
          }}
          size="lg"
          fontSize="sm"
          rounded="none"
          className="flex-1"
        >
          Reload
        </InfoButton>
      </div>
    </div>
  );
};
