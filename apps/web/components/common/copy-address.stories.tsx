import type { Meta, StoryObj } from '@storybook/react';
import CopyAddress from './copy-address';

const meta: Meta<typeof CopyAddress> = {
  title: 'Common/CopyAddress',
  component: CopyAddress,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    address: {
      description: '블록체인 주소',
      control: { type: 'text' },
    },
    full: {
      description: '주소 전체 표시 여부',
      control: { type: 'boolean' },
    },
  },
};

export default meta;
type Story = StoryObj<typeof CopyAddress>;

// 기본 사용 예시 (축약된 주소)
export const Default: Story = {
  args: {
    address: '0x1234567890abcdef1234567890abcdef12345678',
    full: false,
  },
};

// 전체 주소 표시
export const FullAddress: Story = {
  args: {
    address: '0x1234567890abcdef1234567890abcdef12345678',
    full: true,
  },
};

// 짧은 주소 예시
export const ShortAddress: Story = {
  args: {
    address: '0xabc123def456',
    full: false,
  },
};

// 다양한 주소 길이에 대한 예시
export const DifferentAddresses: Story = {
  render: () => (
    <div className="flex flex-col gap-4">
      <div>
        <p className="mb-1 text-sm">기본 주소:</p>
        <CopyAddress address="0x1234567890abcdef1234567890abcdef12345678" />
      </div>
      <div>
        <p className="mb-1 text-sm">전체 주소:</p>
        <CopyAddress address="0x1234567890abcdef1234567890abcdef12345678" full />
      </div>
      <div>
        <p className="mb-1 text-sm">짧은 주소:</p>
        <CopyAddress address="0xabc123def456" />
      </div>
    </div>
  ),
};
