import React from 'react';
import { useUserStats } from '@/hooks/query/user/use-user-stats';
import { pxToRem } from '@repo/ui/lib/utils';
import { Skeleton } from '@repo/ui/components/skeleton';
import { Address } from 'viem';

interface UserStatsSectionProps {
  userAddress: Address;
}

// 스켈레톤 컴포넌트
function UserStatsSkeleton() {
  return (
    <section
      className="gap-space-30 py-space-50 flex items-center [&>section]:h-(--card-height) [&>section]:w-(--card-width)"
      style={
        {
          '--card-width': pxToRem(322),
          '--card-height': pxToRem(118),
        } as React.CSSProperties
      }
    >
      {Array.from({ length: 4 }).map((_, index) => (
        <section
          key={index}
          className="gap-space-30 px-space-20 border-line bg-gray-2 flex flex-col justify-center border"
        >
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-6 w-16" />
        </section>
      ))}
    </section>
  );
}

// 에러 컴포넌트
function UserStatsError({ error }: { error: Error }) {
  return (
    <section
      className="gap-space-30 py-space-50 flex items-center justify-center"
      style={
        {
          '--card-width': pxToRem(322),
          '--card-height': pxToRem(118),
        } as React.CSSProperties
      }
    >
      <div className="text-center">
        <div className="mb-2 text-lg font-semibold text-gray-700">Failed to load user stats</div>
        <div className="text-sm text-gray-500">
          {error.message || 'Please refresh the page or try again later.'}
        </div>
      </div>
    </section>
  );
}

export default function UserStatsSection({ userAddress }: UserStatsSectionProps) {
  const { data, isLoading, error } = useUserStats(userAddress);

  if (isLoading) {
    return <UserStatsSkeleton />;
  }

  if (error) {
    return <UserStatsError error={error} />;
  }

  const { positionsValue, profitLoss, volumeTraded, marketTraded } = data?.stats ?? {};
  return (
    <section
      className="gap-space-30 py-space-50 flex items-center [&>section]:h-(--card-height) [&>section]:w-(--card-width)"
      style={
        {
          '--card-width': pxToRem(322),
          '--card-height': pxToRem(118),
        } as React.CSSProperties
      }
    >
      <section className="gap-space-30 px-space-20 border-line bg-gray-2 flex flex-col justify-center border">
        <div className="text-size-sm text-mid-dark font-semibold">Positions Value</div>
        <div className="text-size-base text-dark-deep pl-space-10 font-bold">{positionsValue}</div>
      </section>
      <section className="gap-space-30 px-space-20 border-line bg-gray-2 flex flex-col justify-center border">
        <div className="text-size-sm text-mid-dark font-semibold">Profit / loss</div>
        <div className="text-size-base text-dark-deep font-bold">{profitLoss}</div>
      </section>
      <section className="gap-space-30 px-space-20 border-line bg-gray-2 flex flex-col justify-center border">
        <div className="text-size-sm text-mid-dark font-semibold">Volume traded</div>
        <div className="text-size-base text-dark-deep font-bold">{volumeTraded}</div>
      </section>
      <section className="gap-space-30 px-space-20 border-line bg-gray-2 flex flex-col justify-center border">
        <div className="text-size-sm text-mid-dark font-semibold">Market traded</div>
        <div className="text-size-base text-dark-deep font-bold">{marketTraded}</div>
      </section>
    </section>
  );
}
