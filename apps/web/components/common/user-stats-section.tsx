import { useUserStats } from '@/hooks/query/user/use-user-stats';
import { pxToRem } from '@repo/ui/lib/utils';
import { Address } from 'viem';

interface UserStatsSectionProps {
  userAddress: Address;
}

export default function UserStatsSection({ userAddress }: UserStatsSectionProps) {
  const { data, isLoading, error } = useUserStats(userAddress);

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (error) {
    return <div>Error: {error.message}</div>;
  }
  const { positionsValue, profitLoss, volumeTraded, marketTraded } = data?.stats ?? {};
  return (
    <section
      className="gap-space-30 py-space-50 flex items-center [&>section]:h-(--card-height) [&>section]:w-(--card-width)"
      style={
        {
          '--card-width': pxToRem(322),
          '--card-height': pxToRem(118),
        } as React.CSSProperties
      }
    >
      <section className="gap-space-30 px-space-20 border-line bg-gray-2 flex flex-col justify-center border">
        <div className="text-size-sm text-mid-dark font-semibold">Positions Value</div>
        <div className="text-size-base text-dark-deep pl-space-10 font-bold">${positionsValue}</div>
      </section>
      <section className="gap-space-30 px-space-20 border-line bg-gray-2 flex flex-col justify-center border">
        <div className="text-size-sm text-mid-dark font-semibold">Profit / loss</div>
        <div className="text-size-base text-dark-deep font-bold">${profitLoss}</div>
      </section>
      <section className="gap-space-30 px-space-20 border-line bg-gray-2 flex flex-col justify-center border">
        <div className="text-size-sm text-mid-dark font-semibold">Volume traded</div>
        <div className="text-size-base text-dark-deep font-bold">${volumeTraded}</div>
      </section>
      <section className="gap-space-30 px-space-20 border-line bg-gray-2 flex flex-col justify-center border">
        <div className="text-size-sm text-mid-dark font-semibold">Market traded</div>
        <div className="text-size-base text-dark-deep font-bold">${marketTraded}</div>
      </section>
    </section>
  );
}
