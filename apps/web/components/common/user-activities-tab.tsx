'use client';

import CommonAvatar from '@/components/ui/avatar-image';
import PredictionLabel from '@/components/ui/prediction-label';
import { UserActivityItem, useUserActivities } from '@/hooks/query/user/use-user-activities';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@repo/ui/components/table';
import { ColumnDef, flexRender, getCoreRowModel, useReactTable } from '@tanstack/react-table';
import { ArrowUpRight } from 'lucide-react';
import { useState } from 'react';
import { Address } from 'viem';

export const columns: ColumnDef<UserActivityItem>[] = [
  {
    accessorKey: 'market',
    header: 'Market',
    size: 60, // 60% of the table width
    cell: ({ row }) => {
      const activity = row.original;
      return (
        <div className="gap-space-20 flex items-center">
          <CommonAvatar size="md2" imageUrl={activity.marketImageUrl} />
          <div className="flex flex-col">
            <div className="text-mid-dark text-size-sm font-semibold">{activity.marketTitle}</div>
            <div className="gap-space-10 flex items-center">
              {activity.outcome && <PredictionLabel label={activity.outcome} colorName="yes" />}
            </div>
          </div>
        </div>
      );
    },
  },
  {
    accessorKey: 'type',
    header: () => <div className="text-center">Type</div>,
    id: 'Type',
    size: 20, // 20% of the table width
    cell: ({ row }) => (
      <div className="text-mid-dark text-size-sm text-center font-medium">{row.original.type}</div>
    ),
  },
  {
    accessorKey: 'amount',
    header: () => <div className="text-right">Value</div>,
    id: 'Value',
    size: 20, // 20% of the table width
    cell: ({ row }) => {
      const activity = row.original;
      const timeAgo = activity.relativeTime;
      return (
        <div className="flex flex-col items-end">
          <div className="text-mid-dark text-size-sm font-semibold">${activity.value}</div>
          <div className="text-size-xs flex items-center gap-1 text-gray-400">
            {timeAgo}
            <ArrowUpRight className="h-3 w-3" />
          </div>
        </div>
      );
    },
  },
];

interface ActivitiesTableProps {
  userAddress: Address;
}

export default function UserActivitiesTab({ userAddress }: ActivitiesTableProps) {
  const [rowSelection, setRowSelection] = useState({});

  const {
    data: activitiesResponse,
    isLoading,
    error,
  } = useUserActivities(userAddress, {
    limit: 50,
    page: 0,
  });

  const activities = activitiesResponse?.activities || [];
  const table = useReactTable<UserActivityItem>({
    data: activities,
    columns: columns as ColumnDef<UserActivityItem>[],
    getCoreRowModel: getCoreRowModel(),
    onRowSelectionChange: setRowSelection,
    state: {
      rowSelection,
    },
  });

  // Loading state
  if (isLoading) {
    return (
      <div className="w-full">
        <div className="px-space-30">
          <div className="flex h-64 items-center justify-center">
            <div className="border-primary h-8 w-8 animate-spin rounded-full border-b-2"></div>
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="w-full">
        <div className="px-space-30">
          <div className="flex h-64 items-center justify-center">
            <div className="text-red-500">Error loading activities: {error.message}</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="w-full">
      <div className="px-space-30">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map(headerGroup => (
              <TableRow key={headerGroup.id} className="border-b-line border-b-0">
                {headerGroup.headers.map(header => (
                  <TableHead
                    style={{
                      width: header.column.columnDef.size + '%',
                    }}
                    className="text-size-sm text-gray-3 pb-space-30 font-semibold"
                    key={header.id}
                  >
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map(row => (
                <TableRow key={row.id} className="border-0">
                  {row.getVisibleCells().map(cell => (
                    <TableCell style={{ width: cell.column.columnDef.size + '%' }} key={cell.id}>
                      {flexRender(cell.column.columnDef.cell, cell.getContext())}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow className="border-0">
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  No activities found.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}
