import type { <PERSON>a, StoryObj } from '@storybook/react';
import { MarketCountdown } from './market-countdown';
import { useState, useEffect } from 'react';

const meta: Meta<typeof MarketCountdown> = {
  title: 'Components/Dev/MarketCountdown',
  component: MarketCountdown,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
};

export default meta;

type Story = StoryObj<typeof MarketCountdown>;

// Helper for creating dates relative to now
const createRelativeDate = (days = 0, hours = 0, minutes = 0, seconds = 0): Date => {
  const date = new Date();
  date.setDate(date.getDate() + days);
  date.setHours(date.getHours() + hours);
  date.setMinutes(date.getMinutes() + minutes);
  date.setSeconds(date.getSeconds() + seconds);
  return date;
};

// Dynamic example with actual updating countdown
const DynamicCountdownExample = () => {
  const [key, setKey] = useState(0);

  // Force re-render every minute to keep the example fresh
  useEffect(() => {
    const interval = setInterval(() => {
      setKey(prev => prev + 1);
    }, 60000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="flex flex-col gap-4 rounded-md border p-4">
      <div className="flex flex-col gap-2">
        <h3 className="text-sm font-semibold">Days, Hours, Minutes format:</h3>
        <MarketCountdown endTime={createRelativeDate(30, 12, 4)} />
      </div>

      <div className="flex flex-col gap-2">
        <h3 className="text-sm font-semibold">Hours, Minutes, Seconds format:</h3>
        <MarketCountdown endTime={createRelativeDate(0, 12, 4, 30)} />
      </div>

      <div className="flex flex-col gap-2">
        <h3 className="text-sm font-semibold">Few minutes remaining:</h3>
        <MarketCountdown endTime={createRelativeDate(0, 0, 4, 30)} />
      </div>
    </div>
  );
};

export const Default: Story = {
  args: {
    endTime: createRelativeDate(30, 12, 4),
  },
};

export const WithoutDays: Story = {
  args: {
    endTime: createRelativeDate(0, 12, 4, 30),
  },
};

export const ShortTime: Story = {
  args: {
    endTime: createRelativeDate(0, 0, 4, 30),
  },
};

export const SingleUnits: Story = {
  args: {
    endTime: createRelativeDate(1, 1, 1, 1),
  },
};

export const WithClassName: Story = {
  args: {
    endTime: createRelativeDate(10, 5, 30),
    className: 'bg-slate-100 p-4 rounded-xl',
  },
};

export const AllFormats: Story = {
  render: () => <DynamicCountdownExample />,
};

export const FarFuture: Story = {
  args: {
    endTime: createRelativeDate(365, 0, 0),
  },
};
