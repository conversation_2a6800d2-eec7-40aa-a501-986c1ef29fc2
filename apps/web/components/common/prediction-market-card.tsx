'use client';
import SvgIcon from '@/components/icons/svg-icon';
import { cardSwitchVariants } from '@/lib/animationVariants';
import { formatVolume } from '@repo/shared/utils/number-format';
import { Card, CardContent, CardFooter, CardHeader } from '@repo/ui/components/card';
import { ScrollArea } from '@repo/ui/components/scroll-area';
import { cn } from '@repo/ui/lib/utils';
import { AnimatePresence, motion } from 'motion/react';
import Link from 'next/link';
import { useState } from 'react';
import CommonAvatar from '../ui/avatar-image';
import { MarketCountdown } from './market-countdown';
import PredictionCardQuickMode from './prediction-card-quick-mode';
import PredictionOutcomes from './prediction-content';
import PredictionMarketCardStatus from './prediction-market-card-status';
import type { MarketListItem } from '@/hooks/query/market/market.mapper';
import type { MarketOutcome } from '@/lib/api/market/market.transform';
import { MarketStatusEnum } from '@/lib/api/market/market.schema.server';
import { INNER_LINKS } from '@/lib/constants';

export function PredictionMarketCardSkeleton() {
  return (
    <Card
      data-name="market-card-skeleton"
      className={cn(
        'h-(--market-card-height) min-w-(--market-card-width) justify-between gap-[10px] rounded-none border-0 p-[10px] shadow-none'
      )}
    >
      {/* Header Skeleton */}
      <CardHeader className="gap-0 p-0">
        <div className="flex h-(--market-header-height) items-center justify-center">
          <div className="flex w-full items-center justify-between gap-3">
            <div className="size-10 animate-pulse rounded-full bg-gray-200" />
            <div className="min-w-0 flex-1 space-y-1">
              <div className="h-4 w-full animate-pulse rounded bg-gray-200" />
              <div className="h-4 w-3/4 animate-pulse rounded bg-gray-200" />
            </div>
            <div className="flex flex-col items-end gap-1">
              <div className="h-3 w-12 animate-pulse rounded bg-gray-200" />
              <div className="h-3 w-8 animate-pulse rounded bg-gray-200" />
            </div>
          </div>
        </div>
      </CardHeader>

      {/* Content Skeleton */}
      <CardContent className="h-[102px] p-0">
        <div className="bg-gray-2 h-full space-y-2 p-2">
          <div className="h-6 w-full animate-pulse rounded bg-gray-200" />
          <div className="h-6 w-full animate-pulse rounded bg-gray-200" />
          <div className="h-6 w-2/3 animate-pulse rounded bg-gray-200" />
        </div>
      </CardContent>

      {/* Footer Skeleton */}
      <CardFooter className="h-(--market-footer-height) justify-between p-0">
        <div className="rounded-round-lg border-line bg-gray-2 gap-space-6 flex items-center justify-center border px-2 py-1">
          <div className="size-6 animate-pulse rounded-full bg-gray-200" />
          <div className="h-3 w-16 animate-pulse rounded bg-gray-200" />
        </div>
        <div className="gap-space-8 flex items-center">
          <div className="h-5 w-12 animate-pulse rounded bg-gray-200" />
          <div className="h-4 w-16 animate-pulse rounded bg-gray-200" />
        </div>
      </CardFooter>
    </Card>
  );
}

export interface PredictionMarketCardProps {
  item: MarketListItem;
}
export function PredictionMarketCard({ item }: PredictionMarketCardProps) {
  const {
    marketId,
    marketTitle,
    marketAvatarImageUrl,
    marketParticipants,
    marketOutcomes,
    channelId,
    channelName,
    channelAvatarImageUrl,
    marketStatus,
    marketMaxOutcomeVolumeFormatted,
    marketTotalVolumeFormatted,
    marketNextDeadline,
    marketProposedOutcome,
  } = item;

  const [isQuickMode, setIsQuickMode] = useState(false);
  const [selectedOption, setSelectedOption] = useState<MarketOutcome | null>(null);

  const handleClickPredict = (outcome: MarketOutcome) => {
    if (marketStatus !== 'OPEN') {
      return;
    }

    setSelectedOption(outcome);
    setIsQuickMode(true);
  };

  const showQuickMode = isQuickMode && selectedOption !== null;
  return (
    <AnimatePresence mode="wait">
      {showQuickMode ? (
        <motion.div
          key="quick-mode"
          variants={cardSwitchVariants}
          initial="quickModeInitial"
          animate="quickModeEnter"
          exit="quickModeExit"
        >
          <PredictionCardQuickMode
            marketId={marketId}
            marketTitle={marketTitle}
            marketAvatarImageUrl={marketAvatarImageUrl}
            marketMaxOutcomeVolume={marketMaxOutcomeVolumeFormatted}
            outcomeText={selectedOption.outcome}
            outcomePercentage={`${((parseFloat(selectedOption.formattedVolume) / parseFloat(marketTotalVolumeFormatted)) * 100).toFixed(2)}%`}
            outcomeOrder={selectedOption.order}
            outcomeVolume={selectedOption.volume}
            marketStatus={marketStatus}
            onBack={() => {
              setIsQuickMode(false);
              setSelectedOption(null);
            }}
          />
        </motion.div>
      ) : (
        <motion.div
          key="market-card"
          variants={cardSwitchVariants}
          initial="marketCardInitial"
          animate="marketCardEnter"
          exit="marketCardExit"
        >
          <Card
            data-name="market-card"
            className={cn(
              'h-(--market-card-height) min-w-(--market-card-width) justify-between gap-[10px] rounded-none border-0 p-[10px] shadow-none'
            )}
          >
            <MarketCardHeader
              imageUrl={marketAvatarImageUrl}
              marketId={marketId}
              title={marketTitle}
              volume={marketTotalVolumeFormatted}
              participants={marketParticipants}
            />
            <CardContent className="h-[102px] p-0">
              <ScrollArea className="bg-gray-2 h-full">
                <PredictionOutcomes
                  outcomes={marketOutcomes}
                  handleClick={handleClickPredict}
                  totalVolume={marketTotalVolumeFormatted}
                  marketStatus={marketStatus}
                />
              </ScrollArea>
            </CardContent>
            <MarketCardFooter
              channelId={channelId}
              marketAvatarImageUrl={channelAvatarImageUrl}
              channelName={channelName}
              status={marketStatus}
              endDate={marketNextDeadline}
            />
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

function MarketCardHeader({
  title,
  volume,
  participants,
  marketId,
  imageUrl,
}: {
  title: string;
  volume: string;
  participants: number;
  marketId: string;
  imageUrl: string;
}) {
  return (
    <CardHeader className="gap-0 p-0">
      <Link href={INNER_LINKS.MAIN.MARKETS.DETAIL(marketId)}>
        <div className="flex h-(--market-header-height) items-center justify-center">
          <div className="flex w-full items-center justify-between gap-3">
            <CommonAvatar size="md" className="rounded-full" imageUrl={imageUrl} />
            <h3 className="text-size-sm line-clamp-2 min-w-0 flex-1 leading-tight font-semibold">
              {title}
            </h3>
            <div className="flex flex-col items-end">
              <span className="text-size-xxs text-muted-foreground">
                Vol. {formatVolume(volume)}
              </span>
              <div className="flex items-center gap-1">
                <SvgIcon name="MemberIcon" />
                <span className="text-size-xxs text-muted-foreground">{participants}</span>
              </div>
            </div>
          </div>
        </div>
      </Link>
    </CardHeader>
  );
}

function MarketCardFooter({
  marketAvatarImageUrl,
  channelName,
  status,
  channelId,
  endDate,
}: {
  marketAvatarImageUrl: string;
  channelName: string;
  status: MarketStatusEnum;
  endDate: Date | string | number | null;
  channelId: string;
}) {
  return (
    <CardFooter className="h-(--market-footer-height) justify-between p-0">
      <div className="flex items-center justify-between">
        <Link href={INNER_LINKS.MAIN.CHANNELS.DETAIL(channelId)}>
          <div className="rounded-round-lg border-line bg-gray-2 gap-space-6 flex items-center justify-center border">
            <CommonAvatar size="sm" imageUrl={marketAvatarImageUrl} className="rounded-full" />
            <div className="text-size-xxs pr-space-12 font-semibold">{channelName}</div>
          </div>
        </Link>
      </div>
      <div className="gap-space-8 flex items-center">
        <PredictionMarketCardStatus status={status} />
        {endDate && <MarketCountdown endTime={endDate} />}
      </div>
    </CardFooter>
  );
}
