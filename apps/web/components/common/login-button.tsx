import { useAppKit } from '@reown/appkit/react';
import { DarkButton, GhostButton } from '../ui/base.button';

export function LoginButtonGroup() {
  const { open } = useAppKit();
  return (
    <div className="flex items-center gap-2">
      <GhostButton
        size="sm"
        onClick={() => {
          open();
        }}
      >
        Log in
      </GhostButton>
      <DarkButton
        size="sm"
        onClick={() => {
          open();
        }}
      >
        Sign Up
      </DarkButton>
    </div>
  );
}
