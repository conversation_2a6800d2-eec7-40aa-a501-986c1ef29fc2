import { <PERSON><PERSON>utton, <PERSON>Butt<PERSON> } from '../ui/base.button';
import { login } from '@/lib/auth';

export function LoginButtonGroup() {
  return (
    <div className="flex items-center gap-2">
      <GhostButton
        size="sm"
        onClick={() => {
          login();
        }}
      >
        Log in
      </GhostButton>
      <DarkButton
        size="sm"
        onClick={() => {
          login();
        }}
      >
        Sign Up
      </DarkButton>
    </div>
  );
}
