import { getGraphVar } from '@/lib/styles';
import { <PERSON>, CardContent, <PERSON><PERSON>ooter, CardHeader } from '@repo/ui/components/card';
import { Slider } from '@repo/ui/components/slider';
import { cn } from '@repo/ui/lib/utils';
import { X } from 'lucide-react';
import { useState } from 'react';
import PredictButton from '../actions/predict-button';
import CommonAvatar from '../ui/avatar-image';
import { BaseButton } from '../ui/base.button';
import { DollarInput } from '../ui/dollar-input';
import { useMyUSDCBalance } from '@/hooks/use-usdc-balance';
import { MarketStatusEnum } from '@/lib/api/market/market.schema.server';

interface PredictionCardQuickModeProps {
  marketId: string;
  marketAvatarImageUrl: string;
  marketTitle: string;
  marketMaxOutcomeVolume: string;
  outcomeText: string;
  outcomePercentage: string;
  outcomeOrder: number;
  outcomeVolume: string;
  marketStatus: MarketStatusEnum;
  onBack: () => void;
}

const MIN_BET_AMOUNT = 1;

export default function PredictionCardQuickMode({
  marketTitle,
  marketId,
  marketAvatarImageUrl,
  marketMaxOutcomeVolume,
  outcomeOrder,
  outcomePercentage,
  outcomeText,
  marketStatus,
  onBack,
}: PredictionCardQuickModeProps) {
  const [usdc, setUsdc] = useState<number>(1);
  const maxBetUsdc = Number(marketMaxOutcomeVolume);
  const { balance: myUSDCBalance } = useMyUSDCBalance();

  // 마켓이 OPEN 상태일 때만 예측 가능
  const isMarketLive = marketStatus === 'OPEN';

  const handleAddAmount = (amount: number) => {
    if (!isMarketLive) return;
    setUsdc(prev => {
      const newAmount = prev + amount;
      return newAmount > maxBetUsdc ? maxBetUsdc : newAmount;
    });
  };

  const handleInputChange = (value: number) => {
    if (!isMarketLive) return;
    setUsdc(Math.min(Math.max(value, MIN_BET_AMOUNT), maxBetUsdc));
  };

  const handleSliderChange = (value: number[]) => {
    if (!isMarketLive) return;
    if (value && value.length > 0) {
      const newValue = value[0];
      if (typeof newValue === 'number') {
        setUsdc(newValue);
      }
    }
  };

  return (
    <Card
      style={{
        backgroundColor: getGraphVar(outcomeOrder, 20),
        boxShadow: `0px 0px 0px var(--market-card-border-width) ${getGraphVar(outcomeOrder, 20)}`,
      }}
      className={cn(
        'gap-space-10 relative h-(--market-card-height) min-w-(--market-card-width) justify-between rounded-none border-0 p-[10px] shadow-none transition-all',
        !isMarketLive && 'opacity-70'
      )}
    >
      <button
        onClick={onBack}
        className="absolute top-0 right-0 rounded-full bg-white p-1 text-gray-400 transition-colors hover:text-gray-600"
        aria-label="Close quick mode"
      >
        <X className="text-mid-dark size-3" />
      </button>
      <CardHeader className="items-cente flex h-(--market-header-height) p-0">
        <div className="gap-space-12 flex items-center">
          <CommonAvatar size="md" imageUrl={marketAvatarImageUrl} />
          <div className="flex flex-col justify-center">
            <h3 className="text-size-sm flex-1 leading-tight font-semibold">{marketTitle}</h3>
            <div className="text-size-xs gap-space-6 flex">
              <span className="text-mid-dark">{outcomeText}</span>
              <span className="text-mid-dark">{outcomePercentage}</span>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="gap-space-8 relative flex flex-col">
          <div className="flex items-center justify-between">
            <div className="text-size-xs text-mid-dark font-semibold">Amount</div>
            <div className="text-size-xs">
              <span className="text-gray-3">My Balance: </span>
              <span>${myUSDCBalance}</span>
            </div>
          </div>

          <div className="gap-space-10 flex">
            <div className="flex flex-col justify-between">
              <Slider
                defaultValue={[MIN_BET_AMOUNT]}
                value={[usdc]}
                min={MIN_BET_AMOUNT}
                max={maxBetUsdc}
                step={1}
                onValueChange={handleSliderChange}
                disabled={!isMarketLive}
              />
              <div className="gap-space-5 flex">
                <BaseButton
                  className="text-size-xxs10 px-space-5 rounded-none"
                  variant="neutral"
                  size="xxs"
                  onClick={() => handleAddAmount(1)}
                  disabled={!isMarketLive}
                >
                  +$1
                </BaseButton>
                <BaseButton
                  className="text-size-xxs10 px-space-5 rounded-none"
                  variant="neutral"
                  size="xxs"
                  onClick={() => handleAddAmount(10)}
                  disabled={!isMarketLive}
                >
                  +$10
                </BaseButton>
              </div>
            </div>
            <DollarInput
              value={usdc}
              onChange={handleInputChange}
              minValue={MIN_BET_AMOUNT}
              maxValue={maxBetUsdc}
              className="text-size-xl px-space-10 flex-1 rounded border text-right font-bold"
              disabled={!isMarketLive}
            />
          </div>
        </div>
      </CardContent>
      <CardFooter className="p-0">
        <PredictButton
          marketId={marketId}
          outcome={isMarketLive ? outcomeText : undefined}
          amount={isMarketLive ? usdc : 0}
          onSuccess={onBack}
          onFailure={onBack}
        />
      </CardFooter>
    </Card>
  );
}
