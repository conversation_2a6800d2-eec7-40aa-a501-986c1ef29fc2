'use client';

import { BaseButton } from '@/components/ui/base.button';
import { Card } from '@repo/ui/components/card';
import { WarnningIcon } from '../icons/warnning-icon';

interface TrackingConsentBannerProps {
  open: boolean;
  onAccept: () => void;
  onReject: () => void;
  cookiePolicyUrl?: string;
}

export function TrackingConsentBanner({
  open,
  onAccept,
  onReject,
  cookiePolicyUrl = '/legal/predictgo-terms-of-use-202506.pdf',
}: TrackingConsentBannerProps) {
  if (!open) return null;

  const handleAccept = () => {
    onAccept();
  };

  const handleReject = () => {
    onReject();
  };

  return (
    <div className="fixed right-0 bottom-0 left-0 z-50">
      <div className="mx-auto">
        <Card className="rounded-none border border-gray-200 bg-white shadow-lg">
          <div className="px-12 py-3">
            <div className="flex items-center justify-between gap-6">
              {/* Left side - Icon and text */}
              <div className="flex min-w-0 flex-1 items-start gap-5">
                <div className="mt-0.5 flex-shrink-0">
                  <WarnningIcon />
                </div>

                <div className="min-w-0 flex-1">
                  <p className="text-sm leading-relaxed text-gray-700">
                    We use <strong>Cookies</strong> to continuously improve website optimization and
                    analytics for individual users like you. For more information on how cookies are
                    used on our website, see{' '}
                    <a
                      href={cookiePolicyUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="font-medium text-blue-600 underline hover:text-blue-800"
                    >
                      "Cookies policy"
                    </a>
                    . Click "Accept" to enable cookies or "Reject" if you do not wish to.
                  </p>
                </div>
              </div>

              {/* Right side - Buttons */}
              <div className="flex flex-shrink-0 items-center gap-3">
                <BaseButton variant="dark" onClick={handleReject} size="lg" width="120px">
                  Reject
                </BaseButton>
                <BaseButton onClick={handleAccept} variant="info" size="lg" width="120px">
                  Accept
                </BaseButton>
              </div>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
}
