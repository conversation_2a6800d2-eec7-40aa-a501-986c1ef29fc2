'use client';

import {
  <PERSON><PERSON><PERSON>,
  <PERSON>ubarContent,
  MenubarItem,
  MenubarMenu,
  MenubarSeparator,
  MenubarTrigger,
} from '@repo/ui/components/menubar';
import { Badge } from '@repo/ui/components/badge';
import { Button } from '@repo/ui/components/button';
import ConnectButton from '../connect-button';
import { toast } from '../ui/base.toast';

import { useAccount } from 'wagmi';
import { useGlobalStore } from '@/store/global.store';
import { useEffect, useState } from 'react';

// 주소를 축약해서 보여주는 유틸리티 함수
const formatAddress = (address: string) => {
  return `${address.slice(0, 6)}...${address.slice(-4)}`;
};

// 빌드 정보 타입 정의
interface BuildInfo {
  buildTime: string;
  buildTimestamp: number;
  buildDate: string;
  version: string;
  environment: string;
  gitCommit: string;
  gitBranch: string;
}

export const DebugEnvFloatingPanel: React.FC = () => {
  const { address, isConnected } = useAccount();
  const { session, safeSmartAccount } = useGlobalStore();
  const [buildInfo, setBuildInfo] = useState<BuildInfo | null>(null);

  // 빌드 정보 로드
  useEffect(() => {
    fetch('/build-info.json')
      .then(res => res.json())
      .then(data => setBuildInfo(data))
      .catch(err => console.warn('Failed to load build info:', err));
  }, []);

  // Debug flag에 따라 패널 표시 여부 결정
  if (process.env.NEXT_PUBLIC_DEBUG !== 'true') {
    return null;
  }

  return (
    <div className="fixed right-4 bottom-4 z-50">
      <div className="flex items-center gap-1">
        <Menubar>
          <MenubarMenu>
            <MenubarTrigger>
              <Badge variant="outline" className="border-none">
                {process.env.NEXT_PUBLIC_ENV} - {buildInfo?.buildDate}
              </Badge>
            </MenubarTrigger>
            <MenubarContent>
              <MenubarItem disabled>Environment: {process.env.NEXT_PUBLIC_ENV}</MenubarItem>
              <MenubarSeparator />

              {/* 빌드 정보 */}
              {buildInfo && (
                <>
                  <MenubarItem disabled>Version: {buildInfo.version}</MenubarItem>
                  <MenubarItem
                    onClick={() => {
                      navigator.clipboard.writeText(buildInfo.buildDate);
                      toast.success('빌드 날짜가 복사되었습니다');
                    }}
                  >
                    Build: {buildInfo.buildDate}
                  </MenubarItem>
                  <MenubarItem disabled>Branch: {buildInfo.gitBranch}</MenubarItem>
                  {buildInfo.gitCommit !== 'local' && (
                    <MenubarItem disabled>
                      Commit: {buildInfo.gitCommit.substring(0, 7)}
                    </MenubarItem>
                  )}
                  <MenubarSeparator />
                </>
              )}

              {/* 지갑 정보 */}
              <MenubarItem>
                <div className="flex w-full items-center justify-between">
                  {/* <span>Wallet Connection</span> */}
                  <ConnectButton />
                </div>
              </MenubarItem>
              <MenubarItem disabled>
                Connected:{' '}
                <span className={isConnected ? 'text-green-500' : 'text-red-500'}>
                  {isConnected ? '✅' : '❌'}
                </span>
              </MenubarItem>
              <MenubarItem disabled>
                Address: {address ? formatAddress(address) : 'None'}
              </MenubarItem>
              <MenubarSeparator />

              {/* GlobalStore - 세션 정보 */}
              <MenubarItem disabled>
                Session:{' '}
                <span className={session ? 'text-green-500' : 'text-red-500'}>
                  {session ? '✅' : '❌'}
                </span>
              </MenubarItem>
              {session && (
                <>
                  <MenubarItem disabled>
                    Session Address: {formatAddress(session.address)}
                  </MenubarItem>
                  <MenubarItem disabled>Chain ID: {session.chainId}</MenubarItem>
                  <MenubarItem disabled>
                    Address Match:{' '}
                    <span
                      className={
                        address && session.address.toLowerCase() === address.toLowerCase()
                          ? 'text-green-500'
                          : 'text-red-500'
                      }
                    >
                      {address && session.address.toLowerCase() === address.toLowerCase()
                        ? '✅'
                        : '❌'}
                    </span>
                  </MenubarItem>
                </>
              )}
              <MenubarSeparator />

              {/* GlobalStore - SafeSmartAccount 정보 */}
              <MenubarItem disabled>
                Smart Account:{' '}
                <span className={safeSmartAccount ? 'text-green-500' : 'text-red-500'}>
                  {safeSmartAccount ? '✅' : '❌'}
                </span>
              </MenubarItem>
              {safeSmartAccount && (
                <MenubarItem disabled>
                  SA Address: {formatAddress(safeSmartAccount.address)}
                </MenubarItem>
              )}
              <MenubarSeparator />

              {/* 환경 정보 */}
              <MenubarItem disabled>Node Env: {process.env.NODE_ENV}</MenubarItem>
            </MenubarContent>
          </MenubarMenu>
        </Menubar>
        {buildInfo && (
          <Button
            variant="ghost"
            size="sm"
            className="h-6 w-6 p-0"
            onClick={() => {
              navigator.clipboard.writeText(buildInfo.buildDate);
              toast.success('빌드 날짜가 복사되었습니다');
            }}
          >
            📋
          </Button>
        )}
      </div>
    </div>
  );
};
