// import { marketService } from '@/lib/api/market/market.service';
// import { PredictionMarketCard } from './prediction-market-card';
// import { GetMarketsOrderSchema } from '@/lib/api/market/market.schema.server';
// import { mapMarketsResponseToMarketsProps } from '@/hooks/query/market/market.mapper';

// interface MarketListServerProps {
//   page?: number;
//   limit?: number;
//   order?: GetMarketsOrder;
// }

// export async function MarketListServer({
//   page = 0,
//   limit = 50,
//   order = 'VOLUME',
// }: MarketListServerProps) {
//   try {
//     const marketsResponse = await marketService.getMarkets({ page, limit, order });

//     // 데이터를 PredictionMarketCard가 요구하는 형태로 변환
//     const transformedData = mapMarketsResponseToMarketsProps(marketsResponse);

//     // 데이터가 없는 경우
//     if (!transformedData || !transformedData.markets || transformedData.markets.length === 0) {
//       return (
//         <div className="flex flex-1 items-center justify-center p-6">
//           <div className="text-center">
//             <div className="mb-2 text-lg font-semibold text-gray-700">No markets available</div>
//             <div className="text-sm text-gray-500">
//               Check back later for new prediction markets.
//             </div>
//           </div>
//         </div>
//       );
//     }

//     return (
//       <div className="flex-1 px-[calc(30px-var(--market-card-border-width))] py-10">
//         <div className="grid grid-cols-[repeat(auto-fill,minmax(var(--market-card-width),1fr))] gap-x-7 gap-y-14 overflow-hidden p-[var(--market-card-border-width)]">
//           {transformedData.markets.map((item, index) => (
//             <div key={item.marketId || index} className="relative w-full">
//               <PredictionMarketCard item={item} />
//               <span className="absolute -top-7 right-0 left-0 h-[1px] [background:var(--color-line,#E3E3E3)]" />
//             </div>
//           ))}
//         </div>
//       </div>
//     );
//   } catch (error) {
//     console.error('Failed to fetch markets:', error);
//     return (
//       <div className="flex flex-1 items-center justify-center p-6">
//         <div className="text-center">
//           <div className="mb-2 text-lg font-semibold text-gray-700">Failed to load markets</div>
//           <div className="text-sm text-gray-500">Please refresh the page or try again later.</div>
//         </div>
//       </div>
//     );
//   }
// }
