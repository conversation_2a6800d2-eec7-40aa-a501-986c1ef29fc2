'use client';

import { useEffect } from 'react';
import clarity from '@microsoft/clarity';

interface MicrosoftClarityProps {
  projectId: string;
}

export function MicrosoftClarity({ projectId }: MicrosoftClarityProps) {
  useEffect(() => {
    if (typeof window === 'undefined' || !projectId) return;

    // Microsoft Clarity가 이미 초기화되어 있는지 확인
    if ((window as any).clarity) {
      return;
    }

    // Microsoft Clarity 초기화
    clarity.init(projectId);
  }, [projectId]);

  return null;
}

// 타입 확장
declare global {
  interface Window {
    clarity: (...args: any[]) => void;
  }
}
