'use client';

import { GoogleAnalytics } from '@next/third-parties/google';
import { useTrackingConsent } from '@/hooks/use-tracking-consent';
import { TrackingConsentBanner } from '@/components/common/tracking-consent-banner';
import { MicrosoftClarity } from '@/components/common/microsoft-clarity';
import { env } from '@/lib/env';
import { useEffect } from 'react';
import { trackingService } from '@/lib/tracking';

export function TrackingConsent() {
  const { consentState, shouldShowDialog, isLoading, acceptConsent, rejectConsent } =
    useTrackingConsent();

  // 동의 상태 로딩 완료 후 트래킹 서비스 초기화
  useEffect(() => {
    if (!isLoading) {
      // 동의 상태가 있으면 enabled: true, 없으면 enabled: false로 초기화
      const enabled = consentState
        ? consentState.googleAnalytics || consentState.microsoftClarity
        : false;

      trackingService.initialize({
        gaId: env.NEXT_PUBLIC_GA_MEASUREMENT_ID,
        clarityProjectId: env.NEXT_PUBLIC_CLARITY_PROJECT_ID,
        enabled,
      });
    }
  }, [isLoading, consentState]);

  // 동의 상태 변경 시 트래킹 서비스 업데이트 (초기화 이후)
  useEffect(() => {
    if (!isLoading && consentState) {
      const enabled = consentState.googleAnalytics || consentState.microsoftClarity;
      trackingService.updateConsent(enabled);
    }
  }, [consentState, isLoading]);

  // 로딩 중이거나 서버 사이드에서는 다이얼로그를 표시하지 않음
  if (isLoading) {
    return null;
  }

  return (
    <>
      {/* Google Analytics 조건부 렌더링 */}
      {consentState?.googleAnalytics && env.NEXT_PUBLIC_GA_MEASUREMENT_ID && (
        <GoogleAnalytics gaId={env.NEXT_PUBLIC_GA_MEASUREMENT_ID} />
      )}

      {/* Microsoft Clarity 조건부 렌더링 */}
      {consentState?.microsoftClarity && env.NEXT_PUBLIC_CLARITY_PROJECT_ID && (
        <MicrosoftClarity projectId={env.NEXT_PUBLIC_CLARITY_PROJECT_ID} />
      )}

      <TrackingConsentBanner
        open={shouldShowDialog}
        onAccept={acceptConsent}
        onReject={rejectConsent}
      />
    </>
  );
}
