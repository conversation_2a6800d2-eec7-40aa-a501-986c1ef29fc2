'use client';

import { GoogleAnalytics } from '@next/third-parties/google';
import { useTrackingConsent } from '@/hooks/use-tracking-consent';
import { TrackingConsentBanner } from '@/components/common/tracking-consent-banner';
import { MicrosoftClarity } from '@/components/common/microsoft-clarity';
import { env } from '@/lib/env';

export function TrackingConsent() {
  const { consentState, shouldShowDialog, isLoading, acceptConsent, rejectConsent } =
    useTrackingConsent();

  // 로딩 중이거나 서버 사이드에서는 다이얼로그를 표시하지 않음
  if (isLoading || typeof window === 'undefined') {
    return null;
  }

  return (
    <>
      {/* Google Analytics 조건부 렌더링 */}
      {consentState?.googleAnalytics && env.NEXT_PUBLIC_GA_MEASUREMENT_ID && (
        <GoogleAnalytics gaId={env.NEXT_PUBLIC_GA_MEASUREMENT_ID} />
      )}

      {/* Microsoft Clarity 조건부 렌더링 */}
      {consentState?.microsoftClarity && env.NEXT_PUBLIC_CLARITY_PROJECT_ID && (
        <MicrosoftClarity projectId={env.NEXT_PUBLIC_CLARITY_PROJECT_ID} />
      )}

      <TrackingConsentBanner
        open={shouldShowDialog}
        onAccept={acceptConsent}
        onReject={rejectConsent}
      />
    </>
  );
}
