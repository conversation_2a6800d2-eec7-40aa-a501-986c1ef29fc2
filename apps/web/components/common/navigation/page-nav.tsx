'use client';

import React from 'react';
import { LoginButtonGroup } from '../login-button';
import { MainNavigation } from './main-navigation';
import { ExploreBar } from './explore-bar';
import { PageNavProfile } from './page-nav-profile';
import { Skeleton } from '@repo/ui/components/skeleton';
import { useCurrentUser } from '@/hooks/query/user';

export function PageNav() {
  const { data: userInfo, isLoading, isSignedIn } = useCurrentUser();

  return (
    <div className="sticky top-0 z-50 bg-white">
      {/* Main Navigation */}
      <div
        data-label="nav-section"
        className="px-space-20 flex h-(--nav-height) justify-between border-b border-gray-200"
      >
        <MainNavigation />

        {/* Right side - User info or Login */}
        <div className="flex items-center gap-4">
          {isLoading ? (
            <>
              <Skeleton className="h-8 w-20" />
              <Skeleton className="h-8 w-8 rounded-full" />
            </>
          ) : !!isSignedIn && !!userInfo ? (
            <PageNavProfile
              profile={{
                avatarUrl: userInfo.imageUrl ?? '',
                name: userInfo.nickname,
                address: userInfo.address,
              }}
            />
          ) : (
            <LoginButtonGroup />
          )}
        </div>
      </div>

      {/* Explore bar */}
      <ExploreBar />
    </div>
  );
}

export default PageNav;
