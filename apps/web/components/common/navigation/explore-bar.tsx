'use client';
import React from 'react';
import { usePathname } from 'next/navigation';
import SvgIcon from '@/components/icons/svg-icon';
import { CategorySwiper } from './category-swiper';
import { MarketBriefSearch } from './market-brief-search';
import { useGlobalStore } from '@/store/global.store';

export function ExploreBar() {
  const pathname = usePathname();
  const isMarketsPage = pathname === '/markets';
  const toggleAside = useGlobalStore(state => state.toggleAside);

  return (
    <div className="bg-gray-2 relative flex h-(--sub-nav-height) w-full flex-1 flex-col">
      <div className="flex h-full w-full">
        {/* Left - Category */}
        <CategorySwiper />

        {/* Center - Search */}
        <MarketBriefSearch />

        {/* Right - Trending */}
        <div className="px-space-20 bg-gray-2 gap-space-10 flex h-full w-[340px] items-center justify-between">
          <div className="rounded-round-sm border-line py-space-8 flex h-[36px] flex-1 items-center border">
            <div className="border-r-line border-r px-1">
              <SvgIcon name="KeywordIcon" />
            </div>
            <div className="text-dark text-size-sm px-space-10 font-semibold">
              PolWeb3 Gaminigitics
            </div>
          </div>
          {isMarketsPage && (
            <button
              onClick={toggleAside}
              className="border-sky rounded-round-sm flex size-[30px] items-center justify-center border bg-white"
            >
              <SvgIcon name="AudioSettingsIcon" />
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
