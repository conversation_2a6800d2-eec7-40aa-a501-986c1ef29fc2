import { INNER_LINKS } from '@/lib/constants';
import { Avatar, AvatarFallback, AvatarImage } from '@repo/ui/components/avatar';
import { shortenAddress } from '@repo/ui/lib/utils';
import Link from 'next/link';
import PortfolioNav from './portfolio-nav';

import CommonAvatar from '@/components/ui/avatar-image';
import {
  BaseDropdownMenu,
  BaseDropdownMenuContent,
  BaseDropdownMenuItem,
  BaseDropdownMenuTrigger,
} from '@/components/ui/base.dropdown-menu';
import { logout } from '@/lib/auth';
import { copyToClipboard } from '@/lib/utils';
import { toast } from '@repo/ui/components/sonner';
import { ChevronDown } from 'lucide-react';
import Image from 'next/image';
import { DepositButton } from './deposit-button';
import { InfoButton } from '@/components/ui/base.button';
import { useRouter } from 'next/navigation';

type NavigationLink = {
  label: string;
  href: string;
  icon: string;
  onClick?: () => void;
};

const LINKS: NavigationLink[] = [
  {
    label: 'Positions',
    href: INNER_LINKS.PROFILE.POSITIONS,
    icon: 'positions.svg',
  },
  {
    label: 'Settings',
    href: INNER_LINKS.PROFILE.SETTINGS.PROFILE,
    icon: 'settings.svg',
  },
  {
    label: 'Channel',
    href: INNER_LINKS.PROFILE.CHANNELS.SETTING,
    icon: 'channel.svg',
  },
  {
    label: 'Referral',
    href: INNER_LINKS.PROFILE.REFERRAL,
    icon: 'referral.svg',
  },
  {
    label: 'Share Bonus',
    href: INNER_LINKS.PROFILE.SHARE_BONUS,
    icon: 'share_bonus.svg',
  },
  {
    label: 'Subscription',
    href: INNER_LINKS.PROFILE.SUBSCRIPTIONS,
    icon: 'subscription.svg',
  },
  {
    label: 'Support',
    href: INNER_LINKS.PROFILE.SUPPORT,
    icon: 'support.svg',
  },
  {
    label: 'Logout',
    href: '/logout',
    icon: 'logout.svg',
    onClick: async () => {
      await logout();
    },
  },
];

export const PageNavProfile = ({
  profile,
}: {
  profile: {
    avatarUrl: string;
    name: string;
    address: string;
  };
}) => {
  const router = useRouter();
  return (
    <div className="gap-space-30 flex items-center">
      <PortfolioNav />
      <DepositButton />
      <div>
        <BaseDropdownMenu>
          <BaseDropdownMenuTrigger className="border-none p-2 focus:ring-0">
            <div className="flex items-center gap-2">
              <CommonAvatar
                imageUrl={profile.avatarUrl}
                style={{ width: '28px', height: '28px' }}
              />
              <ChevronDown className="size-4" />
            </div>
          </BaseDropdownMenuTrigger>
          <BaseDropdownMenuContent className="border-line min-w-[200px] p-0">
            <BaseDropdownMenuItem className="bg-gray-2 border-line rounded-none border-b px-[10px] py-[12px]">
              <div className="flex w-full flex-col gap-3">
                <div className="flex items-start gap-2">
                  <Avatar>
                    <AvatarImage src={profile.avatarUrl} />
                    <AvatarFallback>
                      <img
                        src="/default-avatar.svg"
                        alt="Default Avatar"
                        className="h-full w-full"
                      />
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <span className="text-size-xs text-dark font-medium">{profile.name}</span>
                    <button
                      onClick={e => {
                        e.stopPropagation();
                        copyToClipboard(profile.address);
                        toast.success('Address copied to clipboard!');
                      }}
                      className="gap-space-6 flex items-center"
                    >
                      <span className="text-size-xxs10 text-gray-3">
                        {shortenAddress(profile.address)}
                      </span>
                      <Image src="/icons/stickies-fill.svg" alt="Copy" width={14} height={14} />
                    </button>
                    <InfoButton
                      onClick={() => {
                        router.push(
                          INNER_LINKS.MAIN.CHANNELS.DETAIL(profile.address.toLowerCase())
                        );
                      }}
                      className="mt-space-10 font-bold text-white"
                      style={{
                        height: '26px',
                        width: '125px',
                        fontSize: 'var(--text-size-xs)',
                      }}
                    >
                      View My Channel
                    </InfoButton>
                  </div>
                </div>
              </div>
            </BaseDropdownMenuItem>
            {LINKS.map(link => {
              if (link.onClick) {
                return (
                  <BaseDropdownMenuItem
                    className="cursor-pointer px-[10px] py-[10px]"
                    key={link.href}
                    onClick={link.onClick}
                  >
                    <div className="flex items-center gap-2">
                      <div className="size-4">
                        <Image
                          src={`/icons/${link.icon}`}
                          alt={link.label}
                          width={16}
                          height={16}
                        />
                      </div>
                      <span className="text-size-xs text-dark font-semibold">{link.label}</span>
                    </div>
                  </BaseDropdownMenuItem>
                );
              }

              return (
                <BaseDropdownMenuItem className="px-[10px] py-[10px]" key={link.href}>
                  <Link href={link.href} className="w-full">
                    <div className="flex items-center gap-2">
                      <div className="size-4">
                        <Image
                          src={`/icons/${link.icon}`}
                          alt={link.label}
                          width={16}
                          height={16}
                        />
                      </div>
                      <span className="text-size-xs text-dark font-semibold">{link.label}</span>
                    </div>
                  </Link>
                </BaseDropdownMenuItem>
              );
            })}
          </BaseDropdownMenuContent>
        </BaseDropdownMenu>
      </div>
    </div>
  );
};
