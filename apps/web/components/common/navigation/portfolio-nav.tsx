'use client';

import { useMy<PERSON>ortfolio } from '@/hooks/query/portfolio';
import { useMyUSDCBalance } from '@/hooks/use-usdc-balance';
import Link from 'next/link';
import { INNER_LINKS } from '@/lib/constants';

export default function PortfolioNav() {
  const { data: portfolio, isLoading } = useMyPortfolio();
  const { balanceShortened } = useMyUSDCBalance();

  return (
    <div className="gap-space-30 flex items-center">
      <Link className="flex flex-col items-end" href={INNER_LINKS.MAIN.PORTFOLIO}>
        <span className="text-size-base text-yes-green font-bold">
          {isLoading ? (
            <div className="h-4 w-16 animate-pulse rounded-md bg-gray-200" />
          ) : (
            `$${portfolio?.formattedPositionsValueShortened}`
          )}
        </span>
        <span className="text-size-xxs10 text-gray-500">Portfolio</span>
      </Link>
      {/* Fund */}
      <Link className="flex flex-col items-end" href={INNER_LINKS.MAIN.PORTFOLIO}>
        <span className="text-size-base text-no-red font-bold">
          {isLoading ? (
            <div className="h-4 w-12 animate-pulse rounded-md bg-gray-200" />
          ) : (
            `$${balanceShortened}`
          )}
        </span>
        <span className="text-size-xxs10 text-gray-500">Funds</span>
      </Link>
    </div>
  );
}
