import Link from 'next/link';
import React from 'react';
import SvgIcon from '@/components/icons/svg-icon';
import { useCategories } from '@/hooks/query/category/use-categories';
import { Carousel, CarouselApi, CarouselContent, CarouselItem } from '@repo/ui/components/carousel';
import { cn } from '@repo/ui/lib/utils';

export function CategorySwiper() {
  const { data: categories, isLoading } = useCategories();
  const [api, setApi] = React.useState<CarouselApi | null>(null);
  const [canScrollPrev, setCanScrollPrev] = React.useState(false);
  const [canScrollNext, setCanScrollNext] = React.useState(false);

  const onSelect = React.useCallback((api: CarouselApi) => {
    if (!api) return;
    setCanScrollPrev(api.canScrollPrev());
    setCanScrollNext(api.canScrollNext());
  }, []);

  React.useEffect(() => {
    if (!api) return;
    onSelect(api);
    api.on('reInit', onSelect);
    api.on('select', onSelect);

    return () => {
      api?.off('select', onSelect);
    };
  }, [api, onSelect]);

  return (
    <div className="relative w-full max-w-1/2">
      <div className="pl-space-20 flex h-full items-center">
        <div className="flex-shrink-0">
          <SvgIcon name="CategoryIcon" />
        </div>

        <div className="relative flex-1 overflow-hidden">
          {/* Left gradient overlay */}
          <div
            className={cn(
              'from-gray-2 via-gray-2/80 pointer-events-none absolute top-0 bottom-0 left-0 z-10 w-10 bg-gradient-to-r to-transparent transition-opacity duration-300',
              canScrollPrev ? 'opacity-100' : 'opacity-0'
            )}
          />

          {/* Right gradient overlay */}
          <div
            className={cn(
              'from-gray-2 via-gray-2/80 pointer-events-none absolute top-0 right-0 bottom-0 z-10 w-10 bg-gradient-to-l to-transparent transition-opacity duration-300',
              canScrollNext ? 'opacity-100' : 'opacity-0'
            )}
          />

          <Carousel
            setApi={setApi}
            opts={{
              align: 'start',
              containScroll: 'trimSnaps',
              dragFree: true,
            }}
            className="w-full px-2"
          >
            <CarouselContent className="-ml-2">
              {isLoading
                ? Array.from({ length: 8 }).map((_, index) => (
                    <CarouselItem key={index} className="basis-auto p-0">
                      <div className="rounded-round-sm ml-2 h-[30px] w-20 animate-pulse bg-gray-200" />
                    </CarouselItem>
                  ))
                : categories?.length && categories.length > 0
                  ? categories.map((category, index) => (
                      <CarouselItem key={index} className="basis-auto p-0">
                        <Link
                          href={`/category/${category.categoryName.toLowerCase().replace(/\s+/g, '-')}`}
                          className="rounded-round-sm hover:bg-icon-dark border-line px-space-12 text-dark text-size-sm ml-2 flex h-[30px] min-w-fit items-center border bg-white font-semibold whitespace-nowrap transition-all hover:text-white"
                        >
                          {category.categoryName}
                        </Link>
                      </CarouselItem>
                    ))
                  : null}
            </CarouselContent>
          </Carousel>
        </div>
      </div>
    </div>
  );
}
