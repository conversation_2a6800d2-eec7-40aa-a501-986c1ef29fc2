import Link from 'next/link';
import React, { useRef } from 'react';
import { Swiper, SwiperSlide } from 'swiper/react';
import { FreeMode } from 'swiper/modules';
import type { Swiper as SwiperType } from 'swiper';
import SvgIcon from '@/components/icons/svg-icon';
import { useCategories } from '@/hooks/query/category/use-categories';

export function CategorySwiper() {
  const swiperRef = useRef<SwiperType | null>(null);
  const { data: categories, isLoading } = useCategories();

  const handleNextSlide = () => {
    if (swiperRef.current) {
      swiperRef.current.slideNext();
    }
  };

  return (
    <div className="relative">
      <div className="pl-space-20 flex h-full items-center">
        <div className="mr-space-10 flex-shrink-0">
          <SvgIcon name="CategoryIcon" />
        </div>

        <div className="overflow-hidden">
          <Swiper
            modules={[FreeMode]}
            freeMode={true}
            slidesPerView="auto"
            spaceBetween={8}
            className="h-full"
            wrapperClass="!items-center"
            onSwiper={swiper => (swiperRef.current = swiper)}
          >
            {isLoading ? (
              <>
                {[...Array(7)].map((_, index) => (
                  <SwiperSlide key={index} className="!w-auto">
                    <div className="rounded-round-sm h-[30px] w-20 animate-pulse bg-gray-200" />
                  </SwiperSlide>
                ))}
              </>
            ) : (
              (categories ?? []).map(category => (
                <SwiperSlide key={category.categoryName} className="!w-auto">
                  <Link
                    href={`/markets?category=${category.categoryName}`}
                    className="rounded-round-sm hover.bg-gray-1 border-line px-space-12 text-dark text-size-sm flex h-[30px] items-center border font-semibold whitespace-nowrap"
                  >
                    {category.categoryName}
                  </Link>
                </SwiperSlide>
              ))
            )}
          </Swiper>
        </div>
        <button
          className="py-space-8 flex flex-shrink-0 items-center rounded-sm px-[10px] transition-colors"
          onClick={handleNextSlide}
        >
          <SvgIcon name="ChevronNextIcon" />
        </button>
      </div>
    </div>
  );
}
