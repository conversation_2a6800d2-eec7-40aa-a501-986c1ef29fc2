import type { Meta, StoryObj } from '@storybook/react';
import React from 'react';
import { PageNav } from './page-nav';

const meta: Meta<typeof PageNav> = {
  title: 'Common/Navigation/PageNav',
  component: PageNav,
  parameters: {
    layout: 'fullscreen',
  },
  tags: ['autodocs'],
  argTypes: {},
};

export default meta;

type Story = StoryObj<typeof PageNav>;

export const Default: Story = {
  args: {},
  render: () => <PageNav />,
};

export const WithSubMenuHover: Story = {
  render: () => (
    <div className="min-h-screen bg-gray-50">
      <PageNav />
      <div className="p-8">
        <h2 className="mb-4 text-lg font-medium">네비게이션 기능 테스트</h2>
        <ul className="space-y-2 text-sm text-gray-600">
          <li>
            • Arcade 메뉴에 마우스를 올리면 서브메뉴(Ranks, Activity, Expert lounge, Weekly Quiz)가
            표시됩니다
          </li>
          <li>• Rewards 메뉴에 마우스를 올리면 서브메뉴(Referral, Share Bonus)가 표시됩니다</li>
          <li>• 서브메뉴는 해당 메뉴 아래 정확한 위치에 배치됩니다</li>
          <li>• 마우스를 벗어나면 300ms 후에 서브메뉴가 사라집니다</li>
          <li>• 트랜지션 효과가 적용되어 부드럽게 나타나고 사라집니다</li>
        </ul>
      </div>
    </div>
  ),
};

export const ExploreBarFeatures: Story = {
  render: () => (
    <div className="min-h-screen bg-gray-50">
      <PageNav />
      <div className="p-8">
        <h2 className="mb-4 text-lg font-medium">Explore Bar 기능</h2>
        <ul className="space-y-2 text-sm text-gray-600">
          <li>
            • 카테고리 필터: Politics, Finance, Sports, Gaming, Creators, Climate, Tech, Other
          </li>
          <li>• 중앙 검색창: "Search by predict" 기능</li>
          <li>• 우측 트렌딩 키워드 영역</li>
          <li>• 모든 요소가 반응형으로 동작합니다</li>
        </ul>
      </div>
    </div>
  ),
};

export const ComponentStructure: Story = {
  render: () => (
    <div className="min-h-screen bg-gray-50">
      <PageNav />
      <div className="p-8">
        <h2 className="mb-4 text-lg font-medium">컴포넌트 구조</h2>
        <div className="space-y-4 text-sm">
          <div className="rounded border border-gray-200 bg-white p-4">
            <h3 className="mb-2 font-medium">MainNavigation</h3>
            <ul className="ml-4 space-y-1 text-gray-600">
              <li>• 로고 및 메인 메뉴 아이템들</li>
              <li>• NavMenuItem: 개별 메뉴 아이템 (아이콘, 라벨, 뱃지)</li>
              <li>• SubNavigation: 드롭다운 서브메뉴 (트랜지션 효과 포함)</li>
            </ul>
          </div>
          <div className="rounded border border-gray-200 bg-white p-4">
            <h3 className="mb-2 font-medium">ExploreBar</h3>
            <ul className="ml-4 space-y-1 text-gray-600">
              <li>• 카테고리 필터 섹션</li>
              <li>• 검색 입력 섹션</li>
              <li>• 트렌딩 키워드 섹션</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  ),
};

// 반응형 테스트
export const Responsive: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'responsive',
    },
  },
  render: () => (
    <div>
      <PageNav />
      <div className="p-4">
        <p className="text-sm text-gray-600">화면 크기를 조정하여 반응형 동작을 확인하세요.</p>
      </div>
    </div>
  ),
};

// 모바일 뷰
export const Mobile: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'mobile1',
    },
  },
  render: () => (
    <div>
      <PageNav />
      <div className="p-4">
        <p className="text-sm text-gray-600">모바일 화면에서의 네비게이션 표시</p>
      </div>
    </div>
  ),
};

// 태블릿 뷰
export const Tablet: Story = {
  parameters: {
    viewport: {
      defaultViewport: 'tablet',
    },
  },
  render: () => (
    <div>
      <PageNav />
      <div className="p-4">
        <p className="text-sm text-gray-600">태블릿 화면에서의 네비게이션 표시</p>
      </div>
    </div>
  ),
};

// 사용자 로그인 상태 시뮬레이션 (실제 데이터 없이)
export const WithoutUser: Story = {
  render: () => (
    <div>
      <PageNav />
      <div className="p-4">
        <p className="text-sm text-gray-600">로그인하지 않은 상태: Login 버튼이 표시됩니다</p>
      </div>
    </div>
  ),
};
