'use client';

import { SearchIcon } from 'lucide-react';
import React, { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import { useSearchMarkets } from '@/hooks/query/market/use-search-markets';
import { useRouter } from 'next/navigation';
import { INNER_LINKS } from '@/lib/constants';

interface MarketSearchResult {
  id: string;
  title: string;
  imageUrl?: string;
}

export function MarketBriefSearch() {
  const [searchQuery, setSearchQuery] = useState('');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);
  const { data: searchResults, isLoading, isFetching } = useSearchMarkets(searchQuery);
  const router = useRouter();

  const handleSearchChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = event.target.value;
    setSearchQuery(newValue);

    if (newValue.length >= 2) {
      setIsTyping(true);
      setIsDropdownOpen(true);
    } else {
      setIsTyping(false);
      setIsDropdownOpen(false);
    }
  }, []);

  const handleMarketSelect = useCallback((market: MarketSearchResult) => {
    setSearchQuery(market.title);
    setIsDropdownOpen(false);
    setIsTyping(false);
    router.push(INNER_LINKS.MAIN.MARKETS.DETAIL(market.id));
  }, []);

  const handleInputFocus = useCallback(() => {
    if (searchQuery.length >= 2) {
      setIsDropdownOpen(true);
    }
  }, [searchQuery]);

  const handleInputBlur = useCallback((event: React.FocusEvent) => {
    // 드롭다운 내부를 클릭한 경우에는 닫지 않음
    if (searchRef.current?.contains(event.relatedTarget as Node)) {
      return;
    }
    setTimeout(() => {
      setIsDropdownOpen(false);
      setIsTyping(false);
    }, 150);
  }, []);

  // 검색 상태 관리 - 단일 useEffect로 통합
  useEffect(() => {
    if (searchQuery.length >= 2) {
      if (!isLoading && !isFetching && isTyping) {
        setIsTyping(false);
      }
    } else {
      setIsTyping(false);
      setIsDropdownOpen(false);
    }
  }, [searchQuery, isLoading, isFetching, isTyping]);

  // ESC 키 핸들링
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        setIsDropdownOpen(false);
        setIsTyping(false);
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, []);

  // 상태 계산을 useMemo로 최적화
  const { showLoading, showNoResults, showResults } = useMemo(() => {
    const loading = isTyping || isLoading || isFetching;
    const hasResults = (searchResults?.markets?.length ?? 0) > 0;
    const searchCompleted = !loading && searchQuery.length >= 2;

    return {
      showLoading: loading,
      showNoResults: searchCompleted && !hasResults,
      showResults: searchCompleted && hasResults,
    };
  }, [isTyping, isLoading, isFetching, searchResults?.markets?.length, searchQuery.length]);

  return (
    <div className="relative flex-1" ref={searchRef}>
      <div className="bg-gray-1 flex h-full w-full items-center rounded-none border-none px-3 ring-0 outline-0">
        <SearchIcon className="mr-2 text-gray-400" />
        <input
          type="text"
          placeholder="Search by predict"
          className="text-size-xs flex-1 border-none bg-transparent ring-0 outline-none"
          value={searchQuery}
          onChange={handleSearchChange}
          onFocus={handleInputFocus}
          onBlur={handleInputBlur}
        />
      </div>

      {/* 검색 결과 드롭다운 */}
      {isDropdownOpen && searchQuery.length >= 2 && (
        <div className="absolute top-full right-0 left-0 z-50 max-h-96 overflow-y-auto rounded-lg border border-gray-200 bg-white shadow-lg">
          {showLoading && (
            <div className="flex items-center gap-2 px-4 py-3 text-sm text-gray-500">
              <div className="h-4 w-4 animate-spin rounded-full border-b-2 border-gray-500"></div>
              Searching...
            </div>
          )}

          {showNoResults && <div className="px-4 py-3 text-sm text-gray-500">No results found</div>}

          {showResults &&
            searchResults?.markets?.map(market => (
              <button
                key={market.id}
                className="flex w-full items-center gap-3 px-4 py-3 text-left transition-colors hover:bg-gray-50"
                onClick={() => handleMarketSelect(market)}
              >
                {/* 마켓 아바타 이미지 */}
                <div className="flex-shrink-0">
                  {market.imageUrl ? (
                    <img
                      src={market.imageUrl}
                      alt={market.title}
                      className="h-10 w-10 rounded-full object-cover"
                    />
                  ) : (
                    <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gray-200">
                      <span className="text-xs font-medium text-gray-500">
                        {market.title.slice(0, 2).toUpperCase()}
                      </span>
                    </div>
                  )}
                </div>

                {/* 마켓 정보 */}
                <div className="min-w-0 flex-1">
                  <div className="truncate text-sm font-medium text-gray-900">{market.title}</div>
                  <div className="truncate text-xs text-gray-500">
                    @{market.id.slice(0, 8)}...{market.id.slice(-4)}
                  </div>
                </div>

                {/* 검색 아이콘 */}
                <div className="flex-shrink-0">
                  <SearchIcon className="h-4 w-4 text-gray-400" />
                </div>
              </button>
            ))}
        </div>
      )}
    </div>
  );
}
