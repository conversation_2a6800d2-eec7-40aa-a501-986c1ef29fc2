import { pxToRem, shortenAddress } from '@repo/ui/lib/utils';
import SvgIcon from '../icons/svg-icon';
import { toast } from '../ui/base.toast';

interface CopyAddressProps {
  address: string;
  full?: boolean;
}

export default function CopyAddress({ address, full = false }: CopyAddressProps) {
  return (
    <button
      className="px-space-12 bg-gray-2 border-line rounded-round-lg text-size-xxs gap-space-6 flex items-center justify-center border font-semibold"
      style={{ height: pxToRem(26) }}
      onClick={() => {
        navigator.clipboard.writeText(address);
        toast.success('Address copied to clipboard!');
      }}
    >
      {full ? address : shortenAddress(address)}
      <SvgIcon name="CopyIcon" />
    </button>
  );
}
