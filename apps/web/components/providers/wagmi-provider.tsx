'use client';

import { wagmiAdapter, projectId, networks } from '@/lib/web3/wagmi';
import { createAppKit } from '@reown/appkit/react';
import React, { type ReactNode } from 'react';
import { cookieToInitialState, WagmiProvider, type Config } from 'wagmi';
import { siweConfig } from '@/lib/web3/siwe';
import { controlAppKitShadowDOM } from '@/lib/appkit-shadow-controller';

if (!projectId) {
  throw new Error('Project ID is not defined');
}

const metadata = {
  name: 'Predict Go',
  description:
    'Predict Go is a decentralized prediction market platform that allows users to create and participate in prediction markets on various topics.',
  url: typeof window !== 'undefined' ? window.location.origin : '', // origin must match your domain & subdomain
  icons: ['https://avatars.githubusercontent.com/u/*********'],
};

// Create the modal
export const appKitModal = createAppKit({
  adapters: [wagmiAdapter],
  projectId,
  networks: networks as any,
  metadata: metadata,
  allWallets: 'HIDE',
  features: {
    analytics: true, // Optional - defaults to your Cloud configuration
    email: false,
  },
  siweConfig: siweConfig,
  defaultAccountTypes: {
    eip155: 'eoa',
  },
});

// 초기 계정 타입 설정
appKitModal.setPreferredAccountType('eoa', 'eip155');

/**
 * SSO 계정의 smart account 변경을 방지하기 위해 lit로 구현된 appkit의 shadow dom을 제어하는 함수
 * appkitmodal의 상태가 변경될 때마다 실행되며, 상태가 false일 때 감시를 중지합니다.
 */
appKitModal.subscribeState(state => controlAppKitShadowDOM(state.open));

export function WagmiContextProvider({
  children,
  cookies,
}: {
  children: ReactNode;
  cookies: string | null;
}) {
  const initialState = cookieToInitialState(wagmiAdapter.wagmiConfig as Config, cookies);

  return (
    <WagmiProvider config={wagmiAdapter.wagmiConfig as Config} initialState={initialState}>
      {children}
    </WagmiProvider>
  );
}
