import { ChannelInfoResponse } from '@/lib/api/channel/channel.schema.server';
import { createContext, type PropsWithChildren } from 'react';

type MyChannelContextType = {
  channel: ChannelInfoResponse | null;
  isLoading: boolean;
  error: Error | null;
};

const MyChannelContext = createContext<MyChannelContextType>({
  channel: null,
  isLoading: false,
  error: null,
});

export function MyChannelProvider({ children }: PropsWithChildren) {
  return <div>MyChannelProvider</div>;
}
