'use client';

import { useEffect } from 'react';
import { useTracking } from '@/hooks/use-tracking';

interface PurchaseTrackingProps {
  transactionId: string;
  marketId: string;
  outcomeId: string;
  amount: number;
  currency: string;
  price: number;
  quantity?: number;
  userId?: string;
  marketTitle?: string;
  category?: string;
  affiliation?: string;
  coupon?: string;
}

export function PurchaseTracking({
  transactionId,
  marketId,
  outcomeId,
  amount,
  currency,
  price,
  quantity,
  userId,
  marketTitle,
  category,
  affiliation,
  coupon,
}: PurchaseTrackingProps) {
  const { trackPurchase, trackMarketView, isTrackingEnabled } = useTracking();

  // 구매 완료 시 호출되는 함수
  const handlePurchaseComplete = () => {
    if (!isTrackingEnabled) return;

    // 구매 이벤트 트래킹
    trackPurchase({
      transactionId,
      marketId,
      outcomeId,
      marketTitle,
      category,
      amount,
      currency,
      price,
      quantity,
      userId,
      affiliation,
      coupon,
    });

    // 구매 트래킹 완료 (로그는 트래킹 서비스에서 자동으로 출력됨)
  };

  // 마켓 조회 트래킹 (컴포넌트 마운트 시)
  useEffect(() => {
    if (!isTrackingEnabled) return;

    trackMarketView({
      marketId,
      marketTitle,
      category,
      userId,
    });
  }, [isTrackingEnabled, marketId, marketTitle, category, userId, trackMarketView]);

  // 이 컴포넌트는 렌더링하지 않음 (트래킹 전용)
  return null;
}

// 구매 트래킹 훅
export function usePurchaseTracking() {
  const { trackPurchase, trackMarketView, isTrackingEnabled } = useTracking();

  const trackPurchaseEvent = (data: {
    transactionId: string;
    marketId: string;
    outcomeId: string;
    marketTitle?: string;
    category?: string;
    amount: number;
    currency: string;
    price: number;
    quantity?: number;
    userId?: string;
    affiliation?: string;
    coupon?: string;
  }) => {
    if (!isTrackingEnabled) return;
    trackPurchase(data);
  };

  const trackMarketViewEvent = (data: {
    marketId: string;
    marketTitle?: string;
    category?: string;
    userId?: string;
  }) => {
    if (!isTrackingEnabled) return;
    trackMarketView(data);
  };

  return {
    trackPurchaseEvent,
    trackMarketViewEvent,
    isTrackingEnabled,
  };
}
