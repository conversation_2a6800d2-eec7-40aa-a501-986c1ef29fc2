import { usePredictAndGo } from '@/hooks/query/predict';
import { ButtonCustomSize, GreenButton } from '../ui/base.button';
import { cn } from '@repo/ui/lib/utils';
import { useEffect } from 'react';
import { toast } from '../ui/base.toast';
import { TEXT_SIZE_CSS_VARS } from '@/lib/constants';

interface PredictButtonProps {
  marketId: string;
  outcome: string | undefined;
  amount: number;
  className?: string;
  onSuccess?: () => void;
  onFailure?: () => void;
  size?: ButtonCustomSize;
  textSize?: keyof typeof TEXT_SIZE_CSS_VARS;
}

export default function PredictButton({
  marketId,
  outcome,
  amount,
  className,
  onSuccess,
  onFailure,
  size,
  textSize,
}: PredictButtonProps) {
  const text = `Predict ${outcome}`;
  const predictAndGoMutation = usePredictAndGo();

  const handlePredict = async () => {
    if (!outcome || !amount || predictAndGoMutation.isPending) {
      return;
    }

    await predictAndGoMutation.mutateAsync({ marketId, outcome, amount: amount.toString() });
  };

  useEffect(() => {
    if (predictAndGoMutation.isSuccess) {
      toast.success('Predict success');
      onSuccess?.();
    }
    if (predictAndGoMutation.isError) {
      const errorMessage = predictAndGoMutation.error.message
        ? predictAndGoMutation.error.message
        : 'Predict failed';
      toast.error(errorMessage);
      onFailure?.();
    }
  }, [predictAndGoMutation.isSuccess, predictAndGoMutation.isError]);

  const isDisabled = predictAndGoMutation.isPending || !amount || !outcome;
  return (
    <GreenButton
      fontSize={textSize}
      className={cn('w-full', className)}
      onClick={handlePredict}
      disabled={isDisabled}
      size={size}
    >
      {text}
    </GreenButton>
  );
}
