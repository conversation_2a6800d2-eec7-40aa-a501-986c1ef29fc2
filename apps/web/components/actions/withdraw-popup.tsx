import { BaseButton, DarkButton, InfoButton } from '@/components/ui/base.button';
import { BaseInput } from '@/components/ui/base.input';
import { DollarInput } from '@/components/ui/dollar-input';
import { cn } from '@repo/ui/lib/utils';
import { useState } from 'react';

interface WithdrawPopupProps {
  onClose: () => void;
  onSubmit: (walletAddress: string, amount: number) => void;
  isPending?: boolean;
}

export default function WithdrawPopup({ onClose, onSubmit, isPending }: WithdrawPopupProps) {
  const [walletAddress, setWalletAddress] = useState('');
  const [amount, setAmount] = useState<number>(0);

  const isValidAmount = amount >= 1; // Minimum $1
  const isValidAddress = walletAddress.trim().length > 0;

  const handleAmountChange = (value: number) => {
    setAmount(value);
  };

  const handleSubmit = () => {
    if (isValidAddress && isValidAmount) {
      onSubmit(walletAddress, amount);
    }
  };

  return (
    <div className={cn('bg-gray-2 pt-space-40 flex flex-col')}>
      {/* Header */}
      <div className="mb-space-50 text-center">
        <h1 className="text-size-base font-bold">Withdraw</h1>
      </div>

      {/* Content */}
      <div className="gap-space-30 pb-space-40 px-space-25 flex flex-col">
        {/* Wallet Address Input */}
        <div className="gap-space-10 flex flex-col">
          <div className="flex items-center justify-between">
            <label className="text-size-sm text-mid-dark font-semibold">Wallet Address</label>
          </div>
          <BaseInput
            placeholder="0x..."
            value={walletAddress}
            onChange={e => setWalletAddress(e.target.value)}
            className={cn(
              'w-full',
              !isValidAddress && walletAddress && 'border-no-red focus:border-no-red'
            )}
          />
          <div className="text-size-xs text-red-500">
            Please confirm the deposit address carefully before entering it.
          </div>
        </div>

        {/* Amount Input */}
        <div className="gap-space-10 flex flex-col">
          <div className="flex items-center justify-between">
            <label className="text-size-sm text-mid-dark font-semibold">Amount</label>
          </div>
          <div className="relative">
            <DollarInput
              placeholder="$0"
              value={amount}
              onChange={handleAmountChange}
              minValue={0}
              className={cn(
                'w-full pr-16',
                !isValidAmount && amount && 'border-no-red focus:border-no-red'
              )}
            />
          </div>
          <div className="text-size-xs text-blue-500">
            Transactions are usually confirmed within 1-2 minutes.
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex">
        <DarkButton
          onClick={onClose}
          size="lg"
          fontSize="xs"
          rounded="none"
          className="flex-1"
          disabled={isPending}
        >
          Cancel
        </DarkButton>
        <InfoButton
          onClick={handleSubmit}
          size="lg"
          fontSize="xs"
          rounded="none"
          className="flex-1"
          disabled={!isValidAddress || !isValidAmount || isPending}
          loading={isPending}
        >
          {isPending ? 'Processing...' : 'Withdraw'}
        </InfoButton>
      </div>
    </div>
  );
}
