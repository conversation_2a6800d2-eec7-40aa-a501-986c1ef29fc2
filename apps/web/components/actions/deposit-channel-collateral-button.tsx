import { useState } from 'react';
import { BaseButton, ButtonCustomSize, ButtonCustomVariant, InfoButton } from '../ui/base.button';
import { Popup } from '../ui/popup';
import AddDepositPopup from './add-deposit-popup';
import { useDepositChannelCollateralAndGo } from '@/hooks/query/predict';
import { toast } from '../ui/base.toast';
import { TEXT_SIZE_CSS_VARS } from '@/lib/constants';
import { ApiError } from '@/lib/api/base-api.error';

interface DepositChannelCollateralButtonProps {
  children: React.ReactNode;
  isDisabled?: boolean;
  variant?: ButtonCustomVariant;
  style?: React.CSSProperties;
}

export default function DepositChannelCollateralButton({
  children,
  variant = 'info',
  isDisabled = false,
  style,
}: DepositChannelCollateralButtonProps) {
  const [isAddDepositPopupOpen, setIsAddDepositPopupOpen] = useState(false);
  const depositMutation = useDepositChannelCollateralAndGo();

  const onSubmit = async (amount: number) => {
    try {
      await depositMutation.mutateAsync({ amount: amount.toString() });
      setIsAddDepositPopupOpen(false);
      toast.success('Successfully deposited!');
    } catch (error) {
      console.error('Failed to deposit:', error);
      if (ApiError.isApiError(error)) {
        toast.error(error.getDisplayMessage());
      } else {
        toast.error('Failed to deposit. Please try again.');
      }
    }
  };

  return (
    <>
      <BaseButton
        variant={variant}
        disabled={depositMutation.isPending || isDisabled}
        onClick={e => {
          e.preventDefault();
          setIsAddDepositPopupOpen(true);
        }}
        style={style}
      >
        {children}
      </BaseButton>
      <Popup isOpen={isAddDepositPopupOpen} onClose={() => setIsAddDepositPopupOpen(false)}>
        <AddDepositPopup
          onSubmit={onSubmit}
          isPending={depositMutation.isPending}
          isDisabled={depositMutation.isPending}
        />
      </Popup>
    </>
  );
}
