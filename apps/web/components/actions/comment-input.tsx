import { AutoResizeTextarea } from '@/components/ui/auto-resize-textarea';
import CommonAvatar from '@/components/ui/avatar-image';
import { DarkButton, NeutralButton } from '@/components/ui/base.button';
import { cn } from '@repo/ui/lib/utils';
import { useRef, useState } from 'react';
import { useCreateComment } from '@/hooks/query/board/use-create-comment';
import { useCurrentUser } from '@/hooks/query/user';
import { toast } from '../ui/base.toast';

export interface CommentInputProps {
  parentId: string;
  placeholder?: string;
  onSuccess?: () => void;
}

export default function CommentInput({ parentId, placeholder, onSuccess }: CommentInputProps) {
  const { data: user } = useCurrentUser();
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const createCommentMutation = useCreateComment();

  const handleTextChange = () => {
    if (textareaRef.current) {
    }
  };

  const handleSubmit = async () => {
    if (textareaRef.current && textareaRef.current.value.trim() && !isSubmitting) {
      const content = textareaRef.current.value.trim();

      try {
        setIsSubmitting(true);
        await createCommentMutation.mutateAsync({
          parentId,
          data: { content },
        });
        toast.success('Comment created successfully');
        textareaRef.current.value = '';
        onSuccess?.();
      } catch (error) {
        console.error('Failed to create comment:', error);
        toast.error('Failed to create comment');
      } finally {
        setIsSubmitting(false);
      }
    }
  };

  const handleCancel = () => {
    if (textareaRef.current) {
      textareaRef.current.value = '';
    }
  };

  return (
    <div className={cn('border-line pb-space-15 border-b')}>
      <div className="gap-space-15 flex items-start">
        <CommonAvatar imageUrl={user?.imageUrl || ''} alt="User avatar" />
        <div className="max-w-full flex-1">
          <AutoResizeTextarea
            ref={textareaRef}
            placeholder={placeholder}
            onChange={handleTextChange}
            className="focus:border-line w-full border-b border-transparent"
            disabled={isSubmitting}
          />
        </div>
      </div>

      <div className="gap-space-10 flex justify-end">
        <NeutralButton
          fontSize="xs"
          onClick={handleCancel}
          variant="neutral"
          size="sm"
          disabled={isSubmitting}
        >
          Cancel
        </NeutralButton>
        <DarkButton
          fontSize="xs"
          onClick={handleSubmit}
          variant="dark"
          size="sm"
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Commenting...' : 'Comment'}
        </DarkButton>
      </div>
    </div>
  );
}
