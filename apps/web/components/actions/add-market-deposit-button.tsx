import { InfoButton } from '@/components/ui/base.button';
import { useAddDepositFlow } from '@/hooks/query/channel/use-add-deposit-flow';
import { usePopupStore } from '@/components/ui/popup/popup.state';
import AddDepositPopup from '@/components/actions/add-deposit-popup';
import { toast } from '@/components/ui/base.toast';
import { formatCurrency } from '@/lib/format';
import { ApiError } from '@/lib/api/base-api.error';

interface AddDepositButtonProps {
  marketId: string;
}

export default function AddMarketDepositButton({ marketId }: AddDepositButtonProps) {
  const addDepositFlowMutation = useAddDepositFlow(marketId);
  const { openPopup, closePopup } = usePopupStore();

  const handleClick = async () => {
    openPopup(
      <AddDepositPopup
        isPending={addDepositFlowMutation.isPending}
        isDisabled={addDepositFlowMutation.isPending}
        onSubmit={async amount => {
          try {
            await addDepositFlowMutation.mutateAsync(amount);
            toast.success(`Successfully deposited ${formatCurrency(amount)}!`);
            closePopup();
          } catch (error) {
            console.error('Failed to deposit:', error);
            if (ApiError.isApiError(error)) {
              toast.error(error.getDisplayMessage());
            } else {
              toast.error('Failed to deposit. Please try again.');
            }
          }
        }}
      />
    );
  };

  return (
    <InfoButton
      size="sm"
      className="w-[140px]"
      onClick={handleClick}
      disabled={addDepositFlowMutation.isPending}
    >
      Add Deposit
    </InfoButton>
  );
}
