import { useEffect } from 'react';
import { InfoButton } from '@/components/ui/base.button';
import { useDisputeClaimAndGo } from '@/hooks/query/predict/use-claim-and-go';
import { toast } from '@/components/ui/base.toast';
import { cn } from '@repo/ui/lib/utils';

interface DisputeClaimButtonProps {
  className?: string;
  onSuccess?: () => void;
  onFailure?: () => void;
}

export default function DisputeClaimButton({
  className,
  onSuccess,
  onFailure,
}: DisputeClaimButtonProps) {
  const disputeClaimMutation = useDisputeClaimAndGo();

  const handleClaim = async () => {
    try {
      await disputeClaimMutation.mutateAsync();
    } catch (error) {
      // Error will be handled in useEffect
    }
  };

  useEffect(() => {
    if (disputeClaimMutation.isSuccess) {
      toast.success('Dispute claim successful');
      onSuccess?.();
    }
    if (disputeClaimMutation.isError) {
      toast.error('Dispute claim failed');
      onFailure?.();
    }
  }, [disputeClaimMutation.isSuccess, disputeClaimMutation.isError, onSuccess, onFailure]);

  const isDisabled = disputeClaimMutation.isPending;

  return (
    <InfoButton onClick={handleClaim} disabled={isDisabled} size="sm" className={cn(className)}>
      {disputeClaimMutation.isPending ? 'Claiming...' : 'Claim'}
    </InfoButton>
  );
}
