import { useWithdrawChannelCollateralAndGo } from '@/hooks/query/predict';
import { useState } from 'react';
import { DarkButton } from '../ui/base.button';
import { toast } from '../ui/base.toast';
import { Popup } from '../ui/popup';
import WithdrawDepositPopup from './withdraw-deposit-popup';

interface WithdrawChannelCollateralButtonProps {
  children: React.ReactNode;
  isDisabled?: boolean;
}

export default function WithdrawChannelCollateralButton({
  children,
  isDisabled = false,
}: WithdrawChannelCollateralButtonProps) {
  const [isAddDepositPopupOpen, setIsAddDepositPopupOpen] = useState(false);
  const withdrawMutation = useWithdrawChannelCollateralAndGo();

  const onSubmit = async (amount: number) => {
    try {
      await withdrawMutation.mutateAsync({ amount: amount.toString() });
      setIsAddDepositPopupOpen(false);
      toast.success('Successfully withdrawn!');
    } catch (error) {
      console.error('Failed to withdraw:', error);
      toast.error('Failed to withdraw. Please try again.');
    }
  };

  return (
    <>
      <DarkButton
        disabled={withdrawMutation.isPending || isDisabled}
        onClick={() => setIsAddDepositPopupOpen(true)}
      >
        {children}
      </DarkButton>
      <Popup isOpen={isAddDepositPopupOpen} onClose={() => setIsAddDepositPopupOpen(false)}>
        <WithdrawDepositPopup
          onSubmit={onSubmit}
          isPending={withdrawMutation.isPending}
          isDisabled={withdrawMutation.isPending}
        />
      </Popup>
    </>
  );
}
