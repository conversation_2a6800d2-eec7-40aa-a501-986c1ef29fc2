import { useProposeMarketFlow } from '@/hooks/query/market';
import { ButtonCustomSize, InfoButton } from '../ui/base.button';
import { cn } from '@repo/ui/lib/utils';
import ProposeAnswerPopup from '@/components/actions/propose-answer-popup';
import { Popup } from '@/components/ui/popup';
import { toast } from '../ui/base.toast';
import { TEXT_SIZE_CSS_VARS } from '@/lib/constants';
import { useState } from 'react';

interface ProposeAnswerButtonProps {
  marketId: string;
  className?: string;
  onSuccess?: () => void;
  onFailure?: () => void;
  size?: ButtonCustomSize;
  textSize?: keyof typeof TEXT_SIZE_CSS_VARS;
}

export default function ProposeAnswerButton({
  marketId,
  className,
  onSuccess,
  onFailure,
  size,
  textSize,
}: ProposeAnswerButtonProps) {
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const proposeMarketMutation = useProposeMarketFlow();

  const handleClick = () => {
    setIsPopupOpen(true);
  };

  const closePopup = () => {
    setIsPopupOpen(false);
  };

  const handleConfirm = async (outcome: string) => {
    try {
      await proposeMarketMutation.mutateAsync({ marketId, outcome });
      toast.success('Propose success');
      closePopup();
      onSuccess?.();
    } catch (error) {
      console.error('Failed to propose:', error);
      toast.error('Propose failed');
      onFailure?.();
    }
  };

  return (
    <>
      <InfoButton
        fontSize={textSize}
        className={cn('w-full', className)}
        onClick={handleClick}
        disabled={proposeMarketMutation.isPending}
        size={size}
      >
        Propose Answer
      </InfoButton>

      <Popup isOpen={isPopupOpen} onClose={closePopup}>
        <ProposeAnswerPopup
          marketId={marketId}
          isPending={proposeMarketMutation.isPending}
          onConfirm={handleConfirm}
          onCancel={closePopup}
        />
      </Popup>
    </>
  );
}
