import { useEffect } from 'react';
import { InfoButton } from '@/components/ui/base.button';
import { useShareClaimAndGo } from '@/hooks/query/predict/use-claim-and-go';
import { toast } from '@/components/ui/base.toast';
import { cn } from '@repo/ui/lib/utils';

interface ShareClaimButtonProps {
  className?: string;
  onSuccess?: () => void;
  onFailure?: () => void;
}

export default function ShareClaimButton({
  className,
  onSuccess,
  onFailure,
}: ShareClaimButtonProps) {
  const shareClaimMutation = useShareClaimAndGo();

  const handleClaim = async () => {
    try {
      await shareClaimMutation.mutateAsync();
    } catch (error) {
      // Error will be handled in useEffect
    }
  };

  useEffect(() => {
    if (shareClaimMutation.isSuccess) {
      toast.success('Share claim successful');
      onSuccess?.();
    }
    if (shareClaimMutation.isError) {
      toast.error('Share claim failed');
      onFailure?.();
    }
  }, [shareClaimMutation.isSuccess, shareClaimMutation.isError, onSuccess, onFailure]);

  const isDisabled = shareClaimMutation.isPending;

  return (
    <InfoButton onClick={handleClaim} disabled={isDisabled} size="sm" className={cn(className)}>
      {shareClaimMutation.isPending ? 'Claiming...' : 'Claim'}
    </InfoButton>
  );
}
