import { Popup } from '@/components/ui/popup';
import OpenDisputePopupBody from '@/components/ui/popup/open-dispute-popup';
import { useDepositDisputeCollateralAndGo } from '@/hooks/query/predict';
import { MarketOutcome } from '@/lib/api/market/market.transform';
import { DepositDisputeCollateralRequestBody } from '@/lib/api/predict/predict.schema';
import { TEXT_SIZE_CSS_VARS } from '@/lib/constants';
import { useState } from 'react';
import { ButtonCustomSize, InfoButton } from '../ui/base.button';
import { toast } from '../ui/base.toast';
import { formatUsdc } from '@/lib/format';
import { ApiError } from '@/lib/api/base-api.error';

interface DisputeButtonProps {
  size?: ButtonCustomSize;
  textSize?: keyof typeof TEXT_SIZE_CSS_VARS;
  topOutcome: MarketOutcome;
  marketId: string;
}

const getDisputeAmount = (totalOutcomeVolume: BigNumber) => {
  const disputeAmount = totalOutcomeVolume.dividedBy(20);
  return formatUsdc(disputeAmount.toString());
};

export default function DisputeButton({
  size,
  textSize,
  topOutcome,
  marketId,
}: DisputeButtonProps) {
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const depositDisputeCollateralMutation = useDepositDisputeCollateralAndGo(marketId);

  const handleClick = () => {
    setIsPopupOpen(true);
  };

  const closePopup = () => {
    setIsPopupOpen(false);
  };

  const handleSubmit = async (data: {
    amount: number;
    description?: string;
    referenceUrl?: string;
    fileURLs?: string[];
  }) => {
    try {
      const requestData: DepositDisputeCollateralRequestBody = {
        marketId,
        amount: data.amount.toString(),
        description: data.description,
        referenceURL: data.referenceUrl,
        fileURLs: data.fileURLs,
      };
      await depositDisputeCollateralMutation.mutateAsync(requestData);
      toast.success('Dispute opened successfully');
      closePopup();
    } catch (error) {
      console.error('Failed to open dispute:', error);
      if (ApiError.isApiError(error)) {
        toast.error(error.getDisplayMessage());
      } else {
        toast.error('Failed to open dispute');
      }
    }
  };

  return (
    <>
      <InfoButton
        fontSize={textSize}
        className="w-full"
        onClick={handleClick}
        disabled={depositDisputeCollateralMutation.isPending}
        size={size}
      >
        Open Dispute
      </InfoButton>

      <Popup isOpen={isPopupOpen} onClose={closePopup}>
        <OpenDisputePopupBody
          disputeAmount={getDisputeAmount(topOutcome.rawVolume)}
          isPending={depositDisputeCollateralMutation.isPending}
          onClose={closePopup}
          onSubmit={handleSubmit}
        />
      </Popup>
    </>
  );
}
