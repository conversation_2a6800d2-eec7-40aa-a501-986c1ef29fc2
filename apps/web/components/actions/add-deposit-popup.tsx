import React, { useState } from 'react';
import { cn } from '@repo/ui/lib/utils';
import { DollarInput } from '@/components/ui/dollar-input';
import { BaseButton, InfoButton } from '@/components/ui/base.button';
import { useMyUSDCBalance } from '@/hooks/use-usdc-balance';

interface AddDepositPopupProps {
  onSubmit: (amount: number) => void;
  isPending: boolean;
  isDisabled: boolean;
}

export default function AddDepositPopup({ onSubmit, isPending, isDisabled }: AddDepositPopupProps) {
  const { balance: usdcBalance } = useMyUSDCBalance();
  const [amount, setAmount] = useState<number>(0);

  const formatBalance = (balance: number): string => {
    return balance.toLocaleString('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    });
  };

  const isValidAmount = amount >= 1; // Minimum $1
  const isAboveMinimumValidation = amount >= 1; // Different validation for button enable
  const canAfford = amount <= Number(usdcBalance);

  const handleAmountChange = (value: number) => {
    setAmount(value);
  };

  const handleMaxClick = () => {
    setAmount(Number(usdcBalance) || 0);
  };

  const handleConfirm = async () => {
    if (!isValidAmount || !canAfford) return;

    onSubmit(amount);
  };

  return (
    <div className={cn('bg-gray-2 pt-space-40 flex flex-col')}>
      {/* Header */}
      <div className="mb-space-50 text-center">
        <h1 className="text-size-base font-bold">Add Deposit</h1>
      </div>

      {/* Content */}
      <div className="gap-space-30 pb-space-40 px-space-25 flex flex-col">
        {/* Amount Input */}
        <div className="gap-space-10 flex flex-col">
          <div className="flex items-center justify-between">
            <label className="text-size-sm text-mid-dark font-semibold">Amount</label>
          </div>

          <div className="relative">
            <DollarInput
              placeholder="Minimum Amount $50"
              value={amount}
              onChange={handleAmountChange}
              maxValue={Number(usdcBalance)}
              minValue={50}
              className={cn(
                'w-full pr-16',
                !isValidAmount && amount && 'border-no-red focus:border-no-red',
                !canAfford && amount && 'border-no-red focus:border-no-red'
              )}
            />
            <BaseButton
              variant="neutral"
              size="sm"
              onClick={handleMaxClick}
              className="absolute top-1/2 right-2 h-6 -translate-y-1/2 px-2 text-xs"
            >
              MAX
            </BaseButton>
          </div>
          <div className="text-size-xs text-gray-3">
            My Balance ${formatBalance(Number(usdcBalance))}
          </div>
        </div>
      </div>
      <InfoButton
        size="md"
        rounded="none"
        onClick={handleConfirm}
        disabled={isDisabled || !isAboveMinimumValidation || !canAfford || !amount}
        loading={isPending}
      >
        {isPending ? 'Processing...' : 'Confirm'}
      </InfoButton>
    </div>
  );
}
