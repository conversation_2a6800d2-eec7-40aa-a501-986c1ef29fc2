import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { InfoButton } from '@/components/ui/base.button';
import { useRedeemAndGo } from '@/hooks/query/predict/use-redeem-and-go';
import { usePopupStore } from '@/components/ui/popup/popup.state';
import ClaimAllPopup, { ClaimStage } from '@/components/ui/popup/claim-all-popup';
import { toast } from '@/components/ui/base.toast';
import { PortfolioPositionItem } from '@/hooks/query/portfolio/use-portfolio-positions';

interface ClaimAllButtonProps {
  positions: PortfolioPositionItem[];
  className?: string;
}

export default function ClaimAllButton({ positions, className }: ClaimAllButtonProps) {
  const router = useRouter();
  const { openPopup, closePopup } = usePopupStore();
  const redeemAndGoMutation = useRedeemAndGo();
  const [claimStage, setClaimStage] = useState<ClaimStage>(ClaimStage.Initial);

  // 총 클레임 금액 계산
  const totalClaimAmount = positions.reduce((sum, position) => sum + parseFloat(position.value), 0);

  // 팝업에 필요한 마켓 데이터 매핑 (중복 제거 및 value 합산)
  const marketData = positions.reduce(
    (acc, position) => {
      const existingMarket = acc.find(market => market.marketId === position.marketId);

      if (existingMarket) {
        // 같은 마켓이 이미 있으면 value 합산
        existingMarket.value += parseFloat(position.value);
      } else {
        // 새로운 마켓 추가
        acc.push({
          title: position.marketTitle,
          imageUrl: position.marketImageUrl || '',
          value: parseFloat(position.value),
          marketId: position.marketId,
        });
      }

      return acc;
    },
    [] as Array<{ title: string; imageUrl: string; value: number; marketId: string }>
  );

  const handleClaimAll = () => {
    if (positions.length === 0) {
      toast.error('No claimable positions available');
      return;
    }
    setClaimStage(ClaimStage.Initial);
    openPopup(
      <ClaimAllPopup
        claimStage={claimStage}
        totalClaimAmount={totalClaimAmount}
        markets={marketData}
        onConfirm={handleConfirm}
        onCancel={closePopup}
        submittedTransactions={0}
        totalTransactions={positions.length}
        onShareWinnings={handleShareWinnings}
      />
    );
  };

  const handleConfirm = async () => {
    setClaimStage(ClaimStage.InProgress);

    try {
      // 클레임 가능한 마켓 ID들 추출 (중복 제거)
      const marketIds = [...new Set(positions.map(position => position.marketId))];

      // RedeemRequestBody 형태로 변환
      const redeemData = {
        marketIds: marketIds,
      };

      await redeemAndGoMutation.mutateAsync(redeemData);

      // 모든 클레임 완료
      setClaimStage(ClaimStage.Completed);
      openPopup(
        <ClaimAllPopup claimStage={ClaimStage.Completed} onShareWinnings={handleShareWinnings} />
      );
    } catch (error) {
      closePopup();
      toast.error('Claim failed. Please try again.');
    }
  };

  const handleShareWinnings = () => {
    closePopup();
    router.push('/share-bonus');
  };

  // 클레임 가능한 포지션이 없으면 버튼 비활성화
  const isDisabled = positions.length === 0 || redeemAndGoMutation.isPending;

  return (
    <InfoButton onClick={handleClaimAll} disabled={isDisabled} size="sm" className={className}>
      Claim All ({positions.length})
    </InfoButton>
  );
}
