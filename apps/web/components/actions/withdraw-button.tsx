import { BaseButton } from '@/components/ui/base.button';
import { usePopupStore } from '@/components/ui/popup/popup.state';
import WithdrawPopup from './withdraw-popup';
import { useWithdrawAndGo } from '@/hooks/query/predict/use-withdraw-and-go';
import { toAmount } from '@/lib/format';
import { toast } from '@/components/ui/base.toast';
import { ApiError } from '@/lib/api/base-api.error';

interface WithdrawButtonProps {
  className?: string;
}

export default function WithdrawButton({ className }: WithdrawButtonProps) {
  const { openPopup, closePopup } = usePopupStore();
  const withdrawMutation = useWithdrawAndGo();

  const handleWithdraw = (walletAddress: string, amount: number) => {
    withdrawMutation.mutate(
      {
        to: walletAddress,
        amount: toAmount(amount),
      },
      {
        onSuccess: () => {
          toast.success('Your withdrawal has been processed successfully.');
          closePopup();
        },
        onError: error => {
          if (ApiError.isApiError(error)) {
            toast.error(error.getDisplayMessage());
          } else {
            toast.error(error.message || 'An error occurred during withdrawal.');
          }
        },
      }
    );
  };

  const handleClick = () => {
    openPopup(
      <WithdrawPopup
        onClose={closePopup}
        onSubmit={handleWithdraw}
        isPending={withdrawMutation.isPending}
      />
    );
  };

  return (
    <BaseButton className={className} variant="dark" onClick={handleClick}>
      Withdraw
    </BaseButton>
  );
}
