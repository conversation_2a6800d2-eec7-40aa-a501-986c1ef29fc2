import { useState } from 'react';
import {
  useUserInfo,
  useUserStats,
  useUserActivities,
  useUserPositions,
  useUserSubscriptions,
  useUserProfileWithStats,
  useCreateUser,
  useUpdateUserProfile,
} from '../../hooks/use-user';
import { CreateUserRequestBody, UpdateUserProfileRequestBody } from '../../lib/api/user/user.dto';

// 사용자 프로필 컴포넌트
export function UserProfile({ address }: { address: string }) {
  const { data: userInfo, isLoading, error } = useUserInfo(address);
  const { data: userStats } = useUserStats(address);

  if (isLoading) return <div>Loading user profile...</div>;
  if (error) return <div>Error: {error.message}</div>;
  if (!userInfo) return <div>User not found</div>;

  return (
    <div className="user-profile">
      <div className="profile-header">
        {(userInfo as any).profileImage && (
          <img src={(userInfo as any).profileImage} alt="Profile" className="profile-image" />
        )}
        <div className="profile-info">
          <h1>{(userInfo as any).nickname || 'Anonymous User'}</h1>
          <p className="address">{address}</p>
          {(userInfo as any).bio && <p className="bio">{(userInfo as any).bio}</p>}
        </div>
      </div>

      {userStats && (
        <div className="user-stats">
          <h3>Statistics</h3>
          <div className="stats-grid">
            <div className="stat-item">
              <span className="stat-label">Total Predictions</span>
              <span className="stat-value">{(userStats as any).totalPredictions}</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Accuracy</span>
              <span className="stat-value">{(userStats as any).accuracy}%</span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Total Volume</span>
              <span className="stat-value">
                ${((userStats as any).totalVolume as number).toLocaleString()}
              </span>
            </div>
            <div className="stat-item">
              <span className="stat-label">Profit/Loss</span>
              <span
                className={`stat-value ${(userStats as any).profitLoss >= 0 ? 'positive' : 'negative'}`}
              >
                ${((userStats as any).profitLoss as number).toLocaleString()}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

// 사용자 활동 내역 컴포넌트
export function UserActivities({ address }: { address: string }) {
  const [page, setPage] = useState(0);
  const { data: activitiesData, isLoading } = useUserActivities(address, { page, limit: 10 });

  if (isLoading) return <div>Loading activities...</div>;

  return (
    <div className="user-activities">
      <h3>Recent Activities</h3>
      {(activitiesData as any)?.activities.map((activity: any) => (
        <div key={activity.id} className="activity-item">
          <div className="activity-type">{activity.type}</div>
          <div className="activity-details">
            <span>{activity.description}</span>
            <time>{new Date(activity.timestamp).toLocaleDateString()}</time>
          </div>
          <div className="activity-amount">
            {activity.amount && `$${(activity.amount as number).toLocaleString()}`}
          </div>
        </div>
      ))}

      <div className="pagination">
        <button onClick={() => setPage(p => Math.max(0, p - 1))} disabled={page === 0}>
          Previous
        </button>
        <span>Page {page + 1}</span>
        <button onClick={() => setPage(p => p + 1)} disabled={!activitiesData?.hasNext}>
          Next
        </button>
      </div>
    </div>
  );
}

// 사용자 포지션 컴포넌트
export function UserPositions({ address }: { address: string }) {
  const [page, setPage] = useState(0);
  const { data: positionsData, isLoading } = useUserPositions(address, { page, limit: 10 });

  if (isLoading) return <div>Loading positions...</div>;

  return (
    <div className="user-positions">
      <h3>Current Positions</h3>
      {(positionsData as any)?.positions.map((position: any) => (
        <div key={position.id} className="position-item">
          <div className="position-market">
            <h4>{position.marketTitle}</h4>
            <span className="position-outcome">{position.outcome}</span>
          </div>
          <div className="position-details">
            <span>Amount: ${(position.amount as number).toLocaleString()}</span>
            <span>Shares: {position.shares}</span>
            <span
              className={`pnl ${(position.currentValue as number) >= (position.amount as number) ? 'positive' : 'negative'}`}
            >
              PnL: $
              {((position.currentValue as number) - (position.amount as number)).toLocaleString()}
            </span>
          </div>
        </div>
      ))}

      <div className="pagination">
        <button onClick={() => setPage(p => Math.max(0, p - 1))} disabled={page === 0}>
          Previous
        </button>
        <span>Page {page + 1}</span>
        <button onClick={() => setPage(p => p + 1)} disabled={!positionsData?.hasNext}>
          Next
        </button>
      </div>
    </div>
  );
}

// 사용자 구독 관리 컴포넌트
export function UserSubscriptions() {
  const [page, setPage] = useState(0);
  const { data: subscriptionsData, isLoading } = useUserSubscriptions({ page, limit: 10 });

  if (isLoading) return <div>Loading subscriptions...</div>;

  return (
    <div className="user-subscriptions">
      <h3>Subscriptions</h3>
      {subscriptionsData?.subscriptions.map(subscription => (
        <div key={subscription.id} className="subscription-item">
          <div className="subscription-info">
            <h4>{subscription.channelName}</h4>
            <p>{subscription.description}</p>
          </div>
          <div className="subscription-status">
            <span className={`status ${subscription.isActive ? 'active' : 'inactive'}`}>
              {subscription.isActive ? 'Active' : 'Inactive'}
            </span>
            <time>Since: {new Date(subscription.subscribedAt).toLocaleDateString()}</time>
          </div>
        </div>
      ))}

      <div className="pagination">
        <button onClick={() => setPage(p => Math.max(0, p - 1))} disabled={page === 0}>
          Previous
        </button>
        <span>Page {page + 1}</span>
        <button onClick={() => setPage(p => p + 1)} disabled={!subscriptionsData?.hasNext}>
          Next
        </button>
      </div>
    </div>
  );
}

// 사용자 등록 컴포넌트
export function UserRegistration() {
  const createUserMutation = useCreateUser();
  const [formData, setFormData] = useState<CreateUserRequestBody>({
    referralCode: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      await createUserMutation.mutateAsync(formData);
      alert('User registered successfully!');
      setFormData({ referralCode: '' });
    } catch (error) {
      console.error('Registration failed:', error);
      alert('Failed to register user');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="user-registration">
      <h3>Register New User</h3>

      <input
        type="text"
        placeholder="Referral Code (optional)"
        value={formData.referralCode || ''}
        onChange={e => setFormData(prev => ({ ...prev, referralCode: e.target.value }))}
      />

      <button type="submit" disabled={createUserMutation.isPending}>
        {createUserMutation.isPending ? 'Registering...' : 'Register'}
      </button>
    </form>
  );
}

// 프로필 편집 컴포넌트
export function ProfileEditor({ currentUserInfo }: { currentUserInfo?: any }) {
  const updateProfileMutation = useUpdateUserProfile();
  const [formData, setFormData] = useState<Partial<UpdateUserProfileRequestBody>>({
    nickname: currentUserInfo?.nickname || '',
    bio: currentUserInfo?.bio || '',
    profileImage: currentUserInfo?.profileImage || '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // 변경된 필드만 전송
    const changedFields = Object.entries(formData).reduce((acc, [key, value]) => {
      if (value !== currentUserInfo?.[key]) {
        acc[key as keyof UpdateUserProfileRequestBody] = value;
      }
      return acc;
    }, {} as Partial<UpdateUserProfileRequestBody>);

    if (Object.keys(changedFields).length === 0) {
      alert('No changes to save');
      return;
    }

    try {
      await updateProfileMutation.mutateAsync(changedFields as UpdateUserProfileRequestBody);
      alert('Profile updated successfully!');
    } catch (error) {
      console.error('Profile update failed:', error);
      alert('Failed to update profile');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="profile-editor">
      <h3>Edit Profile</h3>

      <input
        type="text"
        placeholder="Nickname"
        value={formData.nickname || ''}
        onChange={e => setFormData(prev => ({ ...prev, nickname: e.target.value }))}
      />

      <textarea
        placeholder="Bio"
        value={formData.bio || ''}
        onChange={e => setFormData(prev => ({ ...prev, bio: e.target.value }))}
      />

      <input
        type="url"
        placeholder="Profile Image URL"
        value={formData.profileImage || ''}
        onChange={e => setFormData(prev => ({ ...prev, profileImage: e.target.value }))}
      />

      <button type="submit" disabled={updateProfileMutation.isPending}>
        {updateProfileMutation.isPending ? 'Updating...' : 'Update Profile'}
      </button>
    </form>
  );
}

// 통합 사용자 대시보드
export function UserDashboard({ address }: { address: string }) {
  const [activeTab, setActiveTab] = useState<
    'profile' | 'activities' | 'positions' | 'subscriptions'
  >('profile');
  const { data: profileWithStats } = useUserProfileWithStats(address);

  return (
    <div className="user-dashboard">
      <nav className="dashboard-nav">
        <button
          onClick={() => setActiveTab('profile')}
          className={activeTab === 'profile' ? 'active' : ''}
        >
          Profile
        </button>
        <button
          onClick={() => setActiveTab('activities')}
          className={activeTab === 'activities' ? 'active' : ''}
        >
          Activities
        </button>
        <button
          onClick={() => setActiveTab('positions')}
          className={activeTab === 'positions' ? 'active' : ''}
        >
          Positions
        </button>
        <button
          onClick={() => setActiveTab('subscriptions')}
          className={activeTab === 'subscriptions' ? 'active' : ''}
        >
          Subscriptions
        </button>
      </nav>

      <div className="dashboard-content">
        {activeTab === 'profile' && (
          <div>
            <UserProfile address={address} />
            <ProfileEditor currentUserInfo={profileWithStats?.userInfo} />
          </div>
        )}
        {activeTab === 'activities' && <UserActivities address={address} />}
        {activeTab === 'positions' && <UserPositions address={address} />}
        {activeTab === 'subscriptions' && <UserSubscriptions />}
      </div>
    </div>
  );
}
