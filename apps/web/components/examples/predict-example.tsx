import { useState } from 'react';
import {
  usePredictAndGo,
  useClaimAndGo,
  useRedeem,
  useWithdraw,
  useDepositChannelCollateral,
  useWithdrawChannelCollateral,
} from '../../hooks/use-predict';
import {
  PredictRequestBody,
  ClaimRequestBody,
  WithdrawRequestBody,
} from '../../lib/api/predict/predict.dto';

// 예측 실행 컴포넌트
export function PredictForm({ marketId }: { marketId: string }) {
  const predictAndGoMutation = usePredictAndGo();
  const [formData, setFormData] = useState<Partial<PredictRequestBody>>({
    marketId,
    outcome: '',
    amount: 0,
    direction: 'BUY',
    slippage: 5,
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.outcome || !formData.amount || formData.amount <= 0) {
      alert('Please fill in all required fields');
      return;
    }

    try {
      await predictAndGoMutation.mutateAsync({
        marketId: formData.marketId!,
        outcome: formData.outcome,
        amount: formData.amount,
        direction: formData.direction!,
        slippage: formData.slippage || 5,
      });

      alert('Prediction submitted successfully!');
    } catch (error) {
      console.error('Prediction failed:', error);
      alert('Failed to submit prediction');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="predict-form">
      <h3>Make a Prediction</h3>

      <select
        value={formData.outcome || ''}
        onChange={e => setFormData(prev => ({ ...prev, outcome: e.target.value }))}
        required
      >
        <option value="">Select Outcome</option>
        <option value="YES">YES</option>
        <option value="NO">NO</option>
      </select>

      <input
        type="number"
        placeholder="Amount"
        min="0"
        step="0.01"
        value={formData.amount || ''}
        onChange={e => setFormData(prev => ({ ...prev, amount: parseFloat(e.target.value) }))}
        required
      />

      <select
        value={formData.direction || 'BUY'}
        onChange={e =>
          setFormData(prev => ({ ...prev, direction: e.target.value as 'BUY' | 'SELL' }))
        }
      >
        <option value="BUY">BUY</option>
        <option value="SELL">SELL</option>
      </select>

      <input
        type="number"
        placeholder="Slippage (%)"
        min="0"
        max="100"
        value={formData.slippage || 5}
        onChange={e => setFormData(prev => ({ ...prev, slippage: parseFloat(e.target.value) }))}
      />

      <button type="submit" disabled={predictAndGoMutation.isPending}>
        {predictAndGoMutation.isPending ? 'Submitting...' : 'Submit Prediction'}
      </button>
    </form>
  );
}

// 보상 청구 컴포넌트
export function ClaimRewards({ marketId }: { marketId: string }) {
  const claimAndGoMutation = useClaimAndGo();
  const [formData, setFormData] = useState<Partial<ClaimRequestBody>>({
    marketId,
    outcome: '',
    amount: 0,
  });

  const handleClaim = async () => {
    if (!formData.outcome || !formData.amount || formData.amount <= 0) {
      alert('Please provide valid outcome and amount');
      return;
    }

    try {
      await claimAndGoMutation.mutateAsync({
        marketId: formData.marketId!,
        outcome: formData.outcome,
        amount: formData.amount,
      });

      alert('Rewards claimed successfully!');
    } catch (error) {
      console.error('Claim failed:', error);
      alert('Failed to claim rewards');
    }
  };

  return (
    <div className="claim-rewards">
      <h3>Claim Rewards</h3>

      <select
        value={formData.outcome || ''}
        onChange={e => setFormData(prev => ({ ...prev, outcome: e.target.value }))}
        required
      >
        <option value="">Select Outcome</option>
        <option value="YES">YES</option>
        <option value="NO">NO</option>
      </select>

      <input
        type="number"
        placeholder="Amount to claim"
        min="0"
        step="0.01"
        value={formData.amount || ''}
        onChange={e => setFormData(prev => ({ ...prev, amount: parseFloat(e.target.value) }))}
        required
      />

      <button onClick={handleClaim} disabled={claimAndGoMutation.isPending}>
        {claimAndGoMutation.isPending ? 'Claiming...' : 'Claim Rewards'}
      </button>
    </div>
  );
}

// 토큰 관리 컴포넌트
export function TokenManagement({ marketId }: { marketId: string }) {
  const redeemMutation = useRedeem();
  const withdrawMutation = useWithdraw();
  const [withdrawData, setWithdrawData] = useState<Partial<WithdrawRequestBody>>({
    marketId,
    amount: 0,
    outcome: '',
  });

  const handleRedeem = async () => {
    try {
      await redeemMutation.mutateAsync({ marketId });
      alert('Tokens redeemed successfully!');
    } catch (error) {
      console.error('Redeem failed:', error);
      alert('Failed to redeem tokens');
    }
  };

  const handleWithdraw = async () => {
    if (!withdrawData.outcome || !withdrawData.amount || withdrawData.amount <= 0) {
      alert('Please provide valid outcome and amount');
      return;
    }

    try {
      await withdrawMutation.mutateAsync({
        marketId: withdrawData.marketId!,
        amount: withdrawData.amount,
        outcome: withdrawData.outcome,
      });

      alert('Tokens withdrawn successfully!');
    } catch (error) {
      console.error('Withdraw failed:', error);
      alert('Failed to withdraw tokens');
    }
  };

  return (
    <div className="token-management">
      <h3>Token Management</h3>

      <div className="redeem-section">
        <h4>Redeem Tokens</h4>
        <button onClick={handleRedeem} disabled={redeemMutation.isPending}>
          {redeemMutation.isPending ? 'Redeeming...' : 'Redeem All Tokens'}
        </button>
      </div>

      <div className="withdraw-section">
        <h4>Withdraw Tokens</h4>
        <select
          value={withdrawData.outcome || ''}
          onChange={e => setWithdrawData(prev => ({ ...prev, outcome: e.target.value }))}
          required
        >
          <option value="">Select Outcome</option>
          <option value="YES">YES</option>
          <option value="NO">NO</option>
        </select>

        <input
          type="number"
          placeholder="Amount to withdraw"
          min="0"
          step="0.01"
          value={withdrawData.amount || ''}
          onChange={e => setWithdrawData(prev => ({ ...prev, amount: parseFloat(e.target.value) }))}
          required
        />

        <button onClick={handleWithdraw} disabled={withdrawMutation.isPending}>
          {withdrawMutation.isPending ? 'Withdrawing...' : 'Withdraw Tokens'}
        </button>
      </div>
    </div>
  );
}

// 채널 담보 관리 컴포넌트
export function ChannelCollateralManagement() {
  const depositMutation = useDepositChannelCollateral();
  const withdrawMutation = useWithdrawChannelCollateral();
  const [amount, setAmount] = useState<number>(0);

  const handleDeposit = async () => {
    if (amount <= 0) {
      alert('Please provide a valid amount');
      return;
    }

    try {
      await depositMutation.mutateAsync({ amount });
      alert('Channel collateral deposited successfully!');
      setAmount(0);
    } catch (error) {
      console.error('Deposit failed:', error);
      alert('Failed to deposit channel collateral');
    }
  };

  const handleWithdraw = async () => {
    if (amount <= 0) {
      alert('Please provide a valid amount');
      return;
    }

    try {
      await withdrawMutation.mutateAsync({ amount });
      alert('Channel collateral withdrawn successfully!');
      setAmount(0);
    } catch (error) {
      console.error('Withdraw failed:', error);
      alert('Failed to withdraw channel collateral');
    }
  };

  return (
    <div className="channel-collateral">
      <h3>Channel Collateral Management</h3>

      <input
        type="number"
        placeholder="Amount"
        min="0"
        value={amount || ''}
        onChange={e => setAmount(parseInt(e.target.value))}
        required
      />

      <div className="action-buttons">
        <button onClick={handleDeposit} disabled={depositMutation.isPending}>
          {depositMutation.isPending ? 'Depositing...' : 'Deposit Collateral'}
        </button>

        <button onClick={handleWithdraw} disabled={withdrawMutation.isPending}>
          {withdrawMutation.isPending ? 'Withdrawing...' : 'Withdraw Collateral'}
        </button>
      </div>
    </div>
  );
}

// 통합 예측 대시보드
export function PredictDashboard({ marketId }: { marketId: string }) {
  const [activeTab, setActiveTab] = useState<'predict' | 'claim' | 'manage' | 'collateral'>(
    'predict'
  );

  return (
    <div className="predict-dashboard">
      <nav className="dashboard-nav">
        <button
          onClick={() => setActiveTab('predict')}
          className={activeTab === 'predict' ? 'active' : ''}
        >
          Predict
        </button>
        <button
          onClick={() => setActiveTab('claim')}
          className={activeTab === 'claim' ? 'active' : ''}
        >
          Claim
        </button>
        <button
          onClick={() => setActiveTab('manage')}
          className={activeTab === 'manage' ? 'active' : ''}
        >
          Manage
        </button>
        <button
          onClick={() => setActiveTab('collateral')}
          className={activeTab === 'collateral' ? 'active' : ''}
        >
          Collateral
        </button>
      </nav>

      <div className="dashboard-content">
        {activeTab === 'predict' && <PredictForm marketId={marketId} />}
        {activeTab === 'claim' && <ClaimRewards marketId={marketId} />}
        {activeTab === 'manage' && <TokenManagement marketId={marketId} />}
        {activeTab === 'collateral' && <ChannelCollateralManagement />}
      </div>
    </div>
  );
}
