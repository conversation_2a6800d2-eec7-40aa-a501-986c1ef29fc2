import { useState } from 'react';
import {
  useComments,
  usePost,
  useChannelPosts,
  usePostWithComments,
  useChannelPostsWithPinned,
  useCreateComment,
  useLikePost,
  useEditPost,
  useDeletePost,
  useCreateChannelPost,
  usePinPost,
  useUnpinPost,
  useTogglePostPin,
  useReportPost,
  useCreateCommentAndRefresh,
} from '../../hooks/use-board';
import {
  CreateChannelPostRequest,
  EditChannelPostRequest,
  CommentType,
  CommentOrder,
  PredictorsFilter,
} from '../../lib/api/board/board.dto';

// 댓글 목록 컴포넌트
export function CommentsList({
  type,
  parentId,
  order = 'latest',
}: {
  type: CommentType;
  parentId: string;
  order?: CommentOrder;
}) {
  const [page, setPage] = useState(0);
  const { data: commentsData, isLoading } = useComments(type, parentId, {
    page,
    limit: 10,
    order,
  });

  if (isLoading) return <div>Loading comments...</div>;

  const comments = (commentsData as any)?.comments || [];

  return (
    <div className="comments-list">
      <h3>Comments</h3>
      {comments.map((comment: any) => (
        <div key={comment.id} className="comment-item">
          <div className="comment-header">
            <span className="comment-author">
              {comment.author?.nickname || comment.author?.address}
            </span>
            <span className="comment-date">{new Date(comment.createdAt).toLocaleString()}</span>
          </div>
          <div className="comment-content">{comment.content}</div>
          {comment.likes && (
            <div className="comment-stats">
              <span>❤️ {comment.likes}</span>
            </div>
          )}
        </div>
      ))}

      <div className="pagination">
        <button onClick={() => setPage(p => Math.max(0, p - 1))} disabled={page === 0}>
          Previous
        </button>
        <span>Page {page + 1}</span>
        <button onClick={() => setPage(p => p + 1)} disabled={!(commentsData as any)?.hasNext}>
          Next
        </button>
      </div>
    </div>
  );
}

// 댓글 작성 폼 컴포넌트
export function CommentForm({
  type,
  parentId,
  onCommentCreated,
}: {
  type: CommentType;
  parentId: string;
  onCommentCreated?: () => void;
}) {
  const [content, setContent] = useState('');
  const createCommentMutation = useCreateComment();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!content.trim()) return;

    try {
      await createCommentMutation.mutateAsync({
        type,
        parentId,
        data: { content },
      });
      setContent('');
      onCommentCreated?.();
    } catch (error) {
      console.error('Failed to create comment:', error);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="comment-form">
      <textarea
        value={content}
        onChange={e => setContent(e.target.value)}
        placeholder="Write a comment..."
        rows={3}
        required
      />
      <button type="submit" disabled={createCommentMutation.isPending || !content.trim()}>
        {createCommentMutation.isPending ? 'Posting...' : 'Post Comment'}
      </button>
    </form>
  );
}

// 포스트 상세 컴포넌트
export function PostDetail({ postId }: { postId: string }) {
  const { data: post, isLoading, error } = usePost(postId);
  const likePostMutation = useLikePost();
  const [showEditForm, setShowEditForm] = useState(false);

  if (isLoading) return <div>Loading post...</div>;
  if (error) return <div>Error: {error.message}</div>;
  if (!post) return <div>Post not found</div>;

  const handleLike = async () => {
    try {
      await likePostMutation.mutateAsync(postId);
    } catch (error) {
      console.error('Failed to like post:', error);
    }
  };

  return (
    <div className="post-detail">
      <div className="post-header">
        <h1>{(post as any).title}</h1>
        <div className="post-meta">
          <span>By {(post as any).author?.nickname || (post as any).author?.address}</span>
          <span>{new Date((post as any).createdAt).toLocaleDateString()}</span>
          {(post as any).isPinned && <span className="pinned-badge">📌 Pinned</span>}
        </div>
      </div>

      <div className="post-content">{(post as any).content}</div>

      {(post as any).images && (post as any).images.length > 0 && (
        <div className="post-images">
          {(post as any).images.map((image: string, index: number) => (
            <img key={index} src={image} alt={`Post image ${index + 1}`} />
          ))}
        </div>
      )}

      <div className="post-actions">
        <button onClick={handleLike} disabled={likePostMutation.isPending} className="like-button">
          ❤️ {(post as any).likes || 0}
        </button>
        <button onClick={() => setShowEditForm(!showEditForm)}>Edit</button>
        <PostActionsMenu postId={postId} />
      </div>

      {showEditForm && <PostEditForm postId={postId} onCancel={() => setShowEditForm(false)} />}
    </div>
  );
}

// 포스트 수정 폼 컴포넌트
export function PostEditForm({
  postId,
  onCancel,
  onSuccess,
}: {
  postId: string;
  onCancel: () => void;
  onSuccess?: () => void;
}) {
  const { data: post } = usePost(postId);
  const editPostMutation = useEditPost();
  const [formData, setFormData] = useState<EditChannelPostRequest>({
    title: (post as any)?.title || '',
    content: (post as any)?.content || '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      await editPostMutation.mutateAsync({ postId, data: formData });
      onSuccess?.();
      onCancel();
    } catch (error) {
      console.error('Failed to edit post:', error);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="post-edit-form">
      <h3>Edit Post</h3>

      <input
        type="text"
        placeholder="Title"
        value={formData.title || ''}
        onChange={e => setFormData(prev => ({ ...prev, title: e.target.value }))}
        required
      />

      <textarea
        placeholder="Content"
        value={formData.content || ''}
        onChange={e => setFormData(prev => ({ ...prev, content: e.target.value }))}
        rows={5}
        required
      />

      <div className="form-actions">
        <button type="submit" disabled={editPostMutation.isPending}>
          {editPostMutation.isPending ? 'Saving...' : 'Save Changes'}
        </button>
        <button type="button" onClick={onCancel}>
          Cancel
        </button>
      </div>
    </form>
  );
}

// 포스트 액션 메뉴 컴포넌트
export function PostActionsMenu({ postId }: { postId: string }) {
  const { data: post } = usePost(postId);
  const deletePostMutation = useDeletePost();
  const togglePinMutation = useTogglePostPin();
  const reportPostMutation = useReportPost();

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this post?')) return;

    try {
      await deletePostMutation.mutateAsync(postId);
    } catch (error) {
      console.error('Failed to delete post:', error);
    }
  };

  const handleTogglePin = async () => {
    try {
      await togglePinMutation.mutateAsync({
        postId,
        currentlyPinned: (post as any)?.isPinned || false,
      });
    } catch (error) {
      console.error('Failed to toggle pin:', error);
    }
  };

  const handleReport = async () => {
    if (!confirm('Are you sure you want to report this post?')) return;

    try {
      await reportPostMutation.mutateAsync({ postId });
      alert('Post reported successfully');
    } catch (error) {
      console.error('Failed to report post:', error);
    }
  };

  return (
    <div className="post-actions-menu">
      <button onClick={handleTogglePin} disabled={togglePinMutation.isPending}>
        {(post as any)?.isPinned ? '📌 Unpin' : '📌 Pin'}
      </button>
      <button
        onClick={handleDelete}
        disabled={deletePostMutation.isPending}
        className="delete-button"
      >
        🗑️ Delete
      </button>
      <button
        onClick={handleReport}
        disabled={reportPostMutation.isPending}
        className="report-button"
      >
        🚩 Report
      </button>
    </div>
  );
}

// 채널 포스트 생성 폼 컴포넌트
export function CreateChannelPostForm({
  channelId,
  onSuccess,
}: {
  channelId: string;
  onSuccess?: () => void;
}) {
  const createPostMutation = useCreateChannelPost();
  const [formData, setFormData] = useState<CreateChannelPostRequest>({
    channelId,
    title: '',
    content: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      await createPostMutation.mutateAsync(formData);
      setFormData({ channelId, title: '', content: '' });
      onSuccess?.();
    } catch (error) {
      console.error('Failed to create post:', error);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="create-post-form">
      <h3>Create New Post</h3>

      <input
        type="text"
        placeholder="Post Title"
        value={formData.title}
        onChange={e => setFormData(prev => ({ ...prev, title: e.target.value }))}
        required
      />

      <textarea
        placeholder="Post Content"
        value={formData.content}
        onChange={e => setFormData(prev => ({ ...prev, content: e.target.value }))}
        rows={6}
        required
      />

      <button type="submit" disabled={createPostMutation.isPending}>
        {createPostMutation.isPending ? 'Creating...' : 'Create Post'}
      </button>
    </form>
  );
}

// 채널 포스트 목록 컴포넌트
export function ChannelPostsList({ channelId }: { channelId: string }) {
  const [page, setPage] = useState(0);
  const { data: postsData, isLoading } = useChannelPosts(channelId, { page, limit: 10 });

  if (isLoading) return <div>Loading posts...</div>;

  const posts = (postsData as any)?.posts || [];

  return (
    <div className="channel-posts-list">
      <h3>Channel Posts</h3>
      {posts.map((post: any) => (
        <div key={post.id} className="post-preview">
          <div className="post-header">
            <h4>{post.title}</h4>
            {post.isPinned && <span className="pinned-badge">📌</span>}
          </div>
          <p className="post-excerpt">{post.content?.substring(0, 200)}...</p>
          <div className="post-meta">
            <span>By {post.author?.nickname || post.author?.address}</span>
            <span>{new Date(post.createdAt).toLocaleDateString()}</span>
            <span>❤️ {post.likes || 0}</span>
            <span>💬 {post.commentCount || 0}</span>
          </div>
        </div>
      ))}

      <div className="pagination">
        <button onClick={() => setPage(p => Math.max(0, p - 1))} disabled={page === 0}>
          Previous
        </button>
        <span>Page {page + 1}</span>
        <button onClick={() => setPage(p => p + 1)} disabled={!(postsData as any)?.hasNext}>
          Next
        </button>
      </div>
    </div>
  );
}

// 채널 포스트와 핀된 포스트를 분리해서 보여주는 컴포넌트
export function ChannelPostsWithPinned({ channelId }: { channelId: string }) {
  const [page, setPage] = useState(0);
  const { data: postsData, isLoading } = useChannelPostsWithPinned(channelId, { page, limit: 10 });

  if (isLoading) return <div>Loading posts...</div>;

  const pinnedPosts = (postsData as any)?.pinnedPosts || [];
  const regularPosts = (postsData as any)?.regularPosts || [];

  return (
    <div className="channel-posts-with-pinned">
      {pinnedPosts.length > 0 && (
        <div className="pinned-posts">
          <h3>📌 Pinned Posts</h3>
          {pinnedPosts.map((post: any) => (
            <div key={post.id} className="post-preview pinned">
              <h4>{post.title}</h4>
              <p>{post.content?.substring(0, 150)}...</p>
              <div className="post-meta">
                <span>By {post.author?.nickname || post.author?.address}</span>
                <span>❤️ {post.likes || 0}</span>
              </div>
            </div>
          ))}
        </div>
      )}

      <div className="regular-posts">
        <h3>Recent Posts</h3>
        {regularPosts.map((post: any) => (
          <div key={post.id} className="post-preview">
            <h4>{post.title}</h4>
            <p>{post.content?.substring(0, 200)}...</p>
            <div className="post-meta">
              <span>By {post.author?.nickname || post.author?.address}</span>
              <span>{new Date(post.createdAt).toLocaleDateString()}</span>
              <span>❤️ {post.likes || 0}</span>
              <span>💬 {post.commentCount || 0}</span>
            </div>
          </div>
        ))}
      </div>

      <div className="pagination">
        <button onClick={() => setPage(p => Math.max(0, p - 1))} disabled={page === 0}>
          Previous
        </button>
        <span>Page {page + 1}</span>
        <button onClick={() => setPage(p => p + 1)} disabled={!(postsData as any)?.hasNext}>
          Next
        </button>
      </div>
    </div>
  );
}

// 포스트와 댓글을 함께 보여주는 컴포넌트
export function PostWithCommentsView({ postId }: { postId: string }) {
  const [commentOrder, setCommentOrder] = useState<CommentOrder>('latest');
  const { data: postData, isLoading } = usePostWithComments(postId, {
    commentOrder,
    commentLimit: 20,
  });

  if (isLoading) return <div>Loading post and comments...</div>;

  const post = (postData as any)?.post;
  const comments = (postData as any)?.comments;

  return (
    <div className="post-with-comments">
      {post && (
        <div className="post-section">
          <PostDetail postId={postId} />
        </div>
      )}

      <div className="comments-section">
        <div className="comments-header">
          <h3>Comments ({(comments as any)?.total || 0})</h3>
          <select
            value={commentOrder}
            onChange={e => setCommentOrder(e.target.value as CommentOrder)}
          >
            <option value="latest">Latest</option>
            <option value="likes">Most Liked</option>
          </select>
        </div>

        <CommentForm type="board" parentId={postId} />

        {comments && (
          <div className="comments-list">
            {(comments as any).comments?.map((comment: any) => (
              <div key={comment.id} className="comment-item">
                <div className="comment-header">
                  <span className="comment-author">
                    {comment.author?.nickname || comment.author?.address}
                  </span>
                  <span className="comment-date">
                    {new Date(comment.createdAt).toLocaleString()}
                  </span>
                </div>
                <div className="comment-content">{comment.content}</div>
                <div className="comment-actions">
                  <span>❤️ {comment.likes || 0}</span>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

// 종합 보드 컴포넌트
export function BoardDashboard({ channelId }: { channelId: string }) {
  const [activeTab, setActiveTab] = useState<'posts' | 'create'>('posts');

  return (
    <div className="board-dashboard">
      <nav className="board-nav">
        <button
          onClick={() => setActiveTab('posts')}
          className={activeTab === 'posts' ? 'active' : ''}
        >
          Posts
        </button>
        <button
          onClick={() => setActiveTab('create')}
          className={activeTab === 'create' ? 'active' : ''}
        >
          Create Post
        </button>
      </nav>

      <div className="board-content">
        {activeTab === 'posts' && <ChannelPostsWithPinned channelId={channelId} />}

        {activeTab === 'create' && (
          <CreateChannelPostForm channelId={channelId} onSuccess={() => setActiveTab('posts')} />
        )}
      </div>
    </div>
  );
}
