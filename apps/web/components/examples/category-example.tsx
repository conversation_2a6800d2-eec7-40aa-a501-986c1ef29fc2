import { useState } from 'react';
import {
  useCategoryStats,
  useTagsInCategory,
  useCategories,
  useMarketsInCategory,
  useCategoryWithMarkets,
  usePopularCategories,
} from '../../hooks/use-category';
import { MarketOrder } from '../../lib/api/category/category.dto';

// 카테고리 통계 컴포넌트
export function CategoryStats({ category }: { category: string }) {
  const { data: categoryStats, isLoading, error } = useCategoryStats(category);

  if (isLoading) return <div>Loading category stats...</div>;
  if (error) return <div>Error: {error.message}</div>;
  if (!categoryStats) return <div>Category not found</div>;

  return (
    <div className="category-stats">
      <div className="category-header">
        <h1>{(categoryStats as any).name}</h1>
        <p className="category-description">{(categoryStats as any).description}</p>
      </div>

      <div className="stats-grid">
        <div className="stat-item">
          <span className="stat-label">Total Markets</span>
          <span className="stat-value">{(categoryStats as any).totalMarkets || 0}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Active Markets</span>
          <span className="stat-value">{(categoryStats as any).activeMarkets || 0}</span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Total Volume</span>
          <span className="stat-value">
            ${((categoryStats as any).totalVolume as number)?.toLocaleString() || '0'}
          </span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Participants</span>
          <span className="stat-value">{(categoryStats as any).totalParticipants || 0}</span>
        </div>
      </div>
    </div>
  );
}

// 카테고리 태그 리스트 컴포넌트
export function CategoryTags({
  category,
  onTagSelect,
}: {
  category: string;
  onTagSelect?: (tag: string) => void;
}) {
  const [page, setPage] = useState(0);
  const { data: tagsData, isLoading } = useTagsInCategory(category, { page, limit: 20 });

  if (isLoading) return <div>Loading tags...</div>;

  const tags = (tagsData as any)?.tags || [];

  return (
    <div className="category-tags">
      <h3>Tags in {category}</h3>
      <div className="tags-grid">
        <button onClick={() => onTagSelect?.('All')} className="tag-button all-tag">
          All
        </button>
        {tags.map((tag: any) => (
          <button
            key={tag.id || tag.name}
            onClick={() => onTagSelect?.(tag.name)}
            className="tag-button"
          >
            {tag.name}
            {tag.count && <span className="tag-count">({tag.count})</span>}
          </button>
        ))}
      </div>

      {tags.length >= 20 && (
        <div className="pagination">
          <button onClick={() => setPage(p => Math.max(0, p - 1))} disabled={page === 0}>
            Previous
          </button>
          <span>Page {page + 1}</span>
          <button onClick={() => setPage(p => p + 1)}>Next</button>
        </div>
      )}
    </div>
  );
}

// 카테고리별 마켓 리스트 컴포넌트
export function CategoryMarkets({
  category,
  tag = 'All',
  order = 'VOLUME' as MarketOrder,
}: {
  category: string;
  tag?: string;
  order?: MarketOrder;
}) {
  const [page, setPage] = useState(0);
  const { data: marketsData, isLoading } = useMarketsInCategory(category, tag, {
    page,
    limit: 10,
    order,
  });

  if (isLoading) return <div>Loading markets...</div>;

  const markets = (marketsData as any)?.markets || [];

  return (
    <div className="category-markets">
      <h3>
        Markets in {category} {tag !== 'All' && `- ${tag}`}
      </h3>

      {markets.map((market: any) => (
        <div key={market.id} className="market-item">
          <div className="market-header">
            <h4>{market.title}</h4>
            <span className={`market-status ${market.status?.toLowerCase()}`}>{market.status}</span>
          </div>

          <p className="market-description">{market.description}</p>

          <div className="market-stats">
            <span>Volume: ${(market.volume as number)?.toLocaleString() || '0'}</span>
            <span>Participants: {market.participants || 0}</span>
            <span>Ends: {new Date(market.endDate).toLocaleDateString()}</span>
          </div>

          {market.outcomes && (
            <div className="market-outcomes">
              {market.outcomes.map((outcome: any) => (
                <div key={outcome.id} className="outcome-item">
                  <span>{outcome.name}</span>
                  <span className="outcome-odds">{outcome.odds}</span>
                </div>
              ))}
            </div>
          )}
        </div>
      ))}

      <div className="pagination">
        <button onClick={() => setPage(p => Math.max(0, p - 1))} disabled={page === 0}>
          Previous
        </button>
        <span>Page {page + 1}</span>
        <button onClick={() => setPage(p => p + 1)} disabled={!(marketsData as any)?.hasNext}>
          Next
        </button>
      </div>
    </div>
  );
}

// 카테고리 목록 컴포넌트
export function CategoriesList({
  onCategorySelect,
}: {
  onCategorySelect?: (category: string) => void;
}) {
  const [page, setPage] = useState(0);
  const { data: categoriesData, isLoading } = useCategories({ page, limit: 12 });

  if (isLoading) return <div>Loading categories...</div>;

  const categories = (categoriesData as any)?.categories || [];

  return (
    <div className="categories-list">
      <h2>Categories</h2>
      <div className="categories-grid">
        {categories.map((category: any) => (
          <div
            key={category.id || category.name}
            className="category-card"
            onClick={() => onCategorySelect?.(category.name)}
          >
            {category.image && (
              <img src={category.image} alt={category.name} className="category-image" />
            )}
            <div className="category-info">
              <h3>{category.name}</h3>
              <p>{category.description}</p>
              <div className="category-stats">
                <span>{category.marketCount || 0} markets</span>
                {category.volume && <span>${(category.volume as number).toLocaleString()}</span>}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="pagination">
        <button onClick={() => setPage(p => Math.max(0, p - 1))} disabled={page === 0}>
          Previous
        </button>
        <span>Page {page + 1}</span>
        <button onClick={() => setPage(p => p + 1)} disabled={!(categoriesData as any)?.hasNext}>
          Next
        </button>
      </div>
    </div>
  );
}

// 인기 카테고리 컴포넌트
export function PopularCategories({
  limit = 6,
  onCategorySelect,
}: {
  limit?: number;
  onCategorySelect?: (category: string) => void;
}) {
  const { data: popularData, isLoading } = usePopularCategories(limit);

  if (isLoading) return <div>Loading popular categories...</div>;

  const categories = (popularData as any)?.categories || [];

  return (
    <div className="popular-categories">
      <h2>Popular Categories</h2>
      <div className="popular-grid">
        {categories.map((category: any) => (
          <div
            key={category.id || category.name}
            className="popular-category-card"
            onClick={() => onCategorySelect?.(category.name)}
          >
            <div className="category-icon">
              {category.image ? (
                <img src={category.image} alt={category.name} />
              ) : (
                <div className="category-placeholder">{category.name.charAt(0)}</div>
              )}
            </div>
            <h3>{category.name}</h3>
            <p>{category.marketCount} markets</p>
          </div>
        ))}
      </div>
    </div>
  );
}

// 통합 카테고리 페이지 컴포넌트
export function CategoryPage({
  initialCategory,
  initialTag = 'All',
}: {
  initialCategory?: string;
  initialTag?: string;
}) {
  const [selectedCategory, setSelectedCategory] = useState(initialCategory || '');
  const [selectedTag, setSelectedTag] = useState(initialTag);
  const [sortOrder, setSortOrder] = useState<MarketOrder>('VOLUME');

  const { data: categoryWithMarkets, isLoading } = useCategoryWithMarkets(
    selectedCategory,
    selectedTag,
    { order: sortOrder, limit: 10 }
  );

  if (!selectedCategory) {
    return (
      <div className="category-page">
        <PopularCategories onCategorySelect={setSelectedCategory} />
        <CategoriesList onCategorySelect={setSelectedCategory} />
      </div>
    );
  }

  if (isLoading) return <div>Loading category page...</div>;

  return (
    <div className="category-page">
      <button onClick={() => setSelectedCategory('')} className="back-button">
        ← Back to Categories
      </button>

      <CategoryStats category={selectedCategory} />

      <div className="category-content">
        <div className="category-sidebar">
          <CategoryTags category={selectedCategory} onTagSelect={setSelectedTag} />

          <div className="sort-controls">
            <h4>Sort by:</h4>
            <select value={sortOrder} onChange={e => setSortOrder(e.target.value as MarketOrder)}>
              <option value="VOLUME">Volume</option>
              <option value="NEWEST">Newest</option>
              <option value="ENDING_SOON">Ending Soon</option>
              <option value="COMPETITIVE">Most Competitive</option>
            </select>
          </div>
        </div>

        <div className="category-main">
          <CategoryMarkets category={selectedCategory} tag={selectedTag} order={sortOrder} />
        </div>
      </div>
    </div>
  );
}

// 카테고리 브라우저 컴포넌트 (전체 통합)
export function CategoryBrowser() {
  const [view, setView] = useState<'grid' | 'category'>('grid');
  const [selectedCategory, setSelectedCategory] = useState<string>('');

  const handleCategorySelect = (category: string) => {
    setSelectedCategory(category);
    setView('category');
  };

  const handleBackToGrid = () => {
    setView('grid');
    setSelectedCategory('');
  };

  if (view === 'category' && selectedCategory) {
    return (
      <CategoryPage
        initialCategory={selectedCategory}
        key={selectedCategory} // Force remount when category changes
      />
    );
  }

  return (
    <div className="category-browser">
      <div className="browser-header">
        <h1>Explore Categories</h1>
        <p>Discover prediction markets across various topics</p>
      </div>

      <PopularCategories onCategorySelect={handleCategorySelect} />
      <CategoriesList onCategorySelect={handleCategorySelect} />
    </div>
  );
}
