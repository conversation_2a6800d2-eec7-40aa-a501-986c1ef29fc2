import { useState } from 'react';
import {
  useChannelById,
  useChannelLeaderboard,
  useChannelMarkets,
  useCollateral,
  useCollateralHistory,
  usePopularChannels,
  useActivePredictions,
  usePredictionHistory,
  useRewards,
  useRewardsHistory,
  useChannelSearch,
  useChannelOverview,
  useUserChannelDashboard,
  useUpdateChannel,
  useDepositToMarket,
  useSubscribeChannel,
  useUnsubscribeChannel,
  useToggleChannelSubscription,
} from '../../hooks/use-channel';
import {
  UpdateChannelRequestBody,
  ChannelPredictionDepositRequestBody,
  LeaderboardType,
  MarketStatus,
  MarketOrder,
  RewardsOrder,
} from '../../lib/api/channel/channel.dto';

// 채널 정보 컴포넌트
export function ChannelInfo({ channelId }: { channelId: string }) {
  const { data: channelInfo, isLoading, error } = useChannelById(channelId);

  if (isLoading) return <div>Loading channel info...</div>;
  if (error) return <div>Error: {error.message}</div>;
  if (!channelInfo) return <div>Channel not found</div>;

  return (
    <div className="channel-info">
      <div className="channel-header">
        {(channelInfo as any).image && (
          <img src={(channelInfo as any).image} alt="Channel" className="channel-image" />
        )}
        <div className="channel-details">
          <h1>{(channelInfo as any).name}</h1>
          <p className="channel-description">{(channelInfo as any).description}</p>
          <div className="channel-stats">
            <span>{(channelInfo as any).subscriberCount || 0} subscribers</span>
            <span>{(channelInfo as any).marketCount || 0} markets</span>
            <span>
              ${((channelInfo as any).totalVolume as number)?.toLocaleString() || '0'} volume
            </span>
          </div>
        </div>
      </div>

      <div className="channel-meta">
        <p>Created: {new Date((channelInfo as any).createdAt).toLocaleDateString()}</p>
        <p>Creator: {(channelInfo as any).creatorAddress}</p>
        {(channelInfo as any).tags && (
          <div className="channel-tags">
            {(channelInfo as any).tags.map((tag: string) => (
              <span key={tag} className="tag">
                {tag}
              </span>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}

// 채널 리더보드 컴포넌트
export function ChannelLeaderboard({
  channelId,
  type = 'volume',
}: {
  channelId: string;
  type?: LeaderboardType;
}) {
  const { data: leaderboard, isLoading } = useChannelLeaderboard(channelId, type);

  if (isLoading) return <div>Loading leaderboard...</div>;

  const leaders = (leaderboard as any)?.leaders || [];

  return (
    <div className="channel-leaderboard">
      <h3>Leaderboard ({type})</h3>
      <div className="leaderboard-list">
        {leaders.map((leader: any, index: number) => (
          <div key={leader.address} className="leader-item">
            <span className="rank">#{index + 1}</span>
            <div className="leader-info">
              <span className="leader-name">{leader.nickname || leader.address}</span>
              <span className="leader-stat">
                {type === 'volume'
                  ? `$${(leader.volume as number).toLocaleString()}`
                  : `$${(leader.profit as number).toLocaleString()}`}
              </span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// 채널 마켓 리스트 컴포넌트
export function ChannelMarkets({
  channelId,
  status = 'live',
  order = 'volume',
}: {
  channelId: string;
  status?: MarketStatus;
  order?: MarketOrder;
}) {
  const [page, setPage] = useState(0);
  const { data: marketsData, isLoading } = useChannelMarkets(channelId, {
    status,
    order,
    page,
    limit: 10,
  });

  if (isLoading) return <div>Loading markets...</div>;

  const markets = (marketsData as any)?.markets || [];

  return (
    <div className="channel-markets">
      <h3>Markets ({status})</h3>
      {markets.map((market: any) => (
        <div key={market.id} className="market-item">
          <div className="market-header">
            <h4>{market.title}</h4>
            <span className={`market-status ${market.status?.toLowerCase()}`}>{market.status}</span>
          </div>
          <p className="market-description">{market.description}</p>
          <div className="market-stats">
            <span>Volume: ${(market.volume as number)?.toLocaleString() || '0'}</span>
            <span>Participants: {market.participants || 0}</span>
            <span>Ends: {new Date(market.endDate).toLocaleDateString()}</span>
          </div>
        </div>
      ))}

      <div className="pagination">
        <button onClick={() => setPage(p => Math.max(0, p - 1))} disabled={page === 0}>
          Previous
        </button>
        <span>Page {page + 1}</span>
        <button onClick={() => setPage(p => p + 1)} disabled={!(marketsData as any)?.hasNext}>
          Next
        </button>
      </div>
    </div>
  );
}

// 담보 통계 컴포넌트
export function CollateralStats() {
  const { data: collateral, isLoading } = useCollateral();

  if (isLoading) return <div>Loading collateral stats...</div>;

  return (
    <div className="collateral-stats">
      <h3>Collateral Statistics</h3>
      <div className="stats-grid">
        <div className="stat-item">
          <span className="stat-label">Total Collateral</span>
          <span className="stat-value">
            ${((collateral as any)?.totalCollateral as number)?.toLocaleString() || '0'}
          </span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Available</span>
          <span className="stat-value">
            ${((collateral as any)?.availableCollateral as number)?.toLocaleString() || '0'}
          </span>
        </div>
        <div className="stat-item">
          <span className="stat-label">Locked</span>
          <span className="stat-value">
            ${((collateral as any)?.lockedCollateral as number)?.toLocaleString() || '0'}
          </span>
        </div>
      </div>
    </div>
  );
}

// 담보 이력 컴포넌트
export function CollateralHistory() {
  const [page, setPage] = useState(0);
  const { data: historyData, isLoading } = useCollateralHistory({ page, limit: 10 });

  if (isLoading) return <div>Loading collateral history...</div>;

  const history = (historyData as any)?.history || [];

  return (
    <div className="collateral-history">
      <h3>Collateral History</h3>
      {history.map((item: any) => (
        <div key={item.id} className="history-item">
          <div className="history-type">{item.type}</div>
          <div className="history-details">
            <span>Amount: ${(item.amount as number).toLocaleString()}</span>
            <span>Date: {new Date(item.timestamp).toLocaleDateString()}</span>
          </div>
        </div>
      ))}

      <div className="pagination">
        <button onClick={() => setPage(p => Math.max(0, p - 1))} disabled={page === 0}>
          Previous
        </button>
        <span>Page {page + 1}</span>
        <button onClick={() => setPage(p => p + 1)} disabled={!(historyData as any)?.hasNext}>
          Next
        </button>
      </div>
    </div>
  );
}

// 인기 채널 컴포넌트
export function PopularChannels({
  onChannelSelect,
}: {
  onChannelSelect?: (channelId: string) => void;
}) {
  const { data: popularData, isLoading } = usePopularChannels();

  if (isLoading) return <div>Loading popular channels...</div>;

  const channels = (popularData as any)?.channels || [];

  return (
    <div className="popular-channels">
      <h3>Popular Channels</h3>
      <div className="channels-grid">
        {channels.map((channel: any) => (
          <div
            key={channel.id}
            className="channel-card"
            onClick={() => onChannelSelect?.(channel.id)}
          >
            {channel.image && <img src={channel.image} alt={channel.name} />}
            <div className="channel-info">
              <h4>{channel.name}</h4>
              <p>{channel.description}</p>
              <div className="channel-stats">
                <span>{channel.subscriberCount} subscribers</span>
                <span>{channel.marketCount} markets</span>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

// 활성 예측 컴포넌트
export function ActivePredictions() {
  const [page, setPage] = useState(0);
  const { data: predictionsData, isLoading } = useActivePredictions({ page, limit: 10 });

  if (isLoading) return <div>Loading active predictions...</div>;

  const predictions = (predictionsData as any)?.predictions || [];

  return (
    <div className="active-predictions">
      <h3>Active Predictions</h3>
      {predictions.map((prediction: any) => (
        <div key={prediction.id} className="prediction-item">
          <div className="prediction-info">
            <h4>{prediction.marketTitle}</h4>
            <span>Outcome: {prediction.outcome}</span>
            <span>Amount: ${(prediction.amount as number).toLocaleString()}</span>
          </div>
          <div className="prediction-status">
            <span className="current-value">
              Current Value: ${(prediction.currentValue as number).toLocaleString()}
            </span>
            <span
              className={`pnl ${
                (prediction.currentValue as number) >= (prediction.amount as number)
                  ? 'positive'
                  : 'negative'
              }`}
            >
              PnL: $
              {(
                (prediction.currentValue as number) - (prediction.amount as number)
              ).toLocaleString()}
            </span>
          </div>
        </div>
      ))}

      <div className="pagination">
        <button onClick={() => setPage(p => Math.max(0, p - 1))} disabled={page === 0}>
          Previous
        </button>
        <span>Page {page + 1}</span>
        <button onClick={() => setPage(p => p + 1)} disabled={!(predictionsData as any)?.hasNext}>
          Next
        </button>
      </div>
    </div>
  );
}

// 리워드 컴포넌트
export function Rewards() {
  const { data: rewards, isLoading } = useRewards();

  if (isLoading) return <div>Loading rewards...</div>;

  return (
    <div className="rewards">
      <h3>Available Rewards</h3>
      <div className="rewards-summary">
        <div className="reward-item">
          <span className="reward-label">Total Earned</span>
          <span className="reward-value">
            ${((rewards as any)?.totalEarned as number)?.toLocaleString() || '0'}
          </span>
        </div>
        <div className="reward-item">
          <span className="reward-label">Available</span>
          <span className="reward-value">
            ${((rewards as any)?.availableRewards as number)?.toLocaleString() || '0'}
          </span>
        </div>
        <div className="reward-item">
          <span className="reward-label">Claimed</span>
          <span className="reward-value">
            ${((rewards as any)?.claimedRewards as number)?.toLocaleString() || '0'}
          </span>
        </div>
      </div>
    </div>
  );
}

// 채널 검색 컴포넌트
export function ChannelSearch({
  onChannelSelect,
}: {
  onChannelSelect?: (channelId: string) => void;
}) {
  const [query, setQuery] = useState('');
  const [page, setPage] = useState(0);
  const { data: searchResults, isLoading } = useChannelSearch(query, { page, limit: 10 });

  return (
    <div className="channel-search">
      <h3>Search Channels</h3>
      <input
        type="text"
        placeholder="Search channels..."
        value={query}
        onChange={e => {
          setQuery(e.target.value);
          setPage(0);
        }}
        className="search-input"
      />

      {isLoading && <div>Searching...</div>}

      {searchResults && (
        <div className="search-results">
          {(searchResults as any).channels?.map((channel: any) => (
            <div
              key={channel.id}
              className="search-result-item"
              onClick={() => onChannelSelect?.(channel.id)}
            >
              <h4>{channel.name}</h4>
              <p>{channel.description}</p>
              <div className="channel-stats">
                <span>{channel.subscriberCount} subscribers</span>
                <span>{channel.marketCount} markets</span>
              </div>
            </div>
          ))}

          {(searchResults as any).channels?.length > 0 && (
            <div className="pagination">
              <button onClick={() => setPage(p => Math.max(0, p - 1))} disabled={page === 0}>
                Previous
              </button>
              <span>Page {page + 1}</span>
              <button
                onClick={() => setPage(p => p + 1)}
                disabled={!(searchResults as any)?.hasNext}
              >
                Next
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
}

// 채널 구독 버튼 컴포넌트
export function ChannelSubscribeButton({
  channelId,
  isSubscribed,
}: {
  channelId: string;
  isSubscribed: boolean;
}) {
  const toggleSubscriptionMutation = useToggleChannelSubscription();

  const handleToggleSubscription = async () => {
    try {
      await toggleSubscriptionMutation.mutateAsync({
        channelId,
        isCurrentlySubscribed: isSubscribed,
      });
    } catch (error) {
      console.error('Toggle subscription failed:', error);
    }
  };

  return (
    <button
      onClick={handleToggleSubscription}
      disabled={toggleSubscriptionMutation.isPending}
      className={`subscribe-button ${isSubscribed ? 'subscribed' : 'unsubscribed'}`}
    >
      {toggleSubscriptionMutation.isPending
        ? 'Processing...'
        : isSubscribed
          ? 'Unsubscribe'
          : 'Subscribe'}
    </button>
  );
}

// 예치금 입금 폼 컴포넌트
export function DepositForm() {
  const depositMutation = useDepositToMarket();
  const [formData, setFormData] = useState<ChannelPredictionDepositRequestBody>({
    marketId: '',
    amount: 0,
    signature: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      await depositMutation.mutateAsync(formData);
      alert('Deposit successful!');
      setFormData({ marketId: '', amount: 0, signature: '' });
    } catch (error) {
      console.error('Deposit failed:', error);
      alert('Failed to deposit');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="deposit-form">
      <h3>Deposit to Market</h3>

      <input
        type="text"
        placeholder="Market ID"
        value={formData.marketId}
        onChange={e => setFormData(prev => ({ ...prev, marketId: e.target.value }))}
        required
      />

      <input
        type="number"
        placeholder="Amount"
        value={formData.amount || ''}
        onChange={e => setFormData(prev => ({ ...prev, amount: Number(e.target.value) }))}
        required
      />

      <input
        type="text"
        placeholder="Signature"
        value={formData.signature}
        onChange={e => setFormData(prev => ({ ...prev, signature: e.target.value }))}
        required
      />

      <button type="submit" disabled={depositMutation.isPending}>
        {depositMutation.isPending ? 'Depositing...' : 'Deposit'}
      </button>
    </form>
  );
}

// 통합 채널 대시보드
export function ChannelDashboard() {
  const { data: dashboardData, isLoading } = useUserChannelDashboard();

  if (isLoading) return <div>Loading dashboard...</div>;

  return (
    <div className="channel-dashboard">
      <h2>Channel Dashboard</h2>

      <div className="dashboard-grid">
        <div className="dashboard-section">
          <CollateralStats />
        </div>

        <div className="dashboard-section">
          <Rewards />
        </div>

        <div className="dashboard-section">
          <ActivePredictions />
        </div>

        <div className="dashboard-section">
          <PopularChannels />
        </div>
      </div>
    </div>
  );
}

// 채널 상세 페이지 컴포넌트
export function ChannelDetailPage({ channelId }: { channelId: string }) {
  const [activeTab, setActiveTab] = useState<'info' | 'markets' | 'leaderboard'>('info');
  const [leaderboardType, setLeaderboardType] = useState<LeaderboardType>('volume');
  const [marketStatus, setMarketStatus] = useState<MarketStatus>('live');

  const { data: channelOverview, isLoading } = useChannelOverview(channelId, leaderboardType);

  if (isLoading) return <div>Loading channel...</div>;

  return (
    <div className="channel-detail-page">
      <ChannelInfo channelId={channelId} />

      <div className="channel-subscription">
        <ChannelSubscribeButton
          channelId={channelId}
          isSubscribed={(channelOverview as any)?.channelInfo?.isSubscribed || false}
        />
      </div>

      <nav className="channel-nav">
        <button
          onClick={() => setActiveTab('info')}
          className={activeTab === 'info' ? 'active' : ''}
        >
          Overview
        </button>
        <button
          onClick={() => setActiveTab('markets')}
          className={activeTab === 'markets' ? 'active' : ''}
        >
          Markets
        </button>
        <button
          onClick={() => setActiveTab('leaderboard')}
          className={activeTab === 'leaderboard' ? 'active' : ''}
        >
          Leaderboard
        </button>
      </nav>

      <div className="channel-content">
        {activeTab === 'info' && (
          <div>
            <ChannelMarkets channelId={channelId} status="live" />
          </div>
        )}

        {activeTab === 'markets' && (
          <div>
            <div className="market-controls">
              <select
                value={marketStatus}
                onChange={e => setMarketStatus(e.target.value as MarketStatus)}
              >
                <option value="live">Live Markets</option>
                <option value="ended">Ended Markets</option>
              </select>
            </div>
            <ChannelMarkets channelId={channelId} status={marketStatus} />
          </div>
        )}

        {activeTab === 'leaderboard' && (
          <div>
            <div className="leaderboard-controls">
              <select
                value={leaderboardType}
                onChange={e => setLeaderboardType(e.target.value as LeaderboardType)}
              >
                <option value="volume">By Volume</option>
                <option value="profit">By Profit</option>
              </select>
            </div>
            <ChannelLeaderboard channelId={channelId} type={leaderboardType} />
          </div>
        )}
      </div>
    </div>
  );
}
