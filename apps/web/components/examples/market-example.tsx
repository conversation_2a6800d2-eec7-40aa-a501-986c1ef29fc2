import { useState } from 'react';
import { useMarket, useMarkets, useSearchMarkets, useCreateMarket } from '../../hooks/use-market';
import { CreateMarketRequest } from '../../lib/api/market/market.schema';

// 마켓 상세 컴포넌트
export function MarketDetail({ marketId }: { marketId: string }) {
  const { data: market, isLoading, error } = useMarket(marketId);

  if (isLoading) return <div>Loading market...</div>;
  if (error) return <div>Error: {error.message}</div>;
  if (!market) return <div>Market not found</div>;

  return (
    <div className="market-detail">
      <h1>{market.title}</h1>
      <p>{market.description}</p>
      <div className="market-info">
        <span>Category: {market.category}</span>
        <span>Status: {market.status}</span>
        <span>Volume: ${market.totalVolume.toLocaleString()}</span>
        <span>Participants: {market.participantCount}</span>
      </div>
      <div className="outcomes">
        <h3>Outcomes:</h3>
        {market.outcomes.map((outcome, index) => (
          <div key={index} className="outcome">
            {outcome}
          </div>
        ))}
      </div>
    </div>
  );
}

// 마켓 목록 컴포넌트
export function MarketList() {
  const [page, setPage] = useState(0);
  const { data: marketsData, isLoading } = useMarkets({ page, limit: 10 });

  if (isLoading) return <div>Loading markets...</div>;

  return (
    <div className="market-list">
      <h2>Markets</h2>
      {marketsData?.markets.map(market => (
        <div key={market.id} className="market-card">
          <h3>{market.title}</h3>
          <p>Category: {market.category}</p>
          <p>Volume: ${market.totalVolume.toLocaleString()}</p>
          <p>Participants: {market.participantCount}</p>
        </div>
      ))}

      <div className="pagination">
        <button onClick={() => setPage(p => Math.max(0, p - 1))} disabled={page === 0}>
          Previous
        </button>
        <span>Page {page + 1}</span>
        <button onClick={() => setPage(p => p + 1)} disabled={!marketsData?.hasNext}>
          Next
        </button>
      </div>
    </div>
  );
}

// 마켓 검색 컴포넌트
export function MarketSearch() {
  const [query, setQuery] = useState('');
  const { data: searchResults, isLoading } = useSearchMarkets(query, { page: 0, limit: 5 });

  return (
    <div className="market-search">
      <input
        type="text"
        placeholder="Search markets..."
        value={query}
        onChange={e => setQuery(e.target.value)}
        className="search-input"
      />

      {isLoading && query.length >= 2 && <div>Searching...</div>}

      {searchResults && (
        <div className="search-results">
          <h3>Search Results ({searchResults.totalLength})</h3>
          {searchResults.markets.map(market => (
            <div key={market.id} className="search-result">
              <h4>{market.title}</h4>
              <p>{market.category}</p>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

// 마켓 생성 컴포넌트
export function CreateMarketForm() {
  const createMarketMutation = useCreateMarket();
  const [formData, setFormData] = useState<Partial<CreateMarketRequest>>({
    title: '',
    description: '',
    category: '',
    outcomes: ['', ''],
    endDate: '',
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.title || !formData.description || !formData.category || !formData.endDate) {
      alert('Please fill in all required fields');
      return;
    }

    const validOutcomes = formData.outcomes?.filter(outcome => outcome.trim()) || [];
    if (validOutcomes.length < 2) {
      alert('Please provide at least 2 outcomes');
      return;
    }

    try {
      await createMarketMutation.mutateAsync({
        title: formData.title,
        description: formData.description,
        category: formData.category,
        outcomes: validOutcomes,
        endDate: formData.endDate,
        imageUrl: formData.imageUrl,
        tags: formData.tags || [],
      });

      alert('Market created successfully!');
      // Reset form
      setFormData({
        title: '',
        description: '',
        category: '',
        outcomes: ['', ''],
        endDate: '',
      });
    } catch (error) {
      alert('Failed to create market');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="create-market-form">
      <h2>Create New Market</h2>

      <input
        type="text"
        placeholder="Market Title"
        value={formData.title || ''}
        onChange={e => setFormData(prev => ({ ...prev, title: e.target.value }))}
        required
      />

      <textarea
        placeholder="Market Description"
        value={formData.description || ''}
        onChange={e => setFormData(prev => ({ ...prev, description: e.target.value }))}
        required
      />

      <input
        type="text"
        placeholder="Category"
        value={formData.category || ''}
        onChange={e => setFormData(prev => ({ ...prev, category: e.target.value }))}
        required
      />

      <input
        type="datetime-local"
        value={formData.endDate || ''}
        onChange={e => setFormData(prev => ({ ...prev, endDate: e.target.value }))}
        required
      />

      <div className="outcomes-section">
        <h3>Outcomes</h3>
        {formData.outcomes?.map((outcome, index) => (
          <input
            key={index}
            type="text"
            placeholder={`Outcome ${index + 1}`}
            value={outcome}
            onChange={e => {
              const newOutcomes = [...(formData.outcomes || [])];
              newOutcomes[index] = e.target.value;
              setFormData(prev => ({ ...prev, outcomes: newOutcomes }));
            }}
          />
        ))}
        <button
          type="button"
          onClick={() =>
            setFormData(prev => ({
              ...prev,
              outcomes: [...(prev.outcomes || []), ''],
            }))
          }
        >
          Add Outcome
        </button>
      </div>

      <button type="submit" disabled={createMarketMutation.isPending}>
        {createMarketMutation.isPending ? 'Creating...' : 'Create Market'}
      </button>
    </form>
  );
}
