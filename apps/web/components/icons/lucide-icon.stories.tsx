import type { Meta, StoryObj } from '@storybook/react';
import { LucideIcon as Icon } from './lucide-icon';

const meta: Meta<typeof Icon> = {
  title: 'Common/Icons/LucideIcon',
  component: Icon,
  argTypes: {
    name: {
      control: { type: 'select' },
      // Provide a subset of common icons for the control for easier testing
      options: ['home', 'settings', 'user', 'search', 'chevron-left', 'chevron-right', 'x'],
    },
    size: { control: 'number' },
    color: { control: 'color' },
    strokeWidth: { control: { type: 'range', min: 0.5, max: 3, step: 0.5 } },
    className: { control: 'text' },
  },
  args: {
    name: 'home', // Default icon
    size: 24,
  },
};

export default meta;
type Story = StoryObj<typeof Icon>;

export const Default: Story = {
  args: {
    name: 'home',
  },
};

export const Settings: Story = {
  args: {
    name: 'settings',
    size: 32,
    className: 'text-blue-500',
  },
};

export const CustomStyle: Story = {
  args: {
    name: 'search',
    size: 48,
    color: 'red',
    strokeWidth: 1,
  },
};

export const NonExistent: Story = {
  args: {
    // @ts-expect-error Testing non-existent icon handling
    name: 'non-existent-icon',
  },
};
