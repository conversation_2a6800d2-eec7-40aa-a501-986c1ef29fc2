import type { <PERSON>a, StoryObj } from '@storybook/react';
import { SmIcon, SmIconName } from './sm-icon';

const smIconNames: SmIconName[] = [
  'support',
  'share_bonus',
  'subscription',
  'settings',
  'referral',
  'logout',
  'positions',
  'documentation',
  'headphones',
  'calendar',
  'channel',
  'chat',
  'bonus_reward',
];

const meta: Meta<typeof SmIcon> = {
  title: 'Common/Icons/SmallIcon',
  component: SmIcon,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    name: {
      control: { type: 'select' },
      options: smIconNames,
      description: 'Name of the icon to display',
    },
    size: {
      control: { type: 'number' },
      description: 'Size of the icon in pixels',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Story to display all available icons
export const AllIcons: Story = {
  render: args => (
    <div
      style={{
        display: 'flex',
        gap: '1rem',
        flexWrap: 'wrap',
        justifyContent: 'center',
        maxWidth: '400px',
      }}
    >
      {smIconNames.map(name => (
        <div key={name} style={{ textAlign: 'center' }}>
          <SmIcon {...args} name={name} />
          <p style={{ marginTop: '0.5rem', fontSize: '0.8rem' }}>{name}</p>
        </div>
      ))}
    </div>
  ),
  args: {
    size: 24, // Smaller default size for the grid view
  },
};

// Base story
export const Default: Story = {
  args: {
    name: 'settings', // Default icon
    size: 24,
  },
};

// Individual stories
export const LogoutLarge: Story = {
  args: {
    name: 'logout',
    size: 48,
  },
};

export const SupportStyled: Story = {
  args: {
    name: 'support',
    size: 32,
    className: 'text-orange-500',
  },
};
