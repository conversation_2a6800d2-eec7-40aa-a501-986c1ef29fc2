import type { <PERSON>a, StoryObj } from '@storybook/react';
import { XlIcon, XlIconName } from './xl-icon';

const meta: Meta<typeof XlIcon> = {
  title: 'Common/Icons/ExtraLargeIcon',
  component: XlIcon,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    name: {
      control: {
        type: 'select',
        options: ['usdc', 'notice', 'mail', 'check', 'alert', 'wallet'],
      },
      description: 'Name of the icon to display',
    },
    size: {
      control: { type: 'number' },
      description: 'Size of the icon in pixels',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
    // Add descriptions for other props if needed
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Base story
export const Default: Story = {
  args: {
    name: 'usdc', // Default icon
    size: 24,
  },
};

// Individual stories for each icon type
const iconNames: XlIconName[] = ['usdc', 'notice', 'mail', 'check', 'alert', 'wallet'];

export const AllIcons: Story = {
  render: args => (
    <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
      {iconNames.map(name => (
        <div key={name} style={{ textAlign: 'center' }}>
          <XlIcon {...args} name={name} />
          <p style={{ marginTop: '0.5rem', fontSize: '0.8rem' }}>{name}</p>
        </div>
      ))}
    </div>
  ),
  args: {
    size: 32,
  },
};

export const Usdc: Story = { args: { name: 'usdc', size: 40 } };
export const Notice: Story = { args: { name: 'notice', size: 40 } };
export const Mail: Story = { args: { name: 'mail', size: 40 } };
export const Check: Story = { args: { name: 'check', size: 40 } };
export const Alert: Story = { args: { name: 'alert', size: 40 } };
export const Wallet: Story = { args: { name: 'wallet', size: 40 } };

export const CustomSize: Story = {
  args: {
    name: 'wallet',
    size: 64,
  },
};

export const CustomClass: Story = {
  args: {
    name: 'check',
    size: 32,
    className: 'text-blue-500', // Example using Tailwind class
  },
};
