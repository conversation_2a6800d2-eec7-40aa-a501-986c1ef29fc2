import React from 'react';
import { cn } from '@repo/ui/lib/utils';
import Image from 'next/image';

// Union type based on filenames in public/assets/icons/xl-icons
export type XlIconName = 'usdc' | 'notice' | 'mail' | 'check' | 'alert' | 'wallet';

interface XlIconProps {
  name: XlIconName;
  size?: number;
  className?: string;
  alt?: string;
  priority?: boolean;
  quality?: number;
  loading?: 'lazy' | 'eager';
  placeholder?: 'blur' | 'empty';
  style?: React.CSSProperties;
  onLoad?: () => void;
  onError?: () => void;
}

export function XlIcon({
  name,
  size = 50, // Default size, can be overridden
  className,
  alt,
  ...props
}: XlIconProps) {
  const src = `/assets/icons/xl-icons/${name}.svg`;

  return (
    <Image
      src={src}
      alt={alt || `${name} icon`}
      className={cn(className)}
      width={size}
      height={size}
      {...props}
    />
  );
}

XlIcon.displayName = 'XlIcon';
