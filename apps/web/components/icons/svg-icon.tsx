import * as Icons from './assets';
import { SVGProps } from 'react';

type IconsType = typeof Icons;
type IconName = keyof IconsType;

interface SvgIconProps extends SVGProps<SVGSVGElement> {
  name: IconName;
  className?: string;
}

export default function SvgIcon({ name, className = '', ...props }: SvgIconProps) {
  const IconComponent = Icons[name];
  return <IconComponent className={className} {...props} />;
}
