import React from 'react';
import dynamic from 'next/dynamic';
import { LucideProps } from 'lucide-react';
import dynamicIconImports from 'lucide-react/dynamicIconImports';
import { cn } from '@repo/ui/lib/utils';

interface IconProps extends LucideProps {
  name: keyof typeof dynamicIconImports;
  className?: string;
}

export const LucideIcon = ({ name, className, ...props }: IconProps) => {
  const LucideIcon = dynamic(dynamicIconImports[name]);

  if (!LucideIcon) {
    console.error(`Lucide Icon not found: ${name}`);
    return null;
  }

  return <LucideIcon className={cn(className)} {...props} />;
};
