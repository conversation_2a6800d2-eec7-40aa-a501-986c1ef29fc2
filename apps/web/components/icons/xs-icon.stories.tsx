import type { <PERSON>a, StoryObj } from '@storybook/react';
import { XsIcon, XsIconName } from './xs-icon';

const xsIconNames: XsIconName[] = [
  'heart',
  'member',
  'time',
  'crown',
  'excalmation',
  'external_link',
  'copy',
];

const meta: Meta<typeof XsIcon> = {
  title: 'Common/Icons/ExtraSmallIcon',
  component: XsIcon,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    name: {
      control: { type: 'select' },
      options: xsIconNames,
      description: 'Name of the icon to display',
    },
    size: {
      control: { type: 'number' },
      description: 'Size of the icon in pixels',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Story to display all available icons
export const AllIcons: Story = {
  render: args => (
    <div
      style={{
        display: 'flex',
        gap: '1rem',
        flexWrap: 'wrap',
        justifyContent: 'center',
      }}
    >
      {xsIconNames.map(name => (
        <div key={name} style={{ textAlign: 'center' }}>
          <XsIcon {...args} name={name} />
          <p style={{ marginTop: '0.5rem', fontSize: '0.8rem' }}>{name}</p>
        </div>
      ))}
    </div>
  ),
  args: {
    size: 20, // Slightly larger size for the grid view
  },
};

// Base story
export const Default: Story = {
  args: {
    name: 'heart', // Default icon
    size: 16,
  },
};

// Individual stories
export const CrownLarge: Story = {
  args: {
    name: 'crown',
    size: 32,
  },
};

export const CopyStyled: Story = {
  args: {
    name: 'copy',
    size: 24,
    className: 'text-gray-500',
  },
};
