import React from 'react';
import { cn } from '@repo/ui/lib/utils';
import Image from 'next/image';

// Union type based on filenames in public/assets/icons/sort-icons
export type SortIconName =
  | 'volume'
  | 'trending'
  | 'newest'
  | 'liquidity'
  | 'ending_soon'
  | 'competitive';

interface SortIconProps {
  name: SortIconName;
  size?: number;
  className?: string;
  alt?: string;
  priority?: boolean;
  quality?: number;
  loading?: 'lazy' | 'eager';
  placeholder?: 'blur' | 'empty';
  style?: React.CSSProperties;
  onLoad?: () => void;
  onError?: () => void;
}

export function SortIcon({
  name,
  size = 20, // Default size, can be overridden
  className,
  ...props
}: SortIconProps) {
  const src = `/assets/icons/sort-icons/${name}.svg`;

  return (
    <Image
      src={src}
      alt={`${name} icon`}
      className={cn(className)}
      width={size}
      height={size}
      {...props}
    />
  );
}

SortIcon.displayName = 'SortIcon';
