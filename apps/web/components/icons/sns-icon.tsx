import React from 'react';
import { cn } from '@repo/ui/lib/utils';
import Image from 'next/image';

// Union type based on filenames in public/assets/icons/sns-icons
export type SnsIconName =
  | 'youtube'
  | 'x'
  | 'tiktok'
  | 'telegram'
  | 'instagram'
  | 'facebook'
  | 'discord'
  | 'copylink'
  | 'abstract';

interface SnsIconProps {
  name: SnsIconName;
  size?: number;
  className?: string;
  alt?: string;
  priority?: boolean;
  quality?: number;
  loading?: 'lazy' | 'eager';
  placeholder?: 'blur' | 'empty';
  style?: React.CSSProperties;
  onLoad?: () => void;
  onError?: () => void;
}

export function SnsIcon({ name, size = 20, className, ...props }: SnsIconProps) {
  const src = `/assets/icons/sns-icons/${name}.svg`;

  return (
    <Image
      src={src}
      alt={`${name} icon`}
      className={cn(className)}
      width={size}
      height={size}
      {...props}
    />
  );
}

SnsIcon.displayName = 'SnsIcon';
