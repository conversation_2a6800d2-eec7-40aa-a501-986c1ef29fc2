import type { Meta, StoryObj } from '@storybook/react';
import { SnsIcon, SnsIconName } from './sns-icon';

// Helper to get the list of icon names for controls
const snsIconNames: SnsIconName[] = [
  'youtube',
  'x',
  'tiktok',
  'telegram',
  'instagram',
  'facebook',
  'discord',
  'copylink',
  'abstract',
];

const meta: Meta<typeof SnsIcon> = {
  title: 'Common/Icons/SocialIcon',
  component: SnsIcon,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    name: {
      control: { type: 'select' },
      options: snsIconNames,
    },
    size: { control: 'number' },
    className: { control: 'text' },
    // Add controls for other next/image props if needed
    priority: { control: 'boolean' },
    quality: { control: { type: 'range', min: 1, max: 100, step: 1 } },
  },
  args: {
    name: 'facebook', // Default icon
    size: 32,
  },
};

export default meta;
type Story = StoryObj<typeof SnsIcon>;

// Story to display all available icons
export const AllIcons: Story = {
  render: args => (
    <div style={{ display: 'flex', gap: '1rem', flexWrap: 'wrap' }}>
      {snsIconNames.map(name => (
        <div key={name} style={{ textAlign: 'center' }}>
          <SnsIcon {...args} name={name} />
          <p style={{ marginTop: '0.5rem', fontSize: '0.8rem' }}>{name}</p>
        </div>
      ))}
    </div>
  ),
  args: {
    size: 32, // Default size for the grid view
  },
};

export const Default: Story = {
  args: {
    name: 'facebook',
    priority: true,
  },
};

export const YoutubeLarge: Story = {
  args: {
    name: 'youtube',
    size: 64,
    className: 'bg-red-100 p-2 rounded-lg',
  },
};

export const XStyled: Story = {
  args: {
    name: 'x',
    size: 48,
    className: 'border-2 border-black p-1',
  },
};

// Add more stories as needed for different icons and props
export const InstagramPriority: Story = {
  args: {
    name: 'instagram',
    size: 40,
    priority: true,
  },
};
