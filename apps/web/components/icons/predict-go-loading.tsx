'use client';

import { CSSProperties } from 'react';

interface PredictGoLoadingProps {
  size?: 'small' | 'medium' | 'large' | 'xl' | '2xl' | '3xl';
  className?: string;
}

export default function PredictGoLoading({
  size = 'medium',
  className = '',
}: PredictGoLoadingProps) {
  const sizeClasses = {
    small: 'w-6 h-6',
    medium: 'w-10 h-10',
    large: 'w-16 h-16',
    xl: 'w-20 h-20',
    '2xl': 'w-24 h-24',
    '3xl': 'w-32 h-32',
  };

  // CSS-in-JS 스타일 정의
  const containerStyle: CSSProperties = {
    display: 'inline-block',
  };

  const starLargeStyle: CSSProperties = {
    transformOrigin: '17.3px 12.4px', // 큰 별의 중심점
    animation: 'predict-starRotate 3s linear infinite',
  };

  const starSmallStyle: CSSProperties = {
    transformOrigin: '23.4px 5.7px', // 작은 별의 중심점
    animation: 'predict-starRotate 2s linear infinite reverse',
  };

  return (
    <>
      <style jsx global>{`
        @keyframes predict-starRotate {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }

        @keyframes predict-gradientRotate {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }
      `}</style>

      <div className={`${sizeClasses[size]} ${className}`} style={containerStyle}>
        <svg
          width="100%"
          height="100%"
          viewBox="0 0 30 30"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <path
            d="M25.5882 17.9498V13.0839C25.5882 12.6159 25.7742 12.167 26.1051 11.8361L27.1702 10.771C27.726 10.2151 28.6765 10.6088 28.6765 11.3949V19.7145C28.6765 20.6506 28.3046 21.5483 27.6427 22.2102L24.8573 24.9957C24.1954 25.6576 23.2977 26.0294 22.3616 26.0294H4.85294C2.9037 26.0294 1.32353 24.4492 1.32353 22.5V9.70588C1.32353 7.75664 2.9037 6.17647 4.85294 6.17647H10.2228C11.0088 6.17647 11.4025 7.12689 10.8467 7.68274L9.78158 8.74783C9.45063 9.07878 9.00177 9.26471 8.53374 9.26471H5.73529C5.00433 9.26471 4.41176 9.85727 4.41176 10.5882V21.6176C4.41176 22.3486 5.00433 22.9412 5.73529 22.9412H20.5969C21.5329 22.9412 22.4307 22.5693 23.0926 21.9074L24.5545 20.4455C25.2164 19.7836 25.5882 18.8859 25.5882 17.9498Z"
            fill="url(#paint0_linear_1_10)"
          />
          <g style={starLargeStyle}>
            <path
              d="M16.5565 8.01969C16.0259 7.39135 15 7.76656 15 8.58897V10.8912C15 11.1471 14.8889 11.3904 14.6955 11.558L12.8169 13.1862C12.1996 13.7211 12.578 14.7353 13.3948 14.7353H15.793C16.0475 14.7353 16.2897 14.8452 16.4572 15.0368L18.0416 16.8489C18.578 17.4623 19.5882 17.083 19.5882 16.2681V13.8226C19.5882 13.5622 19.7033 13.315 19.9027 13.1473L21.6182 11.7047C22.2484 11.1748 21.8736 10.1471 21.0503 10.1471H18.7627C18.5029 10.1471 18.2562 10.0325 18.0886 9.83398L16.5565 8.01969Z"
              fill="#D9D9D9"
            />
            <path
              d="M16.5565 8.01969C16.0259 7.39135 15 7.76656 15 8.58897V10.8912C15 11.1471 14.8889 11.3904 14.6955 11.558L12.8169 13.1862C12.1996 13.7211 12.578 14.7353 13.3948 14.7353H15.793C16.0475 14.7353 16.2897 14.8452 16.4572 15.0368L18.0416 16.8489C18.578 17.4623 19.5882 17.083 19.5882 16.2681V13.8226C19.5882 13.5622 19.7033 13.315 19.9027 13.1473L21.6182 11.7047C22.2484 11.1748 21.8736 10.1471 21.0503 10.1471H18.7627C18.5029 10.1471 18.2562 10.0325 18.0886 9.83398L16.5565 8.01969Z"
              fill="url(#paint1_linear_1_10)"
            />
          </g>
          <g style={starSmallStyle}>
            <path
              d="M22.7156 2.38623C22.4615 2.04639 21.921 2.22609 21.921 2.6504V4.76971C21.921 4.90881 21.8554 5.03976 21.744 5.12306L20.0333 6.40209C19.6935 6.65616 19.8731 7.1966 20.2975 7.1966H22.4168C22.5559 7.1966 22.6868 7.2622 22.7701 7.37361L24.0491 9.08436C24.3032 9.4242 24.8437 9.2445 24.8437 8.82019V6.70087C24.8437 6.56178 24.9093 6.43082 25.0207 6.34753L26.7314 5.0685C27.0713 4.81443 26.8916 4.27398 26.4672 4.27398H24.3479C24.2088 4.27398 24.0779 4.20838 23.9946 4.09698L22.7156 2.38623Z"
              fill="#D9D9D9"
            />
            <path
              d="M22.7156 2.38623C22.4615 2.04639 21.921 2.22609 21.921 2.6504V4.76971C21.921 4.90881 21.8554 5.03976 21.744 5.12306L20.0333 6.40209C19.6935 6.65616 19.8731 7.1966 20.2975 7.1966H22.4168C22.5559 7.1966 22.6868 7.2622 22.7701 7.37361L24.0491 9.08436C24.3032 9.4242 24.8437 9.2445 24.8437 8.82019V6.70087C24.8437 6.56178 24.9093 6.43082 25.0207 6.34753L26.7314 5.0685C27.0713 4.81443 26.8916 4.27398 26.4672 4.27398H24.3479C24.2088 4.27398 24.0779 4.20838 23.9946 4.09698L22.7156 2.38623Z"
              fill="url(#paint2_linear_1_10)"
            />
          </g>
          <defs>
            <linearGradient
              id="paint0_linear_1_10"
              x1="8.53583"
              y1="23.3826"
              x2="31.7857"
              y2="4.00575"
              gradientUnits="userSpaceOnUse"
            >
              <animateTransform
                attributeName="gradientTransform"
                type="rotate"
                values="0 20.16 13.91;360 20.16 13.91"
                dur="4s"
                repeatCount="indefinite"
              />
              <stop stopColor="#5AC8FA" />
              <stop offset="1" stopColor="#2F84D3" />
            </linearGradient>
            <linearGradient
              id="paint1_linear_1_10"
              x1="7.91634"
              y1="4.74252"
              x2="8.02229"
              y2="26.9972"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#5AC8FA" />
              <stop offset="1" stopColor="#2F84D3" />
            </linearGradient>
            <linearGradient
              id="paint2_linear_1_10"
              x1="20.7353"
              y1="2.64707"
              x2="20.7878"
              y2="11.0812"
              gradientUnits="userSpaceOnUse"
            >
              <stop stopColor="#5AC8FA" />
              <stop offset="1" stopColor="#2F84D3" />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </>
  );
}
