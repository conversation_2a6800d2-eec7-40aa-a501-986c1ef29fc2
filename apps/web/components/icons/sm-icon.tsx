import React from 'react';
import { cn } from '@repo/ui/lib/utils';
import Image from 'next/image';

// Union type based on filenames in public/assets/icons/sm-icons
export type SmIconName =
  | 'support'
  | 'share_bonus'
  | 'subscription'
  | 'settings'
  | 'referral'
  | 'logout'
  | 'positions'
  | 'documentation'
  | 'headphones'
  | 'calendar'
  | 'channel'
  | 'chat'
  | 'bonus_reward';

interface SmIconProps {
  name: SmIconName;
  size?: number;
  className?: string;
  alt?: string;
  priority?: boolean;
  quality?: number;
  loading?: 'lazy' | 'eager';
  placeholder?: 'blur' | 'empty';
  style?: React.CSSProperties;
  onLoad?: () => void;
  onError?: () => void;
}

export function SmIcon({
  name,
  size = 20, // Default size, can be overridden
  className,
  ...props
}: SmIconProps) {
  const src = `/assets/icons/sm-icons/${name}.svg`;

  return (
    <Image
      src={src}
      alt={`${name} icon`}
      className={cn(className)}
      width={size}
      height={size}
      {...props}
    />
  );
}

SmIcon.displayName = 'SmIcon';
