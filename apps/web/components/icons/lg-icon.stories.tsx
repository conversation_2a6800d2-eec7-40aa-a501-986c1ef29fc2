import type { Meta, StoryObj } from '@storybook/react';
import { LgIcon, LgIconName } from './lg-icon';

const lgIconNames: LgIconName[] = [
  'market',
  'ranks',
  'referral',
  'category',
  'channel',
  'insight',
  'keyword',
  'activity',
  'bonus_reward',
];

const meta: Meta<typeof LgIcon> = {
  title: 'Common/Icons/LargeIcon',
  component: LgIcon,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    name: {
      control: { type: 'select' },
      options: lgIconNames,
      description: 'Name of the icon to display',
    },
    size: {
      control: { type: 'number' },
      description: 'Size of the icon in pixels',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
    // Add descriptions for other props if needed
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Story to display all available icons
export const AllIcons: Story = {
  render: args => (
    <div
      style={{
        display: 'flex',
        gap: '1rem',
        flexWrap: 'wrap',
        justifyContent: 'center',
      }}
    >
      {lgIconNames.map(name => (
        <div key={name} style={{ textAlign: 'center' }}>
          <LgIcon {...args} name={name} />
          <p style={{ marginTop: '0.5rem', fontSize: '0.8rem' }}>{name}</p>
        </div>
      ))}
    </div>
  ),
  args: {
    size: 32,
  },
};

// Base story
export const Default: Story = {
  args: {
    name: 'market', // Default icon
    size: 24,
  },
};

// Individual stories can be added here if needed
export const RanksLarge: Story = {
  args: {
    name: 'ranks',
    size: 64,
  },
};

export const ReferralStyled: Story = {
  args: {
    name: 'referral',
    size: 48,
    className: 'text-green-600',
  },
};
