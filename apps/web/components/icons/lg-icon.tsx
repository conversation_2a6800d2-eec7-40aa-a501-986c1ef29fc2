import React from 'react';
import { cn } from '@repo/ui/lib/utils';
import Image from 'next/image';

// Union type based on filenames in public/assets/icons/lg-icons
export type LgIconName =
  | 'market'
  | 'ranks'
  | 'referral'
  | 'category'
  | 'channel'
  | 'insight'
  | 'keyword'
  | 'activity'
  | 'bonus_reward'
  | 'weekly_quiz'
  | 'expert_lounge'
  | 'documentation'
  | 'share_bonus';

interface LgIconProps {
  name: LgIconName;
  size?: number;
  className?: string;
  alt?: string;
  priority?: boolean;
  quality?: number;
  loading?: 'lazy' | 'eager';
  placeholder?: 'blur' | 'empty';
  style?: React.CSSProperties;
  onLoad?: () => void;
  onError?: () => void;
}

export function LgIcon({
  name,
  size = 20, // Default size, can be overridden
  className,
  ...props
}: LgIconProps) {
  const src = `/assets/icons/lg-icons/${name}.svg`;

  return (
    <Image
      src={src}
      alt={`${name} icon`}
      className={cn(className)}
      width={size}
      height={size}
      {...props}
    />
  );
}

LgIcon.displayName = 'LgIcon';
