import React from 'react';
import { cn } from '@repo/ui/lib/utils';
import Image from 'next/image';

// Union type based on filenames in public/assets/icons/xs-icons
export type XsIconName =
  | 'bomb'
  | 'calendar'
  | 'copy'
  | 'crown'
  | 'delete'
  | 'edit'
  | 'excalmation'
  | 'external_link'
  | 'heart'
  | 'market'
  | 'member'
  | 'pin'
  | 'qr_code'
  | 'reply'
  | 'report'
  | 'shaer'
  | 'subscripbers'
  | 'time-1'
  | 'time'
  | 'unheart'
  | 'unpin'
  | 'view'
  | 'volum'
  | 'volum2';

interface XsIconProps {
  name: XsIconName;
  size?: number;
  className?: string;
  alt?: string;
  priority?: boolean;
  quality?: number;
  loading?: 'lazy' | 'eager';
  placeholder?: 'blur' | 'empty';
  style?: React.CSSProperties;
  onLoad?: () => void;
  onError?: () => void;
}

export function XsIcon({
  name,
  size = 16, // Default size, can be overridden (typically smaller for 'xs')
  className,
  ...props
}: XsIconProps) {
  const src = `/assets/icons/xs-icons/${name}.svg`;

  return (
    <Image
      src={src}
      alt={`${name} icon`}
      className={cn(className)}
      width={size}
      height={size}
      {...props}
    />
  );
}

XsIcon.displayName = 'XsIcon';
