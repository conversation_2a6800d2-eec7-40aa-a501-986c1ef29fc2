import type { Meta, StoryObj } from '@storybook/react';
import SvgIcon from './svg-icon';
import * as Icons from './assets';

const meta = {
  title: 'Components/Icons/SvgIcon',
  component: SvgIcon,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
  argTypes: {
    name: {
      description: 'Icon name to display',
      control: 'select',
      options: Object.keys(Icons),
    },
    className: {
      description: 'Additional CSS classes',
      control: 'text',
    },
  },
} satisfies Meta<typeof SvgIcon>;

export default meta;

type Story = StoryObj<typeof SvgIcon>;

// 기본 아이콘 예시
export const Default: Story = {
  args: {
    name: 'TimeIcon',
  },
};

// 모든 아이콘을 보여주는 그리드
export const AllIcons: Story = {
  render: () => {
    // 모든 아이콘 이름을 배열로 가져옴
    const iconNames = Object.keys(Icons) as Array<keyof typeof Icons>;

    return (
      <div className="p-4">
        <h1 className="mb-6 text-2xl font-bold">All Icons ({iconNames.length})</h1>
        <div className="grid grid-cols-2 gap-4 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6">
          {iconNames.map(iconName => (
            <div
              key={iconName as string}
              className="flex flex-col items-center justify-center rounded-md border p-4 transition-colors hover:bg-gray-50"
            >
              <SvgIcon className="size-[48px]" name={iconName} />
              <span className="text-center text-xs break-all">{iconName as string}</span>
            </div>
          ))}
        </div>
      </div>
    );
  },
};
