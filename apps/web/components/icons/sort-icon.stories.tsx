import type { Meta, StoryObj } from '@storybook/react';
import { SortIcon, SortIconName } from './sort-icon';

const sortIconNames: SortIconName[] = [
  'volume',
  'trending',
  'newest',
  'liquidity',
  'ending_soon',
  'competitive',
];

const meta: Meta<typeof SortIcon> = {
  title: 'Common/Icons/SortIcon',
  component: SortIcon,
  parameters: {
    layout: 'centered',
  },
  argTypes: {
    name: {
      control: { type: 'select' },
      options: sortIconNames,
      description: 'Name of the icon to display',
    },
    size: {
      control: { type: 'number' },
      description: 'Size of the icon in pixels',
    },
    className: {
      control: 'text',
      description: 'Additional CSS classes',
    },
  },
};

export default meta;
type Story = StoryObj<typeof meta>;

// Story to display all available icons
export const AllIcons: Story = {
  render: args => (
    <div
      style={{
        display: 'flex',
        gap: '1rem',
        flexWrap: 'wrap',
        justifyContent: 'center',
      }}
    >
      {sortIconNames.map(name => (
        <div key={name} style={{ textAlign: 'center' }}>
          <SortIcon {...args} name={name} />
          <p style={{ marginTop: '0.5rem', fontSize: '0.8rem' }}>{name}</p>
        </div>
      ))}
    </div>
  ),
  args: {
    size: 32,
  },
};

// Base story
export const Default: Story = {
  args: {
    name: 'trending', // Default icon
    size: 24,
  },
};

// Individual stories
export const VolumeLarge: Story = {
  args: {
    name: 'volume',
    size: 64,
  },
};

export const NewestStyled: Story = {
  args: {
    name: 'newest',
    size: 48,
    className: 'text-purple-600',
  },
};
