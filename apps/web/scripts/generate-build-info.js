#!/usr/bin/env node

import { writeFileSync } from 'fs';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Git 정보를 가져오는 함수
const getGitInfo = () => {
  try {
    const gitCommit = execSync('git rev-parse HEAD', { encoding: 'utf-8' }).trim();
    const gitBranch = execSync('git rev-parse --abbrev-ref HEAD', { encoding: 'utf-8' }).trim();
    return { gitCommit, gitBranch };
  } catch (error) {
    console.warn('⚠️  Failed to get git info:', error.message);
    return {
      gitCommit: process.env.CI_COMMIT_SHA || process.env.GITHUB_SHA || 'unknown',
      gitBranch: process.env.CI_COMMIT_REF_NAME || process.env.GITHUB_REF_NAME || 'unknown',
    };
  }
};

const { gitCommit, gitBranch } = getGitInfo();

const buildInfo = {
  buildTime: new Date().toISOString(),
  buildTimestamp: Date.now(),
  buildDate: new Date().toLocaleDateString('ko-KR', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: false,
  }),
  version: process.env.npm_package_version || '0.1.0',
  environment: process.env.NEXT_PUBLIC_ENV || 'development',
  gitCommit,
  gitBranch,
};

const outputPath = join(__dirname, '../public/build-info.json');

try {
  writeFileSync(outputPath, JSON.stringify(buildInfo, null, 2));
  console.log('✅ Build info generated successfully');
  console.log(`📅 Build time: ${buildInfo.buildDate}`);
  console.log(`📍 Environment: ${buildInfo.environment}`);
  console.log(`🌿 Branch: ${buildInfo.gitBranch}`);
  console.log(`🔧 Commit: ${buildInfo.gitCommit.substring(0, 7)}`);
} catch (error) {
  console.error('❌ Failed to generate build info:', error);
  process.exit(1);
}
