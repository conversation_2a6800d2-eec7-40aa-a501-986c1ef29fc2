# Transformer Layer (BLACK_BOX) Design Plan

```mermaid
graph LR
    A[API Response] --> B[Transformer Layer] --> C[Component Props]
    B --> D[Type Safety]
    B --> E[Data Validation]
    B --> F[Format Conversion]
```

## 1. Current API Structure Analysis

Based on the existing codebase:

- **BaseApi/DataApi**: Standardized API client with ky instance
- **Domain-based folders**: Each API domain has `.service.ts`, `.dto.ts`, `.routes.ts`
- **Zod validation**: Strong type safety with schema validation
- **Error handling**: ApiError class with proper error formatting

## 2. BLACK_BOX Architecture

### 2.1 Directory Structure

```
lib/
├── api/
│   ├── market/
│   │   ├── market.service.ts     # API calls
│   │   ├── market.dto.ts         # API types
│   │   └── market.routes.ts      # API routes
│   └── transformers/             # NEW: Transformer layer
│       ├── base/
│       │   ├── base.transformer.ts
│       │   └── transformer.types.ts
│       ├── market/
│       │   └── market.transformer.ts
│       └── index.ts              # Export all transformers
```

### 2.2 Base Transformer Pattern

```typescript
// lib/api/transformers/base/base.transformer.ts
export abstract class BaseTransformer<TInput, TOutput> {
  abstract transform(input: TInput): TOutput;
  
  protected safeTransform<T>(
    data: unknown, 
    transformer: (data: T) => TOutput
  ): TOutput {
    try {
      return transformer(data as T);
    } catch (error) {
      throw new TransformError('Transform failed', error);
    }
  }
}

// lib/api/transformers/base/transformer.types.ts
export interface TransformResult<T> {
  success: true;
  data: T;
} | {
  success: false;
  error: string;
  fallback?: T;
}
```

### 2.3 Market Transformer Implementation

```typescript
// lib/api/transformers/market/market.transformer.ts
export class MarketTransformer extends BaseTransformer<Market, PredictionMarketCardProps> {
  
  transform(market: Market): PredictionMarketCardProps {
    return this.safeTransform(market, (data) => ({
      title: data.title,
      volume: this.formatVolume(data.totalVolume),
      participants: data.participantCount,
      username: data.creatorName,
      status: this.mapStatus(data.status),
      endDate: data.endDate,
      marketAvatarImageUrl: data.imageUrl,
      options: this.transformOutcomes(data.outcomes),
    }));
  }

  transformBatch(markets: MarketBrief[]): PredictionMarketCardProps[] {
    return markets.map(market => this.transform(market));
  }

  private formatVolume(volume: number): string { /* ... */ }
  private mapStatus(status: string): ComponentStatus { /* ... */ }
  private transformOutcomes(outcomes: string[]): PredictionOption[] { /* ... */ }
}
```

## 3. Integration Patterns

### 3.1 Service Layer Integration

```typescript
// lib/api/market/market.service.ts (Enhanced)
export class MarketService extends BaseApi {
  private transformer = new MarketTransformer();

  // Raw API method (existing)
  async getMarkets(options?: GetMarketsOptions) {
    const response = await this.fetcher.get(MARKET_ROUTES.GET['/'], { searchParams }).json();
    return MarketBriefInfosResponseSchema.parse(response);
  }

  // Transformed method (NEW)
  async getMarketsForCards(options?: GetMarketsOptions): Promise<PredictionMarketCardProps[]> {
    const response = await this.getMarkets(options);
    return this.transformer.transformBatch(response.markets);
  }
}
```

### 3.2 Hook Integration Pattern

```typescript
// hooks/use-market-cards.ts
export function useMarketCards(options?: UseMarketCardsOptions) {
  const marketService = new MarketService();
  
  return useQuery({
    queryKey: ['market-cards', options],
    queryFn: () => marketService.getMarketsForCards(options),
    // Direct component-ready data
  });
}
```

### 3.3 Component Usage

```typescript
// components/market-list.tsx
export default function MarketList() {
  const { data: marketCards, isLoading } = useMarketCards();
  
  if (isLoading) return <div>Loading...</div>;
  
  return (
    <div>
      {marketCards?.map((props, index) => (
        <PredictionMarketCard key={index} {...props} />
      ))}
    </div>
  );
}
```

## 4. Advanced Features

### 4.1 Configurable Transformations

```typescript
interface TransformConfig {
  volumeFormat: 'short' | 'full';
  locale: string;
  dateFormat: string;
}

const transformer = new MarketTransformer(config);
```

### 4.2 Validation & Error Handling

```typescript
export class MarketTransformer {
  transform(market: Market): PredictionMarketCardProps {
    // Validate input
    const validated = MarketSchema.parse(market);
    
    // Transform with error boundary
    return this.safeTransform(validated, this.doTransform);
  }
}
```

### 4.3 Caching & Performance

```typescript
export class MarketTransformer {
  private cache = new Map<string, PredictionMarketCardProps>();
  
  transform(market: Market): PredictionMarketCardProps {
    const cacheKey = `${market.id}-${market.updatedAt}`;
    
    if (this.cache.has(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }
    
    const result = this.doTransform(market);
    this.cache.set(cacheKey, result);
    return result;
  }
}
```

## 5. Implementation Steps

1. **Create base transformer infrastructure**
   - BaseTransformer class
   - TransformError class
   - Common utility functions

2. **Implement MarketTransformer**
   - Basic transformation logic
   - Status mapping
   - Volume formatting
   - Options conversion

3. **Enhance MarketService**
   - Add transformed methods
   - Maintain backward compatibility

4. **Create custom hooks**
   - useMarketCards
   - Integrate with React Query

5. **Update components**
   - Use transformed data directly
   - Remove transformation logic from components

## 6. Benefits

- **Type Safety**: End-to-end TypeScript support
- **Reusability**: Same transformer across different components
- **Maintainability**: Centralized transformation logic
- **Performance**: Optional caching and memoization
- **Testability**: Isolated transformation logic
- **Consistency**: Standardized data formatting

## 7. Testing Strategy

```typescript
describe('MarketTransformer', () => {
  it('should transform market to card props', () => {
    const transformer = new MarketTransformer();
    const mockMarket = createMockMarket();
    
    const result = transformer.transform(mockMarket);
    
    expect(result).toMatchObject({
      title: mockMarket.title,
      volume: expect.any(String),
      // ...
    });
  });
});
