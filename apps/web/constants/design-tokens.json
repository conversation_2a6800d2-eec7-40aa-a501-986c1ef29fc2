{"font": {"text-base": {"16_sb_inter": {"description": "16xSB_inter title", "type": "custom-fontStyle", "value": {"fontSize": 16, "textDecoration": "none", "fontFamily": "Inter", "fontWeight": 700, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 24, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:5a816099c52368283fa72a64dadf3733093bcbc0,", "exportKey": "font"}}}}, "text-sm": {"14_sb_inter": {"description": "14xSB_Inter", "type": "custom-fontStyle", "value": {"fontSize": 14, "textDecoration": "none", "fontFamily": "Inter", "fontWeight": 600, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 20, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:e3fec1cf1df1ce25b8d187abd02bd66ef1fe00fc,", "exportKey": "font"}}}}, "text-xs": {"12_r_inter": {"description": "12x_R_Inter", "type": "custom-fontStyle", "value": {"fontSize": 12, "textDecoration": "none", "fontFamily": "Inter", "fontWeight": 400, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 16, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:bcd82ee1ada93fc925c2b50436410f3157daa7f2,", "exportKey": "font"}}}, "12_sb_inter": {"description": "12_sb_inter", "type": "custom-fontStyle", "value": {"fontSize": 12, "textDecoration": "none", "fontFamily": "Inter", "fontWeight": 600, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 16, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:51d6579d130bafc2a1e1f409807a101df4616f3f,", "exportKey": "font"}}}, "12_md_inter": {"type": "custom-fontStyle", "value": {"fontSize": 12, "textDecoration": "none", "fontFamily": "Inter", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 16, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:6bb8040701514c1679c6b8e69b9306cf3aa269cd,", "exportKey": "font"}}}}, "text-xl": {"20_b_inter": {"description": "Xl_20", "type": "custom-fontStyle", "value": {"fontSize": 20, "textDecoration": "none", "fontFamily": "Inter", "fontWeight": 700, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 28, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:7f7da5e76b3d9a52fc836362e40609795e7f4e85,", "exportKey": "font"}}}}, "text-15px": {"15_sb_inter_0.9375": {"description": "15xTitle_SB_Inter", "type": "custom-fontStyle", "value": {"fontSize": 15, "textDecoration": "none", "fontFamily": "Inter", "fontWeight": 600, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 24, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:6a634932b47ebaf72089520a566c25033a66ce24,", "exportKey": "font"}}}}, "text-13px": {"13_sb_inter_0.8125": {"description": "13 x SB_inter", "type": "custom-fontStyle", "value": {"fontSize": 13, "textDecoration": "none", "fontFamily": "Inter", "fontWeight": 600, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 16, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:38222981be08e7bc3236abcbe00b45a93a2da6da,", "exportKey": "font"}}}, "13_md_inter_0.8125": {"description": "13xMD_inter", "type": "custom-fontStyle", "value": {"fontSize": 13, "textDecoration": "none", "fontFamily": "Inter", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 20, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:f7634b29dd8d67313103b4b728dd44ca7f887568,", "exportKey": "font"}}}}, "text-11px": {"11_sb_inter_0.6875": {"description": "11 x SB_inter", "type": "custom-fontStyle", "value": {"fontSize": 11, "textDecoration": "none", "fontFamily": "Inter", "fontWeight": 600, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 14, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:12654833ab7422c5a0c4d143d4dcb68f98f3c275,", "exportKey": "font"}}}, "11_m_inter_6875": {"description": "11 x m_inter", "type": "custom-fontStyle", "value": {"fontSize": 11, "textDecoration": "none", "fontFamily": "Inter", "fontWeight": 500, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 14, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:37c79c23016575a5253970554e31692067dcfa1c,", "exportKey": "font"}}}}, "text-10px": {"10_sb_inter_0.625": {"description": "10x_SB_Inter", "type": "custom-fontStyle", "value": {"fontSize": 10, "textDecoration": "none", "fontFamily": "Inter", "fontWeight": 600, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 12, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:0d905a23379cfdc9f49ec0ce333166b3818fc387,", "exportKey": "font"}}}}, "text-8px": {"8_sb_inter_0.5": {"description": "xss_8_sm", "type": "custom-fontStyle", "value": {"fontSize": 8, "textDecoration": "none", "fontFamily": "Inter", "fontWeight": 600, "fontStyle": "normal", "fontStretch": "normal", "letterSpacing": 0, "lineHeight": 9, "paragraphIndent": 0, "paragraphSpacing": 0, "textCase": "none"}, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"styleId": "S:564a17bcb87155727046c0ef45f83fae41ce2a05,", "exportKey": "font"}}}}}, "01_yistyle_css": {"color": {"point-hbright": {"type": "color", "value": "#aadc36ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:79:2502", "exportKey": "variables"}}}, "graph-no-point-3": {"type": "color", "value": "#f76566ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:79:2503", "exportKey": "variables"}}}, "point-3": {"type": "color", "value": "#5ac8faff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:85:2748", "exportKey": "variables"}}}, "icon-gray": {"type": "color", "value": "#c1c1c1ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:86:2749", "exportKey": "variables"}}}, "icon-dark": {"type": "color", "value": "#3b424bff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:86:3767", "exportKey": "variables"}}}, "white": {"type": "color", "value": "#ffffffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:120:14248", "exportKey": "variables"}}}, "gray-1": {"type": "color", "value": "#ecececff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:120:14330", "exportKey": "variables"}}}, "graph-yes-point-2": {"type": "color", "value": "#8dc016ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:120:14331", "exportKey": "variables"}}}, "mid-dark": {"type": "color", "value": "#23252bff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:157:5388", "exportKey": "variables"}}}, "dark-deep": {"type": "color", "value": "#1d1d1dff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:163:5446", "exportKey": "variables"}}}, "dark-blue": {"type": "color", "value": "#2e343bff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:163:7243", "exportKey": "variables"}}}, "gray-3": {"description": "", "type": "color", "value": "{primitives.color.grey}", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:173:5563", "exportKey": "variables"}}}, "line": {"type": "color", "value": "#eaeaeaff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:185:13102", "exportKey": "variables"}}}, "gray-2": {"type": "color", "value": "#f9f9f9ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:191:10767", "exportKey": "variables"}}}, "graph-1": {"type": "color", "value": "#f19595ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:539:24598", "exportKey": "variables"}}}, "graph-2": {"type": "color", "value": "#f1c695ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:539:24599", "exportKey": "variables"}}}, "graph-3": {"type": "color", "value": "#f1eb8dff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:539:24600", "exportKey": "variables"}}}, "graph-4": {"type": "color", "value": "#c9f18dff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:539:24601", "exportKey": "variables"}}}, "graph-5": {"type": "color", "value": "#a9e9a3ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:539:24602", "exportKey": "variables"}}}, "graph-6": {"type": "color", "value": "#a3e9dfff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:539:24603", "exportKey": "variables"}}}, "graph-7": {"type": "color", "value": "#b5d0f1ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:539:24604", "exportKey": "variables"}}}, "graph-8": {"type": "color", "value": "#bcb5f1ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:539:24605", "exportKey": "variables"}}}, "graph-9": {"type": "color", "value": "#ebc2efff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:539:24606", "exportKey": "variables"}}}, "graph-11": {"type": "color", "value": "#dad2d5ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:539:24607", "exportKey": "variables"}}}, "graph-10": {"type": "color", "value": "#ffc3d7ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:680:57143", "exportKey": "variables"}}}}, "spacing": {"4": {"type": "dimension", "value": 4, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:182:5525", "exportKey": "variables"}}}, "8": {"type": "dimension", "value": 8, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:182:5535", "exportKey": "variables"}}}, "9": {"type": "dimension", "value": 9, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:182:5534", "exportKey": "variables"}}}, "12": {"type": "dimension", "value": 12, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:182:5536", "exportKey": "variables"}}}, "14": {"type": "dimension", "value": 14, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:182:5532", "exportKey": "variables"}}}, "16": {"type": "dimension", "value": 16, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:182:5528", "exportKey": "variables"}}}, "20": {"type": "dimension", "value": 20, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:182:5533", "exportKey": "variables"}}}, "21": {"type": "dimension", "value": 21, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:182:5527", "exportKey": "variables"}}}, "24": {"type": "dimension", "value": 24, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:182:5531", "exportKey": "variables"}}}, "28": {"type": "dimension", "value": 28, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:182:5526", "exportKey": "variables"}}}, "30": {"type": "dimension", "value": 30, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:182:5529", "exportKey": "variables"}}}, "37": {"type": "dimension", "value": 37, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:182:5530", "exportKey": "variables"}}}, "40": {"type": "dimension", "value": 40, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:182:5524", "exportKey": "variables"}}}}, "font": {"size": {"5xl": {"type": "dimension", "value": 48, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1126:40574", "exportKey": "variables"}}}, "3xl": {"type": "dimension", "value": 30, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1126:40575", "exportKey": "variables"}}}, "2xl": {"type": "dimension", "value": 24, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1126:40576", "exportKey": "variables"}}}, "xl": {"type": "dimension", "value": 20, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1126:40577", "exportKey": "variables"}}}, "4xl": {"type": "dimension", "value": 36, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1126:40580", "exportKey": "variables"}}}, "7xl": {"type": "dimension", "value": 72, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1126:40590", "exportKey": "variables"}}}, "lg": {"type": "dimension", "value": 18, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1126:40594", "exportKey": "variables"}}}, "base": {"type": "dimension", "value": 16, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1126:40597", "exportKey": "variables"}}}, "xs": {"type": "dimension", "value": 12, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1126:40601", "exportKey": "variables"}}}, "sm": {"type": "dimension", "value": 14, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1126:40602", "exportKey": "variables"}}}, "6xl": {"type": "dimension", "value": 60, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1126:40604", "exportKey": "variables"}}}, "8xl": {"type": "dimension", "value": 96, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1126:40608", "exportKey": "variables"}}}, "9xl": {"type": "dimension", "value": 128, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1126:40614", "exportKey": "variables"}}}, "xxs": {"type": "dimension", "value": 11, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1126:40618", "exportKey": "variables"}}}, "xxs10": {"type": "dimension", "value": 10, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1126:40655", "exportKey": "variables"}}}, "xxs8": {"type": "dimension", "value": 8, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1126:40821", "exportKey": "variables"}}}, "sm13": {"type": "dimension", "value": 13, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1126:40822", "exportKey": "variables"}}}, "base15": {"type": "dimension", "value": 15, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1126:40823", "exportKey": "variables"}}}}, "leading": {"3": {"type": "dimension", "value": 12, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["LINE_HEIGHT", "LETTER_SPACING", "PARAGRAPH_SPACING", "PARAGRAPH_INDENT"], "variableId": "VariableID:1126:40584", "exportKey": "variables"}}}, "4": {"type": "dimension", "value": 16, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["LINE_HEIGHT", "LETTER_SPACING", "PARAGRAPH_SPACING", "PARAGRAPH_INDENT"], "variableId": "VariableID:1126:40583", "exportKey": "variables"}}}, "5": {"type": "dimension", "value": 20, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["LINE_HEIGHT", "LETTER_SPACING", "PARAGRAPH_SPACING", "PARAGRAPH_INDENT"], "variableId": "VariableID:1126:40592", "exportKey": "variables"}}}, "6": {"type": "dimension", "value": 24, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["LINE_HEIGHT", "LETTER_SPACING", "PARAGRAPH_SPACING", "PARAGRAPH_INDENT"], "variableId": "VariableID:1126:40582", "exportKey": "variables"}}}, "7": {"type": "dimension", "value": 28, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["LINE_HEIGHT", "LETTER_SPACING", "PARAGRAPH_SPACING", "PARAGRAPH_INDENT"], "variableId": "VariableID:1126:40588", "exportKey": "variables"}}}, "8": {"type": "dimension", "value": 32, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["LINE_HEIGHT", "LETTER_SPACING", "PARAGRAPH_SPACING", "PARAGRAPH_INDENT"], "variableId": "VariableID:1126:40579", "exportKey": "variables"}}}, "9": {"type": "dimension", "value": 36, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["LINE_HEIGHT", "LETTER_SPACING", "PARAGRAPH_SPACING", "PARAGRAPH_INDENT"], "variableId": "VariableID:1126:40578", "exportKey": "variables"}}}, "10": {"type": "dimension", "value": 40, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["LINE_HEIGHT", "LETTER_SPACING", "PARAGRAPH_SPACING", "PARAGRAPH_INDENT"], "variableId": "VariableID:1126:40581", "exportKey": "variables"}}}}, "tracking": {"widest": {"type": "dimension", "value": 1.6, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["LETTER_SPACING"], "variableId": "VariableID:1126:40585", "exportKey": "variables"}}}, "wide": {"type": "dimension", "value": 0.4, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["LETTER_SPACING"], "variableId": "VariableID:1126:40586", "exportKey": "variables"}}}, "normal": {"type": "dimension", "value": 0, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["LETTER_SPACING"], "variableId": "VariableID:1126:40605", "exportKey": "variables"}}}, "tight": {"type": "dimension", "value": -0.4, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["LETTER_SPACING"], "variableId": "VariableID:1126:40606", "exportKey": "variables"}}}, "tighter": {"type": "dimension", "value": -0.8, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["LETTER_SPACING"], "variableId": "VariableID:1126:40607", "exportKey": "variables"}}}, "wider": {"type": "dimension", "value": 0.8, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["LETTER_SPACING"], "variableId": "VariableID:1126:40611", "exportKey": "variables"}}}}, "weight": {"black": {"type": "dimension", "value": 900, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:1126:40587", "exportKey": "variables"}}}, "extrabold": {"type": "dimension", "value": 800, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:1126:40589", "exportKey": "variables"}}}, "semibold": {"type": "dimension", "value": 600, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:1126:40591", "exportKey": "variables"}}}, "light": {"type": "dimension", "value": 300, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:1126:40593", "exportKey": "variables"}}}, "extralight": {"type": "dimension", "value": 200, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:1126:40595", "exportKey": "variables"}}}, "thin": {"type": "dimension", "value": 100, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:1126:40598", "exportKey": "variables"}}}, "bold": {"type": "dimension", "value": 700, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:1126:40610", "exportKey": "variables"}}}, "medium": {"type": "dimension", "value": 500, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:1126:40612", "exportKey": "variables"}}}, "normal": {"type": "dimension", "value": 400, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:1126:40613", "exportKey": "variables"}}}}, "family": {"serif": {"type": "string", "value": "Georgia", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_FAMILY"], "variableId": "VariableID:1126:40596", "exportKey": "variables"}}}, "mono": {"type": "string", "value": "<PERSON><PERSON>", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_FAMILY"], "variableId": "VariableID:1126:40599", "exportKey": "variables"}}}, "sans": {"type": "string", "value": "Inter", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_FAMILY"], "variableId": "VariableID:1126:40600", "exportKey": "variables"}}}}, "style": {"italic": {"type": "string", "value": "italic", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_STYLE"], "variableId": "VariableID:1126:40603", "exportKey": "variables"}}}, "not-italic": {"type": "string", "value": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "01_y<PERSON><PERSON>_<PERSON>S", "scopes": ["FONT_STYLE"], "variableId": "VariableID:1126:40609", "exportKey": "variables"}}}}}}, "tailwindcss": {"breakpoint": {"sm": {"type": "dimension", "value": 640, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:1128:24908", "exportKey": "variables"}}}, "md": {"type": "dimension", "value": 768, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:1128:24909", "exportKey": "variables"}}}, "lg": {"type": "dimension", "value": 1024, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:1128:24910", "exportKey": "variables"}}}, "xl": {"type": "dimension", "value": 1280, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:1128:24911", "exportKey": "variables"}}}, "2xl": {"type": "dimension", "value": 1536, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:1128:24912", "exportKey": "variables"}}}}, "radius": {"none": {"type": "dimension", "value": 0, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["CORNER_RADIUS"], "variableId": "VariableID:1128:24913", "exportKey": "variables"}}}, "xs": {"type": "dimension", "value": 2, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["CORNER_RADIUS"], "variableId": "VariableID:1128:24914", "exportKey": "variables"}}}, "sm": {"type": "dimension", "value": 4, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["CORNER_RADIUS"], "variableId": "VariableID:1128:24915", "exportKey": "variables"}}}, "md": {"type": "dimension", "value": 6, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["CORNER_RADIUS"], "variableId": "VariableID:1128:24916", "exportKey": "variables"}}}, "lg": {"type": "dimension", "value": 8, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["CORNER_RADIUS"], "variableId": "VariableID:1128:24917", "exportKey": "variables"}}}, "xl": {"type": "dimension", "value": 12, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["CORNER_RADIUS"], "variableId": "VariableID:1128:24918", "exportKey": "variables"}}}, "2xl": {"type": "dimension", "value": 16, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["CORNER_RADIUS"], "variableId": "VariableID:1128:24919", "exportKey": "variables"}}}, "3xl": {"type": "dimension", "value": 24, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["CORNER_RADIUS"], "variableId": "VariableID:1128:24920", "exportKey": "variables"}}}, "full": {"type": "dimension", "value": 9999, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["CORNER_RADIUS"], "variableId": "VariableID:1128:24921", "exportKey": "variables"}}}, "4xl": {"type": "dimension", "value": 32, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["CORNER_RADIUS"], "variableId": "VariableID:1128:25274", "exportKey": "variables"}}}}, "container": {"0": {"type": "dimension", "value": 0, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:1128:24922", "exportKey": "variables"}}}, "xs": {"type": "dimension", "value": 320, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:1128:24923", "exportKey": "variables"}}}, "sm": {"type": "dimension", "value": 384, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:1128:24924", "exportKey": "variables"}}}, "md": {"type": "dimension", "value": 448, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:1128:24925", "exportKey": "variables"}}}, "lg": {"type": "dimension", "value": 512, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:1128:24926", "exportKey": "variables"}}}, "xl": {"type": "dimension", "value": 576, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:1128:24927", "exportKey": "variables"}}}, "2xl": {"type": "dimension", "value": 672, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:1128:24928", "exportKey": "variables"}}}, "3xl": {"type": "dimension", "value": 768, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:1128:24929", "exportKey": "variables"}}}, "4xl": {"type": "dimension", "value": 896, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:1128:24930", "exportKey": "variables"}}}, "5xl": {"type": "dimension", "value": 1024, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:1128:24931", "exportKey": "variables"}}}, "6xl": {"type": "dimension", "value": 1152, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:1128:24932", "exportKey": "variables"}}}, "7xl": {"type": "dimension", "value": 1280, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:1128:24933", "exportKey": "variables"}}}, "2xs": {"type": "dimension", "value": 288, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:1128:25275", "exportKey": "variables"}}}, "3xs": {"type": "dimension", "value": 256, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT"], "variableId": "VariableID:1128:25276", "exportKey": "variables"}}}}, "opacity": {"0": {"type": "dimension", "value": 0, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:24934", "exportKey": "variables"}}}, "5": {"type": "dimension", "value": 5, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:24935", "exportKey": "variables"}}}, "10": {"type": "dimension", "value": 10, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:24936", "exportKey": "variables"}}}, "15": {"type": "dimension", "value": 15, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:25226", "exportKey": "variables"}}}, "20": {"type": "dimension", "value": 20, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:24937", "exportKey": "variables"}}}, "25": {"type": "dimension", "value": 25, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:24938", "exportKey": "variables"}}}, "30": {"type": "dimension", "value": 30, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:24939", "exportKey": "variables"}}}, "35": {"type": "dimension", "value": 35, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:25227", "exportKey": "variables"}}}, "40": {"type": "dimension", "value": 40, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:24940", "exportKey": "variables"}}}, "45": {"type": "dimension", "value": 45, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:25228", "exportKey": "variables"}}}, "50": {"type": "dimension", "value": 50, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:24941", "exportKey": "variables"}}}, "55": {"type": "dimension", "value": 55, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:25229", "exportKey": "variables"}}}, "60": {"type": "dimension", "value": 60, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:24942", "exportKey": "variables"}}}, "65": {"type": "dimension", "value": 65, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:25230", "exportKey": "variables"}}}, "70": {"type": "dimension", "value": 70, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:24943", "exportKey": "variables"}}}, "75": {"type": "dimension", "value": 75, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:24944", "exportKey": "variables"}}}, "80": {"type": "dimension", "value": 80, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:24945", "exportKey": "variables"}}}, "85": {"type": "dimension", "value": 85, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:25231", "exportKey": "variables"}}}, "90": {"type": "dimension", "value": 90, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:24946", "exportKey": "variables"}}}, "95": {"type": "dimension", "value": 95, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:24947", "exportKey": "variables"}}}, "100": {"type": "dimension", "value": 100, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:24948", "exportKey": "variables"}}}}, "spacing": {"0": {"type": "dimension", "value": 0, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24949", "exportKey": "variables"}}}, "1": {"type": "dimension", "value": 4, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24950", "exportKey": "variables"}}}, "2": {"type": "dimension", "value": 8, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24951", "exportKey": "variables"}}}, "3": {"type": "dimension", "value": 12, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24952", "exportKey": "variables"}}}, "4": {"type": "dimension", "value": 16, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24953", "exportKey": "variables"}}}, "5": {"type": "dimension", "value": 20, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24954", "exportKey": "variables"}}}, "6": {"type": "dimension", "value": 24, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24955", "exportKey": "variables"}}}, "7": {"type": "dimension", "value": 28, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24956", "exportKey": "variables"}}}, "8": {"type": "dimension", "value": 32, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24957", "exportKey": "variables"}}}, "9": {"type": "dimension", "value": 36, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24958", "exportKey": "variables"}}}, "10": {"type": "dimension", "value": 40, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24959", "exportKey": "variables"}}}, "11": {"type": "dimension", "value": 44, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24960", "exportKey": "variables"}}}, "12": {"type": "dimension", "value": 48, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24961", "exportKey": "variables"}}}, "14": {"type": "dimension", "value": 56, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24962", "exportKey": "variables"}}}, "16": {"type": "dimension", "value": 64, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24963", "exportKey": "variables"}}}, "20": {"type": "dimension", "value": 80, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24964", "exportKey": "variables"}}}, "24": {"type": "dimension", "value": 96, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24965", "exportKey": "variables"}}}, "28": {"type": "dimension", "value": 112, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24966", "exportKey": "variables"}}}, "32": {"type": "dimension", "value": 128, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24967", "exportKey": "variables"}}}, "36": {"type": "dimension", "value": 144, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24968", "exportKey": "variables"}}}, "40": {"type": "dimension", "value": 160, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24969", "exportKey": "variables"}}}, "44": {"type": "dimension", "value": 176, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24970", "exportKey": "variables"}}}, "48": {"type": "dimension", "value": 192, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24971", "exportKey": "variables"}}}, "52": {"type": "dimension", "value": 208, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24972", "exportKey": "variables"}}}, "56": {"type": "dimension", "value": 224, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24973", "exportKey": "variables"}}}, "60": {"type": "dimension", "value": 240, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24974", "exportKey": "variables"}}}, "64": {"type": "dimension", "value": 256, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24975", "exportKey": "variables"}}}, "72": {"type": "dimension", "value": 288, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24976", "exportKey": "variables"}}}, "80": {"type": "dimension", "value": 320, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24977", "exportKey": "variables"}}}, "96": {"type": "dimension", "value": 384, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24978", "exportKey": "variables"}}}, "px": {"type": "dimension", "value": 1, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24979", "exportKey": "variables"}}}, "0-5": {"type": "dimension", "value": 2, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24980", "exportKey": "variables"}}}, "1-5": {"type": "dimension", "value": 6, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24981", "exportKey": "variables"}}}, "2-5": {"type": "dimension", "value": 10, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24982", "exportKey": "variables"}}}, "3-5": {"type": "dimension", "value": 14, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["WIDTH_HEIGHT", "GAP"], "variableId": "VariableID:1128:24983", "exportKey": "variables"}}}}, "skew": {"0": {"type": "dimension", "value": 0, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": [], "variableId": "VariableID:1128:24984", "exportKey": "variables"}}}, "1": {"type": "dimension", "value": 1, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": [], "variableId": "VariableID:1128:24985", "exportKey": "variables"}}}, "2": {"type": "dimension", "value": 2, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": [], "variableId": "VariableID:1128:24986", "exportKey": "variables"}}}, "3": {"type": "dimension", "value": 3, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": [], "variableId": "VariableID:1128:24987", "exportKey": "variables"}}}, "6": {"type": "dimension", "value": 6, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": [], "variableId": "VariableID:1128:24988", "exportKey": "variables"}}}, "12": {"type": "dimension", "value": 12, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": [], "variableId": "VariableID:1128:24989", "exportKey": "variables"}}}}, "color": {"black": {"type": "color", "value": "#000000ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:24990", "exportKey": "variables"}}}, "white": {"type": "color", "value": "#ffffffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:24991", "exportKey": "variables"}}}, "slate": {"50": {"type": "color", "value": "#f8fafcff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:24992", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#f1f5f9ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:24993", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#e2e8f0ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:24994", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#cad5e2ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:24995", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#90a1b9ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:24996", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#62748eff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:24997", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#45556cff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:24998", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#314158ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:24999", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#1d293dff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25000", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#0f172bff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25001", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#020618ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25002", "exportKey": "variables"}}}}, "gray": {"50": {"type": "color", "value": "#f9fafbff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25003", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#f3f4f6ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25004", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#e5e7ebff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25005", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#d1d5dcff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25006", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#99a1afff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25007", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#6a7282ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25008", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#4a5565ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25009", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#364153ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25010", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#1e2939ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25011", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#101828ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25012", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#030712ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25013", "exportKey": "variables"}}}}, "zinc": {"50": {"type": "color", "value": "#fafafaff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25014", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#f4f4f5ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25015", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#e4e4e7ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25016", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#d4d4d8ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25017", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#9f9fa9ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25018", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#71717bff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25019", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#52525cff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25020", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#3f3f46ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25021", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#27272aff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25022", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#18181bff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25023", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#09090bff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25024", "exportKey": "variables"}}}}, "neutral": {"50": {"type": "color", "value": "#fafafaff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25025", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#f5f5f5ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25026", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#e5e5e5ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25027", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#d4d4d4ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25028", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#a1a1a1ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25029", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#737373ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25030", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#525252ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25031", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#404040ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25032", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#262626ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25033", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#171717ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25034", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#0a0a0aff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25035", "exportKey": "variables"}}}}, "stone": {"50": {"type": "color", "value": "#fafaf9ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25036", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#f5f5f4ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25037", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#e7e5e4ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25038", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#d6d3d1ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25039", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#a6a09bff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25040", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#79716bff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25041", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#57534dff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25042", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#44403bff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25043", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#292524ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25044", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#1c1917ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25045", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#0c0a09ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25046", "exportKey": "variables"}}}}, "red": {"50": {"type": "color", "value": "#fef2f2ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25047", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#ffe2e2ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25048", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#ffc9c9ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25049", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#ffa2a2ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25050", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#ff6467ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25051", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#fb2c36ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25052", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#e7000bff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25053", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#c10007ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25054", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#9f0712ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25055", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#82181aff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25056", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#460809ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25057", "exportKey": "variables"}}}}, "amber": {"50": {"type": "color", "value": "#fffbebff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25058", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#fef3c6ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25295", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#fee685ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25297", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#ffd230ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25289", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#ffba00ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25292", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#fd9a00ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25291", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#e17100ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25296", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#bb4d00ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25294", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#973c00ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25290", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#7b3306ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25288", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#461901ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25293", "exportKey": "variables"}}}}, "yellow": {"50": {"type": "color", "value": "#fefce8ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25059", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#fef9c2ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25060", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#fff085ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25061", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#ffdf20ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25062", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#fcc800ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25063", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#efb100ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25064", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#d08700ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25065", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#a65f00ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25066", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#894b00ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25067", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#733e0aff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25068", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#432004ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25069", "exportKey": "variables"}}}}, "lime": {"50": {"type": "color", "value": "#f7fee7ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25070", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#ecfccaff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25071", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#d8f999ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25072", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#bbf451ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25073", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#9ae600ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25074", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#7ccf00ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25075", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#5ea500ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25076", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#497d00ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25077", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#3d6300ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25078", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#35530eff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25079", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#192e03ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25080", "exportKey": "variables"}}}}, "green": {"50": {"type": "color", "value": "#f0fdf4ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25081", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#dcfce7ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25082", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#b9f8cfff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25083", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#7bf1a8ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25084", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#05df72ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25085", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#00c951ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25086", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#00a63eff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25087", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#008236ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25088", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#016630ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25089", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#0d542bff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25090", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#052e16ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25091", "exportKey": "variables"}}}}, "emerald": {"50": {"type": "color", "value": "#ecfdf5ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25092", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#d0fae5ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25093", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#a4f4cfff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25094", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#5ee9b5ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25095", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#00d492ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25096", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#00bc7dff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25097", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#009966ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25098", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#007a55ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25099", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#006045ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25100", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#004f3bff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25101", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#002c22ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25102", "exportKey": "variables"}}}}, "teal": {"50": {"type": "color", "value": "#f0fdfaff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25103", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#cbfbf1ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25104", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#96f7e4ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25105", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#46ecd5ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25106", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#00d5beff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25107", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#00bba7ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25108", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#009689ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25109", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#00786fff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25110", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#005f5aff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25111", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#0b4f4aff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25112", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#022f2eff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25113", "exportKey": "variables"}}}}, "cyan": {"50": {"type": "color", "value": "#ecfeffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25114", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#cefafeff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25115", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#a2f4fdff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25116", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#53eafdff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25117", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#00d3f2ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25118", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#00b8dbff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25119", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#0092b8ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25120", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#007595ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25121", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#005f78ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25122", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#104e64ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25123", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#053345ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25124", "exportKey": "variables"}}}}, "sky": {"50": {"type": "color", "value": "#f0f9ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25125", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#dff2feff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25126", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#b8e6feff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25127", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#74d4ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25128", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#00bcffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25129", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#00a6f4ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25130", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#0084d1ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25131", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#0069a8ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25132", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#00598aff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25133", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#024a70ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25134", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#052f4aff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25135", "exportKey": "variables"}}}}, "blue": {"50": {"type": "color", "value": "#eff6ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25136", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#dbeafeff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25137", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#bedbffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25138", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#8ec5ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25139", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#51a2ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25140", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#2b7fffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25141", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#155dfcff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25142", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#1447e6ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25143", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#193cb8ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25144", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#1c398eff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25145", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#162456ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25146", "exportKey": "variables"}}}}, "indigo": {"50": {"type": "color", "value": "#eef2ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25147", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#e0e7ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25148", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#c6d2ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25149", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#a3b3ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25150", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#7c86ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25151", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#615fffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25152", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#4f39f6ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25153", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#432dd7ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25154", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#372aa<PERSON>ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25155", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#312c85ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25156", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#1e1a4dff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25157", "exportKey": "variables"}}}}, "violet": {"50": {"type": "color", "value": "#f5f3ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25158", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#ede9feff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25159", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#ddd6ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25160", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#c4b4ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25161", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#a684ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25162", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#8e51ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25163", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#7f22<PERSON>ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25164", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#7008e7ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25165", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#5d0ec0ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25166", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#4d179aff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25167", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#2f0d68ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25168", "exportKey": "variables"}}}}, "purple": {"50": {"type": "color", "value": "#faf5ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25169", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#f3e8ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25170", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#e9d4ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25171", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#dab2ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25172", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#c27affff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25173", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#ad46ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25174", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#9810faff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25175", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#8200dbff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25176", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#6e11b0ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25177", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#59168bff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25178", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#3c0366ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25179", "exportKey": "variables"}}}}, "fuchsia": {"50": {"type": "color", "value": "#fdf4ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25180", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#fae8ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25181", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#f6cfffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25182", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#f4a8ffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25183", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#ed6bffff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25184", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#e12afbff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25185", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#c800deff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25186", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#a800b7ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25187", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#8a0194ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25188", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#721378ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25189", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#4b004fff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25190", "exportKey": "variables"}}}}, "pink": {"50": {"type": "color", "value": "#fdf2f8ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25191", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#fce7f3ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25192", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#fccee8ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25193", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#fda5d5ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25194", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#fb64b6ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25195", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#f6339aff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25196", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#e60076ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25197", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#c6005cff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25198", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#a3004cff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25199", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#861043ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25200", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#510424ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25201", "exportKey": "variables"}}}}, "rose": {"50": {"type": "color", "value": "#fff1f2ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25202", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#ffe4e6ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25203", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#ffccd3ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25204", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#ffa1adff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25205", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#ff637eff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25206", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#ff2056ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25207", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#ec003fff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25208", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#c70036ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25209", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#a50036ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25210", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#8b0836ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25211", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#4d0218ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25212", "exportKey": "variables"}}}}, "transparent": {"type": "color", "value": "#ffffff00", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25232", "exportKey": "variables"}}}, "orange": {"50": {"type": "color", "value": "#fff7edff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25287", "exportKey": "variables"}}}, "100": {"type": "color", "value": "#ffedd4ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25286", "exportKey": "variables"}}}, "200": {"type": "color", "value": "#ffd6a8ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25284", "exportKey": "variables"}}}, "300": {"type": "color", "value": "#ffb86aff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25283", "exportKey": "variables"}}}, "400": {"type": "color", "value": "#ff8904ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25281", "exportKey": "variables"}}}, "500": {"type": "color", "value": "#ff6900ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25280", "exportKey": "variables"}}}, "600": {"type": "color", "value": "#f54a00ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25279", "exportKey": "variables"}}}, "700": {"type": "color", "value": "#ca3500ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25282", "exportKey": "variables"}}}, "800": {"type": "color", "value": "#9f2d00ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25278", "exportKey": "variables"}}}, "900": {"type": "color", "value": "#7e2a0cff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25277", "exportKey": "variables"}}}, "950": {"type": "color", "value": "#441306ff", "blendMode": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25285", "exportKey": "variables"}}}}}, "border-width": {"0": {"type": "dimension", "value": 0, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["STROKE_FLOAT"], "variableId": "VariableID:1128:25213", "exportKey": "variables"}}}, "1": {"type": "dimension", "value": 1, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["STROKE_FLOAT"], "variableId": "VariableID:1128:25214", "exportKey": "variables"}}}, "2": {"type": "dimension", "value": 2, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["STROKE_FLOAT"], "variableId": "VariableID:1128:25215", "exportKey": "variables"}}}, "4": {"type": "dimension", "value": 4, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["STROKE_FLOAT"], "variableId": "VariableID:1128:25216", "exportKey": "variables"}}}, "8": {"type": "dimension", "value": 8, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["STROKE_FLOAT"], "variableId": "VariableID:1128:25217", "exportKey": "variables"}}}}, "blur": {"none": {"type": "dimension", "value": 0, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:25218", "exportKey": "variables"}}}, "xs": {"type": "dimension", "value": 4, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:25219", "exportKey": "variables"}}}, "sm": {"type": "dimension", "value": 8, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:25220", "exportKey": "variables"}}}, "md": {"type": "dimension", "value": 12, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:25221", "exportKey": "variables"}}}, "lg": {"type": "dimension", "value": 16, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:25222", "exportKey": "variables"}}}, "xl": {"type": "dimension", "value": 24, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:25223", "exportKey": "variables"}}}, "2xl": {"type": "dimension", "value": 40, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:25224", "exportKey": "variables"}}}, "3xl": {"type": "dimension", "value": 64, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["EFFECT_FLOAT", "OPACITY"], "variableId": "VariableID:1128:25225", "exportKey": "variables"}}}}, "font": {"family": {"sans": {"type": "string", "value": "Inter", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25233", "exportKey": "variables"}}}, "serif": {"type": "string", "value": "Georgia", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25234", "exportKey": "variables"}}}, "mono": {"type": "string", "value": "<PERSON><PERSON>", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25235", "exportKey": "variables"}}}}, "size": {"xs": {"type": "dimension", "value": 12, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1128:25236", "exportKey": "variables"}}}, "sm": {"type": "dimension", "value": 14, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1128:25237", "exportKey": "variables"}}}, "base": {"type": "dimension", "value": 16, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1128:25238", "exportKey": "variables"}}}, "lg": {"type": "dimension", "value": 18, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1128:25239", "exportKey": "variables"}}}, "xl": {"type": "dimension", "value": 20, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1128:25240", "exportKey": "variables"}}}, "2xl": {"type": "dimension", "value": 24, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1128:25241", "exportKey": "variables"}}}, "3xl": {"type": "dimension", "value": 30, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1128:25242", "exportKey": "variables"}}}, "4xl": {"type": "dimension", "value": 36, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1128:25243", "exportKey": "variables"}}}, "5xl": {"type": "dimension", "value": 48, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1128:25244", "exportKey": "variables"}}}, "6xl": {"type": "dimension", "value": 60, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1128:25245", "exportKey": "variables"}}}, "7xl": {"type": "dimension", "value": 72, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1128:25246", "exportKey": "variables"}}}, "8xl": {"type": "dimension", "value": 96, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1128:25247", "exportKey": "variables"}}}, "9xl": {"type": "dimension", "value": 128, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_SIZE"], "variableId": "VariableID:1128:25248", "exportKey": "variables"}}}}, "style": {"italic": {"type": "string", "value": "italic", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25249", "exportKey": "variables"}}}, "not-italic": {"type": "string", "value": "normal", "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["ALL_SCOPES"], "variableId": "VariableID:1128:25250", "exportKey": "variables"}}}}, "weight": {"thin": {"type": "dimension", "value": 100, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:1128:25251", "exportKey": "variables"}}}, "extralight": {"type": "dimension", "value": 200, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:1128:25252", "exportKey": "variables"}}}, "light": {"type": "dimension", "value": 300, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:1128:25253", "exportKey": "variables"}}}, "normal": {"type": "dimension", "value": 400, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:1128:25254", "exportKey": "variables"}}}, "medium": {"type": "dimension", "value": 500, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:1128:25255", "exportKey": "variables"}}}, "semibold": {"type": "dimension", "value": 600, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:1128:25256", "exportKey": "variables"}}}, "bold": {"type": "dimension", "value": 700, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:1128:25257", "exportKey": "variables"}}}, "extrabold": {"type": "dimension", "value": 800, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:1128:25258", "exportKey": "variables"}}}, "black": {"type": "dimension", "value": 900, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["FONT_WEIGHT"], "variableId": "VariableID:1128:25259", "exportKey": "variables"}}}}, "tracking": {"tighter": {"type": "dimension", "value": -0.8, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["LETTER_SPACING"], "variableId": "VariableID:1128:25260", "exportKey": "variables"}}}, "tight": {"type": "dimension", "value": -0.4, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["LETTER_SPACING"], "variableId": "VariableID:1128:25261", "exportKey": "variables"}}}, "normal": {"type": "dimension", "value": 0, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["LETTER_SPACING"], "variableId": "VariableID:1128:25262", "exportKey": "variables"}}}, "wide": {"type": "dimension", "value": 0.4, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["LETTER_SPACING"], "variableId": "VariableID:1128:25263", "exportKey": "variables"}}}, "wider": {"type": "dimension", "value": 0.8, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["LETTER_SPACING"], "variableId": "VariableID:1128:25264", "exportKey": "variables"}}}, "widest": {"type": "dimension", "value": 1.6, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["LETTER_SPACING"], "variableId": "VariableID:1128:25265", "exportKey": "variables"}}}}, "leading": {"3": {"type": "dimension", "value": 12, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["LINE_HEIGHT", "LETTER_SPACING", "PARAGRAPH_SPACING", "PARAGRAPH_INDENT"], "variableId": "VariableID:1128:25266", "exportKey": "variables"}}}, "4": {"type": "dimension", "value": 16, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["LINE_HEIGHT", "LETTER_SPACING", "PARAGRAPH_SPACING", "PARAGRAPH_INDENT"], "variableId": "VariableID:1128:25267", "exportKey": "variables"}}}, "5": {"type": "dimension", "value": 20, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["LINE_HEIGHT", "LETTER_SPACING", "PARAGRAPH_SPACING", "PARAGRAPH_INDENT"], "variableId": "VariableID:1128:25268", "exportKey": "variables"}}}, "6": {"type": "dimension", "value": 24, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["LINE_HEIGHT", "LETTER_SPACING", "PARAGRAPH_SPACING", "PARAGRAPH_INDENT"], "variableId": "VariableID:1128:25269", "exportKey": "variables"}}}, "7": {"type": "dimension", "value": 28, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["LINE_HEIGHT", "LETTER_SPACING", "PARAGRAPH_SPACING", "PARAGRAPH_INDENT"], "variableId": "VariableID:1128:25270", "exportKey": "variables"}}}, "8": {"type": "dimension", "value": 32, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["LINE_HEIGHT", "LETTER_SPACING", "PARAGRAPH_SPACING", "PARAGRAPH_INDENT"], "variableId": "VariableID:1128:25271", "exportKey": "variables"}}}, "9": {"type": "dimension", "value": 36, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["LINE_HEIGHT", "LETTER_SPACING", "PARAGRAPH_SPACING", "PARAGRAPH_INDENT"], "variableId": "VariableID:1128:25272", "exportKey": "variables"}}}, "10": {"type": "dimension", "value": 40, "extensions": {"org.lukasoppermann.figmaDesignTokens": {"collection": "TailwindCSS", "scopes": ["LINE_HEIGHT", "LETTER_SPACING", "PARAGRAPH_SPACING", "PARAGRAPH_INDENT"], "variableId": "VariableID:1128:25273", "exportKey": "variables"}}}}}}, "typography": {"text-base": {"16_sb_inter": {"description": "16xSB_inter title", "fontSize": {"type": "dimension", "value": 16}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Inter"}, "fontWeight": {"type": "number", "value": 700}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 24}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}, "text-sm": {"14_sb_inter": {"description": "14xSB_Inter", "fontSize": {"type": "dimension", "value": 14}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Inter"}, "fontWeight": {"type": "number", "value": 600}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 20}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}, "text-xs": {"12_r_inter": {"description": "12x_R_Inter", "fontSize": {"type": "dimension", "value": 12}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Inter"}, "fontWeight": {"type": "number", "value": 400}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 16}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "12_sb_inter": {"description": "12_sb_inter", "fontSize": {"type": "dimension", "value": 12}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Inter"}, "fontWeight": {"type": "number", "value": 600}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 16}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "12_md_inter": {"fontSize": {"type": "dimension", "value": 12}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Inter"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 16}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}, "text-xl": {"20_b_inter": {"description": "Xl_20", "fontSize": {"type": "dimension", "value": 20}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Inter"}, "fontWeight": {"type": "number", "value": 700}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 28}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}, "text-15px": {"15_sb_inter_0.9375": {"description": "15xTitle_SB_Inter", "fontSize": {"type": "dimension", "value": 15}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Inter"}, "fontWeight": {"type": "number", "value": 600}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 24}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}, "text-13px": {"13_sb_inter_0.8125": {"description": "13 x SB_inter", "fontSize": {"type": "dimension", "value": 13}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Inter"}, "fontWeight": {"type": "number", "value": 600}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 16}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "13_md_inter_0.8125": {"description": "13xMD_inter", "fontSize": {"type": "dimension", "value": 13}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Inter"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 20}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}, "text-11px": {"11_sb_inter_0.6875": {"description": "11 x SB_inter", "fontSize": {"type": "dimension", "value": 11}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Inter"}, "fontWeight": {"type": "number", "value": 600}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 14}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}, "11_m_inter_6875": {"description": "11 x m_inter", "fontSize": {"type": "dimension", "value": 11}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Inter"}, "fontWeight": {"type": "number", "value": 500}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 14}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}, "text-10px": {"10_sb_inter_0.625": {"description": "10x_SB_Inter", "fontSize": {"type": "dimension", "value": 10}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Inter"}, "fontWeight": {"type": "number", "value": 600}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 12}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}, "text-8px": {"8_sb_inter_0.5": {"description": "xss_8_sm", "fontSize": {"type": "dimension", "value": 8}, "textDecoration": {"type": "string", "value": "none"}, "fontFamily": {"type": "string", "value": "Inter"}, "fontWeight": {"type": "number", "value": 600}, "fontStyle": {"type": "string", "value": "normal"}, "fontStretch": {"type": "string", "value": "normal"}, "letterSpacing": {"type": "dimension", "value": 0}, "lineHeight": {"type": "dimension", "value": 9}, "paragraphIndent": {"type": "dimension", "value": 0}, "paragraphSpacing": {"type": "dimension", "value": 0}, "textCase": {"type": "string", "value": "none"}}}}}